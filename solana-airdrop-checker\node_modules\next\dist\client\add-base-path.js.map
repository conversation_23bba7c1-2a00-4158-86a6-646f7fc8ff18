{"version": 3, "sources": ["../../src/client/add-base-path.ts"], "sourcesContent": ["import { addPathPrefix } from '../shared/lib/router/utils/add-path-prefix'\nimport { normalizePathTrailingSlash } from './normalize-trailing-slash'\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nexport function addBasePath(path: string, required?: boolean): string {\n  return normalizePathTrailingSlash(\n    process.env.__NEXT_MANUAL_CLIENT_BASE_PATH && !required\n      ? path\n      : addPathPrefix(path, basePath)\n  )\n}\n"], "names": ["addBasePath", "basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "path", "required", "normalizePathTrailingSlash", "__NEXT_MANUAL_CLIENT_BASE_PATH", "addPathPrefix"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;+BALc;wCACa;AAE3C,MAAMC,WAAW,AAACC,QAAQC,GAAG,CAACC,sBAAsB,IAAe;AAE5D,SAASJ,YAAYK,IAAY,EAAEC,QAAkB;IAC1D,OAAOC,IAAAA,kDAA0B,EAC/BL,QAAQC,GAAG,CAACK,8BAA8B,IAAI,CAACF,WAC3CD,OACAI,IAAAA,4BAAa,EAACJ,MAAMJ;AAE5B"}