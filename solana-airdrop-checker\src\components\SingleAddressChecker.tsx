'use client';

import { useState } from 'react';
import { wctAirdropAPI, WCTAirdropResult } from '@/lib/solana-api';

export default function SingleAddressChecker() {
  const [address, setAddress] = useState('');
  const [result, setResult] = useState<WCTAirdropResult | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!address.trim()) {
      alert('Please enter a Solana address');
      return;
    }

    if (!wctAirdropAPI.isValidSolanaAddress(address.trim())) {
      alert('Please enter a valid Solana address');
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const result = await wctAirdropAPI.checkWCTEligibility(address.trim());
      setResult(result);
    } catch (error) {
      console.error('Error checking WCT eligibility:', error);
      setResult({
        address: address.trim(),
        balance: 0,
        isEligible: false,
        eligibleAmount: 0,
        error: 'Failed to check WCT eligibility'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setAddress('');
    setResult(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">WCT Airdrop Checker - Single Address</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-2">
            Solana Wallet Address
          </label>
          <input
            type="text"
            id="address"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            placeholder="Enter Solana wallet address (e.g., 54iFFejXJWnkydLqExz3tk8aTkaY1mYtdVtmhaxv44rw)"
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>
        
        <div className="flex gap-3">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? 'Checking...' : 'Check WCT Eligibility'}
          </button>
          
          <button
            type="button"
            onClick={handleClear}
            disabled={loading}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Clear
          </button>
        </div>
      </form>

      {result && (
        <div className="mt-6 p-4 border rounded-md">
          <h3 className="font-semibold text-lg mb-3">Result</h3>
          
          <div className="space-y-2">
            <div>
              <span className="font-medium">Address:</span>
              <span className="ml-2 font-mono text-sm break-all">{result.address}</span>
            </div>
            
            <div>
              <span className="font-medium">WCT Airdrop Status:</span>
              <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                result.isEligible
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'}
              </span>
            </div>

            {result.isEligible && (
              <div>
                <span className="font-medium">Eligible Amount:</span>
                <span className="ml-2 font-semibold text-green-600">
                  {result.eligibleAmount.toLocaleString()} WCT
                </span>
              </div>
            )}
            
            {result.error && (
              <div>
                <span className="font-medium text-red-600">Error:</span>
                <span className="ml-2 text-red-600">{result.error}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
