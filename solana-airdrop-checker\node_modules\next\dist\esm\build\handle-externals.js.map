{"version": 3, "sources": ["../../src/build/handle-externals.ts"], "sourcesContent": ["import type { WebpackLayerName } from '../lib/constants'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { ResolveOptions } from 'webpack'\nimport { defaultOverrides } from '../server/require-hook'\nimport { BARREL_OPTIMIZATION_PREFIX } from '../shared/lib/constants'\nimport path from '../shared/lib/isomorphic/path'\nimport {\n  NODE_BASE_ESM_RESOLVE_OPTIONS,\n  NODE_BASE_RESOLVE_OPTIONS,\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from './webpack-config'\nimport { isWebpackBundledLayer, isWebpackServerOnlyLayer } from './utils'\nimport { normalizePathSep } from '../shared/lib/page-path/normalize-path-sep'\nconst reactPackagesRegex = /^(react|react-dom|react-server-dom-webpack)($|\\/)/\n\nconst pathSeparators = '[/\\\\\\\\]'\nconst optionalEsmPart = `((${pathSeparators}esm)?${pathSeparators})`\nconst externalFileEnd = '(\\\\.external(\\\\.js)?)$'\nconst nextDist = `next${pathSeparators}dist`\n\nconst externalPattern = new RegExp(\n  `${nextDist}${optionalEsmPart}.*${externalFileEnd}`\n)\n\nconst nodeModulesRegex = /node_modules[/\\\\].*\\.[mc]?js$/\n\nexport function isResourceInPackages(\n  resource: string,\n  packageNames?: string[],\n  packageDirMapping?: Map<string, string>\n): boolean {\n  if (!packageNames) return false\n  return packageNames.some((p: string) =>\n    packageDirMapping && packageDirMapping.has(p)\n      ? resource.startsWith(packageDirMapping.get(p)! + path.sep)\n      : resource.includes(\n          path.sep +\n            path.join('node_modules', p.replace(/\\//g, path.sep)) +\n            path.sep\n        )\n  )\n}\n\nexport async function resolveExternal(\n  dir: string,\n  esmExternalsConfig: NextConfigComplete['experimental']['esmExternals'],\n  context: string,\n  request: string,\n  isEsmRequested: boolean,\n  getResolve: (\n    options: ResolveOptions\n  ) => (\n    resolveContext: string,\n    resolveRequest: string\n  ) => Promise<[string | null, boolean]>,\n  isLocalCallback?: (res: string) => any,\n  baseResolveCheck = true,\n  esmResolveOptions: any = NODE_ESM_RESOLVE_OPTIONS,\n  nodeResolveOptions: any = NODE_RESOLVE_OPTIONS,\n  baseEsmResolveOptions: any = NODE_BASE_ESM_RESOLVE_OPTIONS,\n  baseResolveOptions: any = NODE_BASE_RESOLVE_OPTIONS\n) {\n  const esmExternals = !!esmExternalsConfig\n  const looseEsmExternals = esmExternalsConfig === 'loose'\n\n  let res: string | null = null\n  let isEsm: boolean = false\n\n  const preferEsmOptions =\n    esmExternals && isEsmRequested ? [true, false] : [false]\n\n  for (const preferEsm of preferEsmOptions) {\n    const resolveOptions = preferEsm ? esmResolveOptions : nodeResolveOptions\n\n    const resolve = getResolve(resolveOptions)\n\n    // Resolve the import with the webpack provided context, this\n    // ensures we're resolving the correct version when multiple\n    // exist.\n    try {\n      ;[res, isEsm] = await resolve(context, request)\n    } catch (err) {\n      res = null\n    }\n\n    if (!res) {\n      continue\n    }\n\n    // ESM externals can only be imported (and not required).\n    // Make an exception in loose mode.\n    if (!isEsmRequested && isEsm && !looseEsmExternals) {\n      continue\n    }\n\n    if (isLocalCallback) {\n      return { localRes: isLocalCallback(res) }\n    }\n\n    // Bundled Node.js code is relocated without its node_modules tree.\n    // This means we need to make sure its request resolves to the same\n    // package that'll be available at runtime. If it's not identical,\n    // we need to bundle the code (even if it _should_ be external).\n    if (baseResolveCheck) {\n      let baseRes: string | null\n      let baseIsEsm: boolean\n      try {\n        const baseResolve = getResolve(\n          isEsm ? baseEsmResolveOptions : baseResolveOptions\n        )\n        ;[baseRes, baseIsEsm] = await baseResolve(dir, request)\n      } catch (err) {\n        baseRes = null\n        baseIsEsm = false\n      }\n\n      // Same as above: if the package, when required from the root,\n      // would be different from what the real resolution would use, we\n      // cannot externalize it.\n      // if request is pointing to a symlink it could point to the same file,\n      // the resolver will resolve symlinks so this is handled\n      if (baseRes !== res || isEsm !== baseIsEsm) {\n        res = null\n        continue\n      }\n    }\n    break\n  }\n  return { res, isEsm }\n}\n\nexport function makeExternalHandler({\n  config,\n  optOutBundlingPackageRegex,\n  transpiledPackages,\n  dir,\n}: {\n  config: NextConfigComplete\n  optOutBundlingPackageRegex: RegExp\n  transpiledPackages: string[]\n  dir: string\n}) {\n  let resolvedExternalPackageDirs: Map<string, string>\n  const looseEsmExternals = config.experimental?.esmExternals === 'loose'\n\n  return async function handleExternals(\n    context: string,\n    request: string,\n    dependencyType: string,\n    layer: WebpackLayerName | null,\n    getResolve: (\n      options: any\n    ) => (\n      resolveContext: string,\n      resolveRequest: string\n    ) => Promise<[string | null, boolean]>\n  ) {\n    // We need to externalize internal requests for files intended to\n    // not be bundled.\n    const isLocal: boolean =\n      request.startsWith('.') ||\n      // Always check for unix-style path, as webpack sometimes\n      // normalizes as posix.\n      path.posix.isAbsolute(request) ||\n      // When on Windows, we also want to check for Windows-specific\n      // absolute paths.\n      (process.platform === 'win32' && path.win32.isAbsolute(request))\n\n    // make sure import \"next\" shows a warning when imported\n    // in pages/components\n    if (request === 'next') {\n      return `commonjs next/dist/lib/import-next-warning`\n    }\n\n    const isAppLayer = isWebpackBundledLayer(layer)\n\n    // Relative requires don't need custom resolution, because they\n    // are relative to requests we've already resolved here.\n    // Absolute requires (require('/foo')) are extremely uncommon, but\n    // also have no need for customization as they're already resolved.\n    if (!isLocal) {\n      if (/^next$/.test(request)) {\n        return `commonjs ${request}`\n      }\n\n      if (reactPackagesRegex.test(request) && !isAppLayer) {\n        return `commonjs ${request}`\n      }\n\n      const notExternalModules =\n        /^(?:private-next-pages\\/|next\\/(?:dist\\/pages\\/|(?:app|cache|document|link|form|head|image|legacy\\/image|constants|dynamic|script|navigation|headers|router|compat\\/router|server)$)|string-hash|private-next-rsc-action-validate|private-next-rsc-action-client-wrapper|private-next-rsc-server-reference|private-next-rsc-cache-wrapper$)/\n      if (notExternalModules.test(request)) {\n        return\n      }\n    }\n\n    // @swc/helpers should not be external as it would\n    // require hoisting the package which we can't rely on\n    if (request.includes('@swc/helpers')) {\n      return\n    }\n\n    // BARREL_OPTIMIZATION_PREFIX is a special marker that tells Next.js to\n    // optimize the import by removing unused exports. This has to be compiled.\n    if (request.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n      return\n    }\n\n    // When in esm externals mode, and using import, we resolve with\n    // ESM resolving options.\n    // Also disable esm request when appDir is enabled\n    const isEsmRequested = dependencyType === 'esm'\n\n    // Don't bundle @vercel/og nodejs bundle for nodejs runtime.\n    // TODO-APP: bundle route.js with different layer that externals common node_module deps.\n    // Make sure @vercel/og is loaded as ESM for Node.js runtime\n    if (\n      isWebpackServerOnlyLayer(layer) &&\n      request === 'next/dist/compiled/@vercel/og/index.node.js'\n    ) {\n      return `module ${request}`\n    }\n\n    // Specific Next.js imports that should remain external\n    // TODO-APP: Investigate if we can remove this.\n    if (request.startsWith('next/dist/')) {\n      // Non external that needs to be transpiled\n      // Image loader needs to be transpiled\n      if (/^next[\\\\/]dist[\\\\/]shared[\\\\/]lib[\\\\/]image-loader/.test(request)) {\n        return\n      }\n\n      if (/^next[\\\\/]dist[\\\\/]compiled[\\\\/]next-server/.test(request)) {\n        return `commonjs ${request}`\n      }\n\n      if (\n        /^next[\\\\/]dist[\\\\/]shared[\\\\/](?!lib[\\\\/]router[\\\\/]router)/.test(\n          request\n        ) ||\n        /^next[\\\\/]dist[\\\\/]compiled[\\\\/].*\\.c?js$/.test(request)\n      ) {\n        return `commonjs ${request}`\n      }\n\n      if (\n        /^next[\\\\/]dist[\\\\/]esm[\\\\/]shared[\\\\/](?!lib[\\\\/]router[\\\\/]router)/.test(\n          request\n        ) ||\n        /^next[\\\\/]dist[\\\\/]compiled[\\\\/].*\\.mjs$/.test(request)\n      ) {\n        return `module ${request}`\n      }\n\n      return resolveNextExternal(request)\n    }\n\n    // TODO-APP: Let's avoid this resolve call as much as possible, and eventually get rid of it.\n    const resolveResult = await resolveExternal(\n      dir,\n      config.experimental.esmExternals,\n      context,\n      request,\n      isEsmRequested,\n      getResolve,\n      isLocal ? resolveNextExternal : undefined\n    )\n\n    if ('localRes' in resolveResult) {\n      return resolveResult.localRes\n    }\n\n    // Forcedly resolve the styled-jsx installed by next.js,\n    // since `resolveExternal` cannot find the styled-jsx dep with pnpm\n    if (request === 'styled-jsx/style') {\n      resolveResult.res = defaultOverrides['styled-jsx/style']\n    }\n\n    const { res, isEsm } = resolveResult\n\n    // If the request cannot be resolved we need to have\n    // webpack \"bundle\" it so it surfaces the not found error.\n    if (!res) {\n      return\n    }\n\n    const isOptOutBundling = optOutBundlingPackageRegex.test(res)\n    // Apply bundling rules to all app layers.\n    // Since handleExternals only handle the server layers, we don't need to exclude client here\n    if (!isOptOutBundling && isAppLayer) {\n      return\n    }\n\n    // ESM externals can only be imported (and not required).\n    // Make an exception in loose mode.\n    if (!isEsmRequested && isEsm && !looseEsmExternals && !isLocal) {\n      throw new Error(\n        `ESM packages (${request}) need to be imported. Use 'import' to reference the package instead. https://nextjs.org/docs/messages/import-esm-externals`\n      )\n    }\n\n    const externalType = isEsm ? 'module' : 'commonjs'\n\n    // Default pages have to be transpiled\n    if (\n      // This is the @babel/plugin-transform-runtime \"helpers: true\" option\n      /node_modules[/\\\\]@babel[/\\\\]runtime[/\\\\]/.test(res)\n    ) {\n      return\n    }\n\n    // Webpack itself has to be compiled because it doesn't always use module relative paths\n    if (\n      /node_modules[/\\\\]webpack/.test(res) ||\n      /node_modules[/\\\\]css-loader/.test(res)\n    ) {\n      return\n    }\n\n    // If a package should be transpiled by Next.js, we skip making it external.\n    // It doesn't matter what the extension is, as we'll transpile it anyway.\n    if (transpiledPackages && !resolvedExternalPackageDirs) {\n      resolvedExternalPackageDirs = new Map()\n      // We need to resolve all the external package dirs initially.\n      for (const pkg of transpiledPackages) {\n        const pkgRes = await resolveExternal(\n          dir,\n          config.experimental.esmExternals,\n          context,\n          pkg + '/package.json',\n          isEsmRequested,\n          getResolve,\n          isLocal ? resolveNextExternal : undefined\n        )\n        if (pkgRes.res) {\n          resolvedExternalPackageDirs.set(pkg, path.dirname(pkgRes.res))\n        }\n      }\n    }\n\n    const resolvedBundlingOptOutRes = resolveBundlingOptOutPackages({\n      resolvedRes: res,\n      config,\n      resolvedExternalPackageDirs,\n      isAppLayer,\n      externalType,\n      isOptOutBundling,\n      request,\n      transpiledPackages,\n    })\n    if (resolvedBundlingOptOutRes) {\n      return resolvedBundlingOptOutRes\n    }\n\n    // if here, we default to bundling the file\n    return\n  }\n}\n\nfunction resolveBundlingOptOutPackages({\n  resolvedRes,\n  config,\n  resolvedExternalPackageDirs,\n  isAppLayer,\n  externalType,\n  isOptOutBundling,\n  request,\n  transpiledPackages,\n}: {\n  resolvedRes: string\n  config: NextConfigComplete\n  resolvedExternalPackageDirs: Map<string, string>\n  isAppLayer: boolean\n  externalType: string\n  isOptOutBundling: boolean\n  request: string\n  transpiledPackages: string[]\n}) {\n  if (nodeModulesRegex.test(resolvedRes)) {\n    const shouldBundlePages =\n      !isAppLayer && config.bundlePagesRouterDependencies && !isOptOutBundling\n\n    const shouldBeBundled =\n      shouldBundlePages ||\n      isResourceInPackages(\n        resolvedRes,\n        transpiledPackages,\n        resolvedExternalPackageDirs\n      )\n\n    if (!shouldBeBundled) {\n      return `${externalType} ${request}` // Externalize if not bundled or opted out\n    }\n  }\n}\n\n/**\n * @param localRes the full path to the file\n * @returns the externalized path\n * @description returns an externalized path if the file is a Next.js file and ends with either `.shared-runtime.js` or `.external.js`\n * This is used to ensure that files used across the rendering runtime(s) and the user code are one and the same. The logic in this function\n * will rewrite the require to the correct bundle location depending on the layer at which the file is being used.\n */\nfunction resolveNextExternal(localRes: string) {\n  const isExternal = externalPattern.test(localRes)\n\n  // if the file ends with .external, we need to make it a commonjs require in all cases\n  // this is used mainly to share the async local storage across the routing, rendering and user layers.\n  if (isExternal) {\n    // it's important we return the path that starts with `next/dist/` here instead of the absolute path\n    // otherwise NFT will get tripped up\n    return `commonjs ${normalizePathSep(\n      localRes.replace(/.*?next[/\\\\]dist/, 'next/dist')\n    )}`\n  }\n}\n"], "names": ["defaultOverrides", "BARREL_OPTIMIZATION_PREFIX", "path", "NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "isWebpackBundledLayer", "isWebpackServerOnlyLayer", "normalizePathSep", "reactPackagesRegex", "pathSeparators", "optionalEsmPart", "externalFileEnd", "nextDist", "externalPattern", "RegExp", "nodeModulesRegex", "isResourceInPackages", "resource", "packageNames", "packageDirMapping", "some", "p", "has", "startsWith", "get", "sep", "includes", "join", "replace", "resolveExternal", "dir", "esmExternalsConfig", "context", "request", "isEsmRequested", "getResolve", "isLocalCallback", "baseResolveCheck", "esmResolveOptions", "nodeResolveOptions", "baseEsmResolveOptions", "baseResolveOptions", "esmExternals", "looseEsmExternals", "res", "isEsm", "preferEsmOptions", "preferEsm", "resolveOptions", "resolve", "err", "localRes", "baseRes", "baseIsEsm", "baseResolve", "makeExternalHandler", "config", "optOutBundlingPackageRegex", "transpiledPackages", "resolvedExternalPackageDirs", "experimental", "handleExternals", "dependencyType", "layer", "isLocal", "posix", "isAbsolute", "process", "platform", "win32", "isApp<PERSON><PERSON>er", "test", "notExternalModules", "resolveNextExternal", "resolveResult", "undefined", "isOptOutBundling", "Error", "externalType", "Map", "pkg", "pkgRes", "set", "dirname", "resolvedBundlingOptOutRes", "resolveBundlingOptOutPackages", "resolvedRes", "shouldBundlePages", "bundlePagesRouterDependencies", "shouldBeBundled", "isExternal"], "mappings": "AAGA,SAASA,gBAAgB,QAAQ,yBAAwB;AACzD,SAASC,0BAA0B,QAAQ,0BAAyB;AACpE,OAAOC,UAAU,gCAA+B;AAChD,SACEC,6BAA6B,EAC7BC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QACf,mBAAkB;AACzB,SAASC,qBAAqB,EAAEC,wBAAwB,QAAQ,UAAS;AACzE,SAASC,gBAAgB,QAAQ,6CAA4C;AAC7E,MAAMC,qBAAqB;AAE3B,MAAMC,iBAAiB;AACvB,MAAMC,kBAAkB,CAAC,EAAE,EAAED,eAAe,KAAK,EAAEA,eAAe,CAAC,CAAC;AACpE,MAAME,kBAAkB;AACxB,MAAMC,WAAW,CAAC,IAAI,EAAEH,eAAe,IAAI,CAAC;AAE5C,MAAMI,kBAAkB,IAAIC,OAC1B,GAAGF,WAAWF,gBAAgB,EAAE,EAAEC,iBAAiB;AAGrD,MAAMI,mBAAmB;AAEzB,OAAO,SAASC,qBACdC,QAAgB,EAChBC,YAAuB,EACvBC,iBAAuC;IAEvC,IAAI,CAACD,cAAc,OAAO;IAC1B,OAAOA,aAAaE,IAAI,CAAC,CAACC,IACxBF,qBAAqBA,kBAAkBG,GAAG,CAACD,KACvCJ,SAASM,UAAU,CAACJ,kBAAkBK,GAAG,CAACH,KAAMrB,KAAKyB,GAAG,IACxDR,SAASS,QAAQ,CACf1B,KAAKyB,GAAG,GACNzB,KAAK2B,IAAI,CAAC,gBAAgBN,EAAEO,OAAO,CAAC,OAAO5B,KAAKyB,GAAG,KACnDzB,KAAKyB,GAAG;AAGpB;AAEA,OAAO,eAAeI,gBACpBC,GAAW,EACXC,kBAAsE,EACtEC,OAAe,EACfC,OAAe,EACfC,cAAuB,EACvBC,UAKsC,EACtCC,eAAsC,EACtCC,mBAAmB,IAAI,EACvBC,oBAAyBnC,wBAAwB,EACjDoC,qBAA0BnC,oBAAoB,EAC9CoC,wBAA6BvC,6BAA6B,EAC1DwC,qBAA0BvC,yBAAyB;IAEnD,MAAMwC,eAAe,CAAC,CAACX;IACvB,MAAMY,oBAAoBZ,uBAAuB;IAEjD,IAAIa,MAAqB;IACzB,IAAIC,QAAiB;IAErB,MAAMC,mBACJJ,gBAAgBR,iBAAiB;QAAC;QAAM;KAAM,GAAG;QAAC;KAAM;IAE1D,KAAK,MAAMa,aAAaD,iBAAkB;QACxC,MAAME,iBAAiBD,YAAYT,oBAAoBC;QAEvD,MAAMU,UAAUd,WAAWa;QAE3B,6DAA6D;QAC7D,4DAA4D;QAC5D,SAAS;QACT,IAAI;;YACD,CAACJ,KAAKC,MAAM,GAAG,MAAMI,QAAQjB,SAASC;QACzC,EAAE,OAAOiB,KAAK;YACZN,MAAM;QACR;QAEA,IAAI,CAACA,KAAK;YACR;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACV,kBAAkBW,SAAS,CAACF,mBAAmB;YAClD;QACF;QAEA,IAAIP,iBAAiB;YACnB,OAAO;gBAAEe,UAAUf,gBAAgBQ;YAAK;QAC1C;QAEA,mEAAmE;QACnE,mEAAmE;QACnE,kEAAkE;QAClE,gEAAgE;QAChE,IAAIP,kBAAkB;YACpB,IAAIe;YACJ,IAAIC;YACJ,IAAI;gBACF,MAAMC,cAAcnB,WAClBU,QAAQL,wBAAwBC;gBAEjC,CAACW,SAASC,UAAU,GAAG,MAAMC,YAAYxB,KAAKG;YACjD,EAAE,OAAOiB,KAAK;gBACZE,UAAU;gBACVC,YAAY;YACd;YAEA,8DAA8D;YAC9D,iEAAiE;YACjE,yBAAyB;YACzB,uEAAuE;YACvE,wDAAwD;YACxD,IAAID,YAAYR,OAAOC,UAAUQ,WAAW;gBAC1CT,MAAM;gBACN;YACF;QACF;QACA;IACF;IACA,OAAO;QAAEA;QAAKC;IAAM;AACtB;AAEA,OAAO,SAASU,oBAAoB,EAClCC,MAAM,EACNC,0BAA0B,EAC1BC,kBAAkB,EAClB5B,GAAG,EAMJ;QAE2B0B;IAD1B,IAAIG;IACJ,MAAMhB,oBAAoBa,EAAAA,uBAAAA,OAAOI,YAAY,qBAAnBJ,qBAAqBd,YAAY,MAAK;IAEhE,OAAO,eAAemB,gBACpB7B,OAAe,EACfC,OAAe,EACf6B,cAAsB,EACtBC,KAA8B,EAC9B5B,UAKsC;QAEtC,iEAAiE;QACjE,kBAAkB;QAClB,MAAM6B,UACJ/B,QAAQV,UAAU,CAAC,QACnB,yDAAyD;QACzD,uBAAuB;QACvBvB,KAAKiE,KAAK,CAACC,UAAU,CAACjC,YACtB,8DAA8D;QAC9D,kBAAkB;QACjBkC,QAAQC,QAAQ,KAAK,WAAWpE,KAAKqE,KAAK,CAACH,UAAU,CAACjC;QAEzD,wDAAwD;QACxD,sBAAsB;QACtB,IAAIA,YAAY,QAAQ;YACtB,OAAO,CAAC,0CAA0C,CAAC;QACrD;QAEA,MAAMqC,aAAajE,sBAAsB0D;QAEzC,+DAA+D;QAC/D,wDAAwD;QACxD,kEAAkE;QAClE,mEAAmE;QACnE,IAAI,CAACC,SAAS;YACZ,IAAI,SAASO,IAAI,CAACtC,UAAU;gBAC1B,OAAO,CAAC,SAAS,EAAEA,SAAS;YAC9B;YAEA,IAAIzB,mBAAmB+D,IAAI,CAACtC,YAAY,CAACqC,YAAY;gBACnD,OAAO,CAAC,SAAS,EAAErC,SAAS;YAC9B;YAEA,MAAMuC,qBACJ;YACF,IAAIA,mBAAmBD,IAAI,CAACtC,UAAU;gBACpC;YACF;QACF;QAEA,kDAAkD;QAClD,sDAAsD;QACtD,IAAIA,QAAQP,QAAQ,CAAC,iBAAiB;YACpC;QACF;QAEA,uEAAuE;QACvE,2EAA2E;QAC3E,IAAIO,QAAQV,UAAU,CAACxB,6BAA6B;YAClD;QACF;QAEA,gEAAgE;QAChE,yBAAyB;QACzB,kDAAkD;QAClD,MAAMmC,iBAAiB4B,mBAAmB;QAE1C,4DAA4D;QAC5D,yFAAyF;QACzF,4DAA4D;QAC5D,IACExD,yBAAyByD,UACzB9B,YAAY,+CACZ;YACA,OAAO,CAAC,OAAO,EAAEA,SAAS;QAC5B;QAEA,uDAAuD;QACvD,+CAA+C;QAC/C,IAAIA,QAAQV,UAAU,CAAC,eAAe;YACpC,2CAA2C;YAC3C,sCAAsC;YACtC,IAAI,qDAAqDgD,IAAI,CAACtC,UAAU;gBACtE;YACF;YAEA,IAAI,8CAA8CsC,IAAI,CAACtC,UAAU;gBAC/D,OAAO,CAAC,SAAS,EAAEA,SAAS;YAC9B;YAEA,IACE,8DAA8DsC,IAAI,CAChEtC,YAEF,4CAA4CsC,IAAI,CAACtC,UACjD;gBACA,OAAO,CAAC,SAAS,EAAEA,SAAS;YAC9B;YAEA,IACE,sEAAsEsC,IAAI,CACxEtC,YAEF,2CAA2CsC,IAAI,CAACtC,UAChD;gBACA,OAAO,CAAC,OAAO,EAAEA,SAAS;YAC5B;YAEA,OAAOwC,oBAAoBxC;QAC7B;QAEA,6FAA6F;QAC7F,MAAMyC,gBAAgB,MAAM7C,gBAC1BC,KACA0B,OAAOI,YAAY,CAAClB,YAAY,EAChCV,SACAC,SACAC,gBACAC,YACA6B,UAAUS,sBAAsBE;QAGlC,IAAI,cAAcD,eAAe;YAC/B,OAAOA,cAAcvB,QAAQ;QAC/B;QAEA,wDAAwD;QACxD,mEAAmE;QACnE,IAAIlB,YAAY,oBAAoB;YAClCyC,cAAc9B,GAAG,GAAG9C,gBAAgB,CAAC,mBAAmB;QAC1D;QAEA,MAAM,EAAE8C,GAAG,EAAEC,KAAK,EAAE,GAAG6B;QAEvB,oDAAoD;QACpD,0DAA0D;QAC1D,IAAI,CAAC9B,KAAK;YACR;QACF;QAEA,MAAMgC,mBAAmBnB,2BAA2Bc,IAAI,CAAC3B;QACzD,0CAA0C;QAC1C,4FAA4F;QAC5F,IAAI,CAACgC,oBAAoBN,YAAY;YACnC;QACF;QAEA,yDAAyD;QACzD,mCAAmC;QACnC,IAAI,CAACpC,kBAAkBW,SAAS,CAACF,qBAAqB,CAACqB,SAAS;YAC9D,MAAM,qBAEL,CAFK,IAAIa,MACR,CAAC,cAAc,EAAE5C,QAAQ,2HAA2H,CAAC,GADjJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM6C,eAAejC,QAAQ,WAAW;QAExC,sCAAsC;QACtC,IACE,qEAAqE;QACrE,2CAA2C0B,IAAI,CAAC3B,MAChD;YACA;QACF;QAEA,wFAAwF;QACxF,IACE,2BAA2B2B,IAAI,CAAC3B,QAChC,8BAA8B2B,IAAI,CAAC3B,MACnC;YACA;QACF;QAEA,4EAA4E;QAC5E,yEAAyE;QACzE,IAAIc,sBAAsB,CAACC,6BAA6B;YACtDA,8BAA8B,IAAIoB;YAClC,8DAA8D;YAC9D,KAAK,MAAMC,OAAOtB,mBAAoB;gBACpC,MAAMuB,SAAS,MAAMpD,gBACnBC,KACA0B,OAAOI,YAAY,CAAClB,YAAY,EAChCV,SACAgD,MAAM,iBACN9C,gBACAC,YACA6B,UAAUS,sBAAsBE;gBAElC,IAAIM,OAAOrC,GAAG,EAAE;oBACde,4BAA4BuB,GAAG,CAACF,KAAKhF,KAAKmF,OAAO,CAACF,OAAOrC,GAAG;gBAC9D;YACF;QACF;QAEA,MAAMwC,4BAA4BC,8BAA8B;YAC9DC,aAAa1C;YACbY;YACAG;YACAW;YACAQ;YACAF;YACA3C;YACAyB;QACF;QACA,IAAI0B,2BAA2B;YAC7B,OAAOA;QACT;QAEA,2CAA2C;QAC3C;IACF;AACF;AAEA,SAASC,8BAA8B,EACrCC,WAAW,EACX9B,MAAM,EACNG,2BAA2B,EAC3BW,UAAU,EACVQ,YAAY,EACZF,gBAAgB,EAChB3C,OAAO,EACPyB,kBAAkB,EAUnB;IACC,IAAI3C,iBAAiBwD,IAAI,CAACe,cAAc;QACtC,MAAMC,oBACJ,CAACjB,cAAcd,OAAOgC,6BAA6B,IAAI,CAACZ;QAE1D,MAAMa,kBACJF,qBACAvE,qBACEsE,aACA5B,oBACAC;QAGJ,IAAI,CAAC8B,iBAAiB;YACpB,OAAO,GAAGX,aAAa,CAAC,EAAE7C,SAAS,CAAC,0CAA0C;;QAChF;IACF;AACF;AAEA;;;;;;CAMC,GACD,SAASwC,oBAAoBtB,QAAgB;IAC3C,MAAMuC,aAAa7E,gBAAgB0D,IAAI,CAACpB;IAExC,sFAAsF;IACtF,sGAAsG;IACtG,IAAIuC,YAAY;QACd,oGAAoG;QACpG,oCAAoC;QACpC,OAAO,CAAC,SAAS,EAAEnF,iBACjB4C,SAASvB,OAAO,CAAC,oBAAoB,eACpC;IACL;AACF"}