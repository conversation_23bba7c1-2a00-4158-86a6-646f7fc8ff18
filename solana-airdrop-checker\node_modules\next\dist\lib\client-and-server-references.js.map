{"version": 3, "sources": ["../../src/lib/client-and-server-references.ts"], "sourcesContent": ["import { extractInfoFromServerReferenceId } from '../shared/lib/server-reference-info'\n\n// Only contains the properties we're interested in.\nexport interface ServerReference {\n  $$typeof: Symbol\n  $$id: string\n}\n\nexport type ServerFunction = ServerReference &\n  ((...args: unknown[]) => Promise<unknown>)\n\nexport function isServerReference<T>(\n  value: T & Partial<ServerReference>\n): value is T & ServerFunction {\n  return value.$$typeof === Symbol.for('react.server.reference')\n}\n\nexport function isUseCacheFunction<T>(\n  value: T & Partial<ServerReference>\n): value is T & ServerFunction {\n  if (!isServerReference(value)) {\n    return false\n  }\n\n  const { type } = extractInfoFromServerReferenceId(value.$$id)\n\n  return type === 'use-cache'\n}\n\nexport function isClientReference(mod: any): boolean {\n  const defaultExport = mod?.default || mod\n  return defaultExport?.$$typeof === Symbol.for('react.client.reference')\n}\n"], "names": ["isClientReference", "isServerReference", "isUseCacheFunction", "value", "$$typeof", "Symbol", "for", "type", "extractInfoFromServerReferenceId", "$$id", "mod", "defaultExport", "default"], "mappings": ";;;;;;;;;;;;;;;;IA6BgBA,iBAAiB;eAAjBA;;IAlBAC,iBAAiB;eAAjBA;;IAMAC,kBAAkB;eAAlBA;;;qCAjBiC;AAW1C,SAASD,kBACdE,KAAmC;IAEnC,OAAOA,MAAMC,QAAQ,KAAKC,OAAOC,GAAG,CAAC;AACvC;AAEO,SAASJ,mBACdC,KAAmC;IAEnC,IAAI,CAACF,kBAAkBE,QAAQ;QAC7B,OAAO;IACT;IAEA,MAAM,EAAEI,IAAI,EAAE,GAAGC,IAAAA,qDAAgC,EAACL,MAAMM,IAAI;IAE5D,OAAOF,SAAS;AAClB;AAEO,SAASP,kBAAkBU,GAAQ;IACxC,MAAMC,gBAAgBD,CAAAA,uBAAAA,IAAKE,OAAO,KAAIF;IACtC,OAAOC,CAAAA,iCAAAA,cAAeP,QAAQ,MAAKC,OAAOC,GAAG,CAAC;AAChD"}