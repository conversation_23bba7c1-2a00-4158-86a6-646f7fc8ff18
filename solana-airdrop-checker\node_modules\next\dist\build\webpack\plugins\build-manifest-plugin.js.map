{"version": 3, "sources": ["../../../../src/build/webpack/plugins/build-manifest-plugin.ts"], "sourcesContent": ["import type { <PERSON>Filter } from '../../../shared/lib/bloom-filter'\nimport type { Rewrite, CustomRoutes } from '../../../lib/load-custom-routes'\nimport devalue from 'next/dist/compiled/devalue'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  BUILD_MANIFEST,\n  MIDDLEWARE_BUILD_MANIFEST,\n  CLIENT_STATIC_FILES_PATH,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport type { BuildManifest } from '../../../server/get-page-files'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nimport { ampFirstEntryNamesMap } from './next-drop-client-page-plugin'\nimport { getSortedRoutes } from '../../../shared/lib/router/utils'\nimport { Span } from '../../../trace'\nimport { getCompilationSpan } from '../utils'\n\ntype DeepMutable<T> = { -readonly [P in keyof T]: DeepMutable<T[P]> }\n\nexport type ClientBuildManifest = {\n  [key: string]: string[]\n}\n\n// Add the runtime ssg manifest file as a lazy-loaded file dependency.\n// We also stub this file out for development mode (when it is not\n// generated).\nexport const srcEmptySsgManifest = `self.__SSG_MANIFEST=new Set;self.__SSG_MANIFEST_CB&&self.__SSG_MANIFEST_CB()`\n\n// nodejs: '/static/<build id>/low-priority.js'\nfunction buildNodejsLowPriorityPath(filename: string, buildId: string) {\n  return `${CLIENT_STATIC_FILES_PATH}/${buildId}/${filename}`\n}\n\nexport function createEdgeRuntimeManifest(\n  originAssetMap: Partial<BuildManifest>\n): string {\n  const manifestFilenames = ['_buildManifest.js', '_ssgManifest.js']\n\n  const assetMap: Partial<BuildManifest> = {\n    ...originAssetMap,\n    lowPriorityFiles: [],\n  }\n\n  // we use globalThis here because middleware can be node\n  // which doesn't have \"self\"\n  const manifestDefCode = `globalThis.__BUILD_MANIFEST = ${JSON.stringify(\n    assetMap,\n    null,\n    2\n  )};\\n`\n  // edge lowPriorityFiles item: '\"/static/\" + process.env.__NEXT_BUILD_ID + \"/low-priority.js\"'.\n  // Since lowPriorityFiles is not fixed and relying on `process.env.__NEXT_BUILD_ID`, we'll produce code creating it dynamically.\n  const lowPriorityFilesCode =\n    `globalThis.__BUILD_MANIFEST.lowPriorityFiles = [\\n` +\n    manifestFilenames\n      .map((filename) => {\n        return `\"/static/\" + process.env.__NEXT_BUILD_ID + \"/${filename}\",\\n`\n      })\n      .join(',') +\n    `\\n];`\n\n  return manifestDefCode + lowPriorityFilesCode\n}\n\nfunction normalizeRewrite(item: {\n  source: string\n  destination: string\n  has?: any\n}): CustomRoutes['rewrites']['beforeFiles'][0] {\n  return {\n    has: item.has,\n    source: item.source,\n    destination: item.destination,\n  }\n}\n\nexport function normalizeRewritesForBuildManifest(\n  rewrites: CustomRoutes['rewrites']\n): CustomRoutes['rewrites'] {\n  return {\n    afterFiles: rewrites.afterFiles\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n    beforeFiles: rewrites.beforeFiles\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n    fallback: rewrites.fallback\n      ?.map(processRoute)\n      ?.map((item) => normalizeRewrite(item)),\n  }\n}\n\n// This function takes the asset map generated in BuildManifestPlugin and creates a\n// reduced version to send to the client.\nexport function generateClientManifest(\n  assetMap: BuildManifest,\n  rewrites: CustomRoutes['rewrites'],\n  clientRouterFilters?: {\n    staticFilter: ReturnType<BloomFilter['export']>\n    dynamicFilter: ReturnType<BloomFilter['export']>\n  },\n  compiler?: any,\n  compilation?: any\n): string | undefined {\n  const compilationSpan = compilation\n    ? getCompilationSpan(compilation)\n    : compiler\n      ? getCompilationSpan(compiler)\n      : new Span({ name: 'client-manifest' })\n\n  const genClientManifestSpan = compilationSpan?.traceChild(\n    'NextJsBuildManifest-generateClientManifest'\n  )\n\n  return genClientManifestSpan?.traceFn(() => {\n    const clientManifest: ClientBuildManifest = {\n      __rewrites: normalizeRewritesForBuildManifest(rewrites) as any,\n      __routerFilterStatic: clientRouterFilters?.staticFilter as any,\n      __routerFilterDynamic: clientRouterFilters?.dynamicFilter as any,\n    }\n    const appDependencies = new Set(assetMap.pages['/_app'])\n    const sortedPageKeys = getSortedRoutes(Object.keys(assetMap.pages))\n\n    sortedPageKeys.forEach((page) => {\n      const dependencies = assetMap.pages[page]\n\n      if (page === '/_app') return\n      // Filter out dependencies in the _app entry, because those will have already\n      // been loaded by the client prior to a navigation event\n      const filteredDeps = dependencies.filter(\n        (dep) => !appDependencies.has(dep)\n      )\n\n      // The manifest can omit the page if it has no requirements\n      if (filteredDeps.length) {\n        clientManifest[page] = filteredDeps\n      }\n    })\n    // provide the sorted pages as an array so we don't rely on the object's keys\n    // being in order and we don't slow down look-up time for page assets\n    clientManifest.sortedPages = sortedPageKeys\n\n    return devalue(clientManifest)\n  })\n}\n\nexport function getEntrypointFiles(entrypoint: any): string[] {\n  return (\n    entrypoint\n      ?.getFiles()\n      .filter((file: string) => {\n        // We don't want to include `.hot-update.js` files into the initial page\n        return /(?<!\\.hot-update)\\.(js|css)($|\\?)/.test(file)\n      })\n      .map((file: string) => file.replace(/\\\\/g, '/')) ?? []\n  )\n}\n\nexport const processRoute = (r: Rewrite) => {\n  const rewrite = { ...r }\n\n  // omit external rewrite destinations since these aren't\n  // handled client-side\n  if (!rewrite?.destination?.startsWith('/')) {\n    delete (rewrite as any).destination\n  }\n  return rewrite\n}\n\n// This plugin creates a build-manifest.json for all assets that are being output\n// It has a mapping of \"entry\" filename to real filename. Because the real filename can be hashed in production\nexport default class BuildManifestPlugin {\n  private buildId: string\n  private rewrites: CustomRoutes['rewrites']\n  private isDevFallback: boolean\n  private appDirEnabled: boolean\n  private clientRouterFilters?: Parameters<typeof generateClientManifest>[2]\n\n  constructor(options: {\n    buildId: string\n    rewrites: CustomRoutes['rewrites']\n    isDevFallback?: boolean\n    appDirEnabled: boolean\n    clientRouterFilters?: Parameters<typeof generateClientManifest>[2]\n  }) {\n    this.buildId = options.buildId\n    this.isDevFallback = !!options.isDevFallback\n    this.rewrites = {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    }\n    this.appDirEnabled = options.appDirEnabled\n    this.clientRouterFilters = options.clientRouterFilters\n    this.rewrites.beforeFiles = options.rewrites.beforeFiles.map(processRoute)\n    this.rewrites.afterFiles = options.rewrites.afterFiles.map(processRoute)\n    this.rewrites.fallback = options.rewrites.fallback.map(processRoute)\n  }\n\n  createAssets(compiler: any, compilation: any) {\n    const compilationSpan =\n      getCompilationSpan(compilation) ?? getCompilationSpan(compiler)\n    if (!compilationSpan) {\n      throw new Error('No span found for compilation')\n    }\n\n    const createAssetsSpan = compilationSpan.traceChild(\n      'NextJsBuildManifest-createassets'\n    )\n\n    return createAssetsSpan.traceFn(() => {\n      const entrypoints: Map<string, any> = compilation.entrypoints\n      const assetMap: DeepMutable<BuildManifest> = {\n        polyfillFiles: [],\n        devFiles: [],\n        ampDevFiles: [],\n        lowPriorityFiles: [],\n        rootMainFiles: [],\n        rootMainFilesTree: {},\n        pages: { '/_app': [] },\n        ampFirstPages: [],\n      }\n\n      const ampFirstEntryNames = ampFirstEntryNamesMap.get(compilation)\n      if (ampFirstEntryNames) {\n        for (const entryName of ampFirstEntryNames) {\n          const pagePath = getRouteFromEntrypoint(entryName)\n          if (!pagePath) {\n            continue\n          }\n\n          assetMap.ampFirstPages.push(pagePath)\n        }\n      }\n\n      const mainFiles = new Set(\n        getEntrypointFiles(entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN))\n      )\n\n      if (this.appDirEnabled) {\n        assetMap.rootMainFiles = [\n          ...new Set(\n            getEntrypointFiles(\n              entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n            )\n          ),\n        ]\n      }\n\n      const compilationAssets: {\n        name: string\n        source: typeof sources.RawSource\n        info: object\n      }[] = compilation.getAssets()\n\n      assetMap.polyfillFiles = compilationAssets\n        .filter((p) => {\n          // Ensure only .js files are passed through\n          if (!p.name.endsWith('.js')) {\n            return false\n          }\n\n          return (\n            p.info && CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL in p.info\n          )\n        })\n        .map((v) => v.name)\n\n      assetMap.devFiles = getEntrypointFiles(\n        entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH)\n      ).filter((file) => !mainFiles.has(file))\n\n      assetMap.ampDevFiles = getEntrypointFiles(\n        entrypoints.get(CLIENT_STATIC_FILES_RUNTIME_AMP)\n      )\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        if (SYSTEM_ENTRYPOINTS.has(entrypoint.name)) continue\n        const pagePath = getRouteFromEntrypoint(entrypoint.name)\n\n        if (!pagePath) {\n          continue\n        }\n\n        const filesForPage = getEntrypointFiles(entrypoint)\n\n        assetMap.pages[pagePath] = [...new Set([...mainFiles, ...filesForPage])]\n      }\n\n      if (!this.isDevFallback) {\n        // Add the runtime build manifest file (generated later in this file)\n        // as a dependency for the app. If the flag is false, the file won't be\n        // downloaded by the client.\n        const buildManifestPath = buildNodejsLowPriorityPath(\n          '_buildManifest.js',\n          this.buildId\n        )\n        const ssgManifestPath = buildNodejsLowPriorityPath(\n          '_ssgManifest.js',\n          this.buildId\n        )\n        assetMap.lowPriorityFiles.push(buildManifestPath, ssgManifestPath)\n        compilation.emitAsset(\n          ssgManifestPath,\n          new sources.RawSource(srcEmptySsgManifest)\n        )\n      }\n\n      assetMap.pages = Object.keys(assetMap.pages)\n        .sort()\n        .reduce(\n          // eslint-disable-next-line\n          (a, c) => ((a[c] = assetMap.pages[c]), a),\n          {} as typeof assetMap.pages\n        )\n\n      let buildManifestName = BUILD_MANIFEST\n\n      if (this.isDevFallback) {\n        buildManifestName = `fallback-${BUILD_MANIFEST}`\n      }\n\n      compilation.emitAsset(\n        buildManifestName,\n        new sources.RawSource(JSON.stringify(assetMap, null, 2))\n      )\n\n      compilation.emitAsset(\n        `server/${MIDDLEWARE_BUILD_MANIFEST}.js`,\n        new sources.RawSource(`${createEdgeRuntimeManifest(assetMap)}`)\n      )\n\n      if (!this.isDevFallback) {\n        compilation.emitAsset(\n          `${CLIENT_STATIC_FILES_PATH}/${this.buildId}/_buildManifest.js`,\n          new sources.RawSource(\n            `self.__BUILD_MANIFEST = ${generateClientManifest(\n              assetMap,\n              this.rewrites,\n              this.clientRouterFilters,\n              compiler,\n              compilation\n            )};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()`\n          )\n        )\n      }\n    })\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.make.tap('NextJsBuildManifest', (compilation: any) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: 'NextJsBuildManifest',\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n        },\n        () => {\n          this.createAssets(compiler, compilation)\n        }\n      )\n    })\n    return\n  }\n}\n"], "names": ["createEdgeRuntimeManifest", "BuildManifestPlugin", "generateClientManifest", "getEntrypointFiles", "normalizeRewritesForBuildManifest", "processRoute", "srcEmptySsgManifest", "buildNodejsLowPriorityPath", "filename", "buildId", "CLIENT_STATIC_FILES_PATH", "originAssetMap", "manifestFilenames", "assetMap", "lowPriorityFiles", "manifestDefCode", "JSON", "stringify", "lowPriorityFilesCode", "map", "join", "normalizeRewrite", "item", "has", "source", "destination", "rewrites", "afterFiles", "beforeFiles", "fallback", "clientRouterFilters", "compiler", "compilation", "compilationSpan", "getCompilationSpan", "Span", "name", "genClientManifestSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceFn", "clientManifest", "__rewrites", "__routerFilterStatic", "staticFilter", "__routerFilterDynamic", "dynamicFilter", "appDependencies", "Set", "pages", "sortedPageKeys", "getSortedRoutes", "Object", "keys", "for<PERSON>ach", "page", "dependencies", "filteredDeps", "filter", "dep", "length", "sortedPages", "devalue", "entrypoint", "getFiles", "file", "test", "replace", "r", "rewrite", "startsWith", "constructor", "options", "isDev<PERSON><PERSON><PERSON>", "appDirEnabled", "createAssets", "Error", "createAssetsSpan", "entrypoints", "polyfillFiles", "devFiles", "ampDevFiles", "rootMainFiles", "rootMainFilesTree", "ampFirstPages", "ampFirstEntryNames", "ampFirstEntryNamesMap", "get", "entryName", "pagePath", "getRouteFromEntrypoint", "push", "mainFiles", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "compilationAssets", "getAssets", "p", "endsWith", "info", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "v", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "values", "SYSTEM_ENTRYPOINTS", "filesForPage", "buildManifestPath", "ssgManifestPath", "emitAsset", "sources", "RawSource", "sort", "reduce", "a", "c", "buildManifestName", "BUILD_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "apply", "hooks", "make", "tap", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAsCgBA,yBAAyB;eAAzBA;;IAwIhB,iFAAiF;IACjF,+GAA+G;IAC/G,OAgMC;eAhMoBC;;IA7ELC,sBAAsB;eAAtBA;;IAoDAC,kBAAkB;eAAlBA;;IAtEAC,iCAAiC;eAAjCA;;IAkFHC,YAAY;eAAZA;;IApIAC,mBAAmB;eAAnBA;;;gEA7BO;yBACa;2BAW1B;+EAE4B;0CACG;uBACN;uBACX;wBACc;;;;;;AAW5B,MAAMA,sBAAsB,CAAC,4EAA4E,CAAC;AAEjH,+CAA+C;AAC/C,SAASC,2BAA2BC,QAAgB,EAAEC,OAAe;IACnE,OAAO,GAAGC,mCAAwB,CAAC,CAAC,EAAED,QAAQ,CAAC,EAAED,UAAU;AAC7D;AAEO,SAASR,0BACdW,cAAsC;IAEtC,MAAMC,oBAAoB;QAAC;QAAqB;KAAkB;IAElE,MAAMC,WAAmC;QACvC,GAAGF,cAAc;QACjBG,kBAAkB,EAAE;IACtB;IAEA,wDAAwD;IACxD,4BAA4B;IAC5B,MAAMC,kBAAkB,CAAC,8BAA8B,EAAEC,KAAKC,SAAS,CACrEJ,UACA,MACA,GACA,GAAG,CAAC;IACN,+FAA+F;IAC/F,gIAAgI;IAChI,MAAMK,uBACJ,CAAC,kDAAkD,CAAC,GACpDN,kBACGO,GAAG,CAAC,CAACX;QACJ,OAAO,CAAC,6CAA6C,EAAEA,SAAS,IAAI,CAAC;IACvE,GACCY,IAAI,CAAC,OACR,CAAC,IAAI,CAAC;IAER,OAAOL,kBAAkBG;AAC3B;AAEA,SAASG,iBAAiBC,IAIzB;IACC,OAAO;QACLC,KAAKD,KAAKC,GAAG;QACbC,QAAQF,KAAKE,MAAM;QACnBC,aAAaH,KAAKG,WAAW;IAC/B;AACF;AAEO,SAASrB,kCACdsB,QAAkC;QAGpBA,0BAAAA,sBAGCA,2BAAAA,uBAGHA,wBAAAA;IAPZ,OAAO;QACLC,UAAU,GAAED,uBAAAA,SAASC,UAAU,sBAAnBD,2BAAAA,qBACRP,GAAG,CAACd,kCADIqB,yBAERP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QACnCM,WAAW,GAAEF,wBAAAA,SAASE,WAAW,sBAApBF,4BAAAA,sBACTP,GAAG,CAACd,kCADKqB,0BAETP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;QACnCO,QAAQ,GAAEH,qBAAAA,SAASG,QAAQ,sBAAjBH,yBAAAA,mBACNP,GAAG,CAACd,kCADEqB,uBAENP,GAAG,CAAC,CAACG,OAASD,iBAAiBC;IACrC;AACF;AAIO,SAASpB,uBACdW,QAAuB,EACvBa,QAAkC,EAClCI,mBAGC,EACDC,QAAc,EACdC,WAAiB;IAEjB,MAAMC,kBAAkBD,cACpBE,IAAAA,0BAAkB,EAACF,eACnBD,WACEG,IAAAA,0BAAkB,EAACH,YACnB,IAAII,WAAI,CAAC;QAAEC,MAAM;IAAkB;IAEzC,MAAMC,wBAAwBJ,mCAAAA,gBAAiBK,UAAU,CACvD;IAGF,OAAOD,yCAAAA,sBAAuBE,OAAO,CAAC;QACpC,MAAMC,iBAAsC;YAC1CC,YAAYrC,kCAAkCsB;YAC9CgB,oBAAoB,EAAEZ,uCAAAA,oBAAqBa,YAAY;YACvDC,qBAAqB,EAAEd,uCAAAA,oBAAqBe,aAAa;QAC3D;QACA,MAAMC,kBAAkB,IAAIC,IAAIlC,SAASmC,KAAK,CAAC,QAAQ;QACvD,MAAMC,iBAAiBC,IAAAA,sBAAe,EAACC,OAAOC,IAAI,CAACvC,SAASmC,KAAK;QAEjEC,eAAeI,OAAO,CAAC,CAACC;YACtB,MAAMC,eAAe1C,SAASmC,KAAK,CAACM,KAAK;YAEzC,IAAIA,SAAS,SAAS;YACtB,6EAA6E;YAC7E,wDAAwD;YACxD,MAAME,eAAeD,aAAaE,MAAM,CACtC,CAACC,MAAQ,CAACZ,gBAAgBvB,GAAG,CAACmC;YAGhC,2DAA2D;YAC3D,IAAIF,aAAaG,MAAM,EAAE;gBACvBnB,cAAc,CAACc,KAAK,GAAGE;YACzB;QACF;QACA,6EAA6E;QAC7E,qEAAqE;QACrEhB,eAAeoB,WAAW,GAAGX;QAE7B,OAAOY,IAAAA,gBAAO,EAACrB;IACjB;AACF;AAEO,SAASrC,mBAAmB2D,UAAe;IAChD,OACEA,CAAAA,8BAAAA,WACIC,QAAQ,GACTN,MAAM,CAAC,CAACO;QACP,wEAAwE;QACxE,OAAO,oCAAoCC,IAAI,CAACD;IAClD,GACC7C,GAAG,CAAC,CAAC6C,OAAiBA,KAAKE,OAAO,CAAC,OAAO,UAAS,EAAE;AAE5D;AAEO,MAAM7D,eAAe,CAAC8D;QAKtBC;IAJL,MAAMA,UAAU;QAAE,GAAGD,CAAC;IAAC;IAEvB,wDAAwD;IACxD,sBAAsB;IACtB,IAAI,EAACC,4BAAAA,uBAAAA,QAAS3C,WAAW,qBAApB2C,qBAAsBC,UAAU,CAAC,OAAM;QAC1C,OAAO,AAACD,QAAgB3C,WAAW;IACrC;IACA,OAAO2C;AACT;AAIe,MAAMnE;IAOnBqE,YAAYC,OAMX,CAAE;QACD,IAAI,CAAC9D,OAAO,GAAG8D,QAAQ9D,OAAO;QAC9B,IAAI,CAAC+D,aAAa,GAAG,CAAC,CAACD,QAAQC,aAAa;QAC5C,IAAI,CAAC9C,QAAQ,GAAG;YACdE,aAAa,EAAE;YACfD,YAAY,EAAE;YACdE,UAAU,EAAE;QACd;QACA,IAAI,CAAC4C,aAAa,GAAGF,QAAQE,aAAa;QAC1C,IAAI,CAAC3C,mBAAmB,GAAGyC,QAAQzC,mBAAmB;QACtD,IAAI,CAACJ,QAAQ,CAACE,WAAW,GAAG2C,QAAQ7C,QAAQ,CAACE,WAAW,CAACT,GAAG,CAACd;QAC7D,IAAI,CAACqB,QAAQ,CAACC,UAAU,GAAG4C,QAAQ7C,QAAQ,CAACC,UAAU,CAACR,GAAG,CAACd;QAC3D,IAAI,CAACqB,QAAQ,CAACG,QAAQ,GAAG0C,QAAQ7C,QAAQ,CAACG,QAAQ,CAACV,GAAG,CAACd;IACzD;IAEAqE,aAAa3C,QAAa,EAAEC,WAAgB,EAAE;QAC5C,MAAMC,kBACJC,IAAAA,0BAAkB,EAACF,gBAAgBE,IAAAA,0BAAkB,EAACH;QACxD,IAAI,CAACE,iBAAiB;YACpB,MAAM,qBAA0C,CAA1C,IAAI0C,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QAEA,MAAMC,mBAAmB3C,gBAAgBK,UAAU,CACjD;QAGF,OAAOsC,iBAAiBrC,OAAO,CAAC;YAC9B,MAAMsC,cAAgC7C,YAAY6C,WAAW;YAC7D,MAAMhE,WAAuC;gBAC3CiE,eAAe,EAAE;gBACjBC,UAAU,EAAE;gBACZC,aAAa,EAAE;gBACflE,kBAAkB,EAAE;gBACpBmE,eAAe,EAAE;gBACjBC,mBAAmB,CAAC;gBACpBlC,OAAO;oBAAE,SAAS,EAAE;gBAAC;gBACrBmC,eAAe,EAAE;YACnB;YAEA,MAAMC,qBAAqBC,+CAAqB,CAACC,GAAG,CAACtD;YACrD,IAAIoD,oBAAoB;gBACtB,KAAK,MAAMG,aAAaH,mBAAoB;oBAC1C,MAAMI,WAAWC,IAAAA,+BAAsB,EAACF;oBACxC,IAAI,CAACC,UAAU;wBACb;oBACF;oBAEA3E,SAASsE,aAAa,CAACO,IAAI,CAACF;gBAC9B;YACF;YAEA,MAAMG,YAAY,IAAI5C,IACpB5C,mBAAmB0E,YAAYS,GAAG,CAACM,2CAAgC;YAGrE,IAAI,IAAI,CAACnB,aAAa,EAAE;gBACtB5D,SAASoE,aAAa,GAAG;uBACpB,IAAIlC,IACL5C,mBACE0E,YAAYS,GAAG,CAACO,+CAAoC;iBAGzD;YACH;YAEA,MAAMC,oBAIA9D,YAAY+D,SAAS;YAE3BlF,SAASiE,aAAa,GAAGgB,kBACtBrC,MAAM,CAAC,CAACuC;gBACP,2CAA2C;gBAC3C,IAAI,CAACA,EAAE5D,IAAI,CAAC6D,QAAQ,CAAC,QAAQ;oBAC3B,OAAO;gBACT;gBAEA,OACED,EAAEE,IAAI,IAAIC,uDAA4C,IAAIH,EAAEE,IAAI;YAEpE,GACC/E,GAAG,CAAC,CAACiF,IAAMA,EAAEhE,IAAI;YAEpBvB,SAASkE,QAAQ,GAAG5E,mBAClB0E,YAAYS,GAAG,CAACe,oDAAyC,GACzD5C,MAAM,CAAC,CAACO,OAAS,CAAC2B,UAAUpE,GAAG,CAACyC;YAElCnD,SAASmE,WAAW,GAAG7E,mBACrB0E,YAAYS,GAAG,CAACgB,0CAA+B;YAGjD,KAAK,MAAMxC,cAAc9B,YAAY6C,WAAW,CAAC0B,MAAM,GAAI;gBACzD,IAAIC,6BAAkB,CAACjF,GAAG,CAACuC,WAAW1B,IAAI,GAAG;gBAC7C,MAAMoD,WAAWC,IAAAA,+BAAsB,EAAC3B,WAAW1B,IAAI;gBAEvD,IAAI,CAACoD,UAAU;oBACb;gBACF;gBAEA,MAAMiB,eAAetG,mBAAmB2D;gBAExCjD,SAASmC,KAAK,CAACwC,SAAS,GAAG;uBAAI,IAAIzC,IAAI;2BAAI4C;2BAAcc;qBAAa;iBAAE;YAC1E;YAEA,IAAI,CAAC,IAAI,CAACjC,aAAa,EAAE;gBACvB,qEAAqE;gBACrE,uEAAuE;gBACvE,4BAA4B;gBAC5B,MAAMkC,oBAAoBnG,2BACxB,qBACA,IAAI,CAACE,OAAO;gBAEd,MAAMkG,kBAAkBpG,2BACtB,mBACA,IAAI,CAACE,OAAO;gBAEdI,SAASC,gBAAgB,CAAC4E,IAAI,CAACgB,mBAAmBC;gBAClD3E,YAAY4E,SAAS,CACnBD,iBACA,IAAIE,gBAAO,CAACC,SAAS,CAACxG;YAE1B;YAEAO,SAASmC,KAAK,GAAGG,OAAOC,IAAI,CAACvC,SAASmC,KAAK,EACxC+D,IAAI,GACJC,MAAM,CACL,2BAA2B;YAC3B,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGrG,SAASmC,KAAK,CAACkE,EAAE,EAAGD,CAAAA,GACvC,CAAC;YAGL,IAAIE,oBAAoBC,yBAAc;YAEtC,IAAI,IAAI,CAAC5C,aAAa,EAAE;gBACtB2C,oBAAoB,CAAC,SAAS,EAAEC,yBAAc,EAAE;YAClD;YAEApF,YAAY4E,SAAS,CACnBO,mBACA,IAAIN,gBAAO,CAACC,SAAS,CAAC9F,KAAKC,SAAS,CAACJ,UAAU,MAAM;YAGvDmB,YAAY4E,SAAS,CACnB,CAAC,OAAO,EAAES,oCAAyB,CAAC,GAAG,CAAC,EACxC,IAAIR,gBAAO,CAACC,SAAS,CAAC,GAAG9G,0BAA0Ba,WAAW;YAGhE,IAAI,CAAC,IAAI,CAAC2D,aAAa,EAAE;gBACvBxC,YAAY4E,SAAS,CACnB,GAAGlG,mCAAwB,CAAC,CAAC,EAAE,IAAI,CAACD,OAAO,CAAC,kBAAkB,CAAC,EAC/D,IAAIoG,gBAAO,CAACC,SAAS,CACnB,CAAC,wBAAwB,EAAE5G,uBACzBW,UACA,IAAI,CAACa,QAAQ,EACb,IAAI,CAACI,mBAAmB,EACxBC,UACAC,aACA,uDAAuD,CAAC;YAGhE;QACF;IACF;IAEAsF,MAAMvF,QAA0B,EAAE;QAChCA,SAASwF,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,uBAAuB,CAACzF;YAC9CA,YAAYuF,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACErF,MAAM;gBACNuF,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA;gBACE,IAAI,CAACpD,YAAY,CAAC3C,UAAUC;YAC9B;QAEJ;QACA;IACF;AACF"}