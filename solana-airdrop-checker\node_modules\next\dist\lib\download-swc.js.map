{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport * as Log from '../build/output/log'\nimport tar from 'next/dist/compiled/tar'\nconst { WritableStream } = require('node:stream/web') as {\n  WritableStream: typeof global.WritableStream\n}\nimport { getRegistry } from './helpers/get-registry'\nimport { getCacheDirectory } from './helpers/get-cache-directory'\n\nconst MAX_VERSIONS_TO_CACHE = 8\n\nasync function extractBinary(\n  outputDirectory: string,\n  pkgName: string,\n  tarFileName: string\n) {\n  const cacheDirectory = getCacheDirectory(\n    'next-swc',\n    process.env['NEXT_SWC_PATH']\n  )\n\n  const extractFromTar = () =>\n    tar.x({\n      file: path.join(cacheDirectory, tarFileName),\n      cwd: outputDirectory,\n      strip: 1,\n    })\n\n  if (!fs.existsSync(path.join(cacheDirectory, tarFileName))) {\n    Log.info(`Downloading swc package ${pkgName}... to ${cacheDirectory}`)\n    await fs.promises.mkdir(cacheDirectory, { recursive: true })\n    const tempFile = path.join(\n      cacheDirectory,\n      `${tarFileName}.temp-${Date.now()}`\n    )\n\n    const registry = getRegistry()\n\n    const downloadUrl = `${registry}${pkgName}/-/${tarFileName}`\n\n    await fetch(downloadUrl).then((res) => {\n      const { ok, body } = res\n      if (!ok || !body) {\n        Log.error(`Failed to download swc package from ${downloadUrl}`)\n      }\n\n      if (!ok) {\n        throw new Error(`request failed with status ${res.status}`)\n      }\n      if (!body) {\n        throw new Error('request failed with empty body')\n      }\n      const cacheWriteStream = fs.createWriteStream(tempFile)\n      return body.pipeTo(\n        new WritableStream({\n          write(chunk) {\n            return new Promise<void>((resolve, reject) =>\n              cacheWriteStream.write(chunk, (error) => {\n                if (error) {\n                  reject(error)\n                  return\n                }\n\n                resolve()\n              })\n            )\n          },\n          close() {\n            return new Promise<void>((resolve, reject) =>\n              cacheWriteStream.close((error) => {\n                if (error) {\n                  reject(error)\n                  return\n                }\n\n                resolve()\n              })\n            )\n          },\n        })\n      )\n    })\n\n    await fs.promises.access(tempFile) // ensure the temp file existed\n    await fs.promises.rename(tempFile, path.join(cacheDirectory, tarFileName))\n  } else {\n    Log.info(`Using cached swc package ${pkgName}...`)\n  }\n  await extractFromTar()\n\n  const cacheFiles = await fs.promises.readdir(cacheDirectory)\n\n  if (cacheFiles.length > MAX_VERSIONS_TO_CACHE) {\n    cacheFiles.sort((a, b) => {\n      if (a.length < b.length) return -1\n      return a.localeCompare(b)\n    })\n\n    // prune oldest versions in cache\n    for (let i = 0; i++; i < cacheFiles.length - MAX_VERSIONS_TO_CACHE) {\n      await fs.promises\n        .unlink(path.join(cacheDirectory, cacheFiles[i]))\n        .catch(() => {})\n    }\n  }\n}\n\nexport async function downloadNativeNextSwc(\n  version: string,\n  bindingsDirectory: string,\n  triplesABI: Array<string>\n) {\n  for (const triple of triplesABI) {\n    const pkgName = `@next/swc-${triple}`\n    const tarFileName = `${pkgName.substring(6)}-${version}.tgz`\n    const outputDirectory = path.join(bindingsDirectory, pkgName)\n\n    if (fs.existsSync(outputDirectory)) {\n      // if the package is already downloaded a different\n      // failure occurred than not being present\n      return\n    }\n\n    await fs.promises.mkdir(outputDirectory, { recursive: true })\n    await extractBinary(outputDirectory, pkgName, tarFileName)\n  }\n}\n\nexport async function downloadWasmSwc(\n  version: string,\n  wasmDirectory: string,\n  variant: 'nodejs' | 'web' = 'nodejs'\n) {\n  const pkgName = `@next/swc-wasm-${variant}`\n  const tarFileName = `${pkgName.substring(6)}-${version}.tgz`\n  const outputDirectory = path.join(wasmDirectory, pkgName)\n\n  if (fs.existsSync(outputDirectory)) {\n    // if the package is already downloaded a different\n    // failure occurred than not being present\n    return\n  }\n\n  await fs.promises.mkdir(outputDirectory, { recursive: true })\n  await extractBinary(outputDirectory, pkgName, tarFileName)\n}\n"], "names": ["downloadNativeNextSwc", "downloadWasmSwc", "WritableStream", "require", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "getCacheDirectory", "process", "env", "extractFromTar", "tar", "x", "file", "path", "join", "cwd", "strip", "fs", "existsSync", "Log", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "getRegistry", "downloadUrl", "fetch", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "close", "access", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "wasmDirectory", "variant"], "mappings": ";;;;;;;;;;;;;;;IA4GsBA,qBAAqB;eAArBA;;IAqBAC,eAAe;eAAfA;;;2DAjIP;6DACE;6DACI;4DACL;6BAIY;mCACM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJlC,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAMnC,MAAMC,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBC,IAAAA,oCAAiB,EACtC,YACAC,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBC,YAAG,CAACC,CAAC,CAAC;YACJC,MAAMC,aAAI,CAACC,IAAI,CAACT,gBAAgBD;YAChCW,KAAKb;YACLc,OAAO;QACT;IAEF,IAAI,CAACC,WAAE,CAACC,UAAU,CAACL,aAAI,CAACC,IAAI,CAACT,gBAAgBD,eAAe;QAC1De,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEjB,QAAQ,OAAO,EAAEE,gBAAgB;QACrE,MAAMY,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACjB,gBAAgB;YAAEkB,WAAW;QAAK;QAC1D,MAAMC,WAAWX,aAAI,CAACC,IAAI,CACxBT,gBACA,GAAGD,YAAY,MAAM,EAAEqB,KAAKC,GAAG,IAAI;QAGrC,MAAMC,WAAWC,IAAAA,wBAAW;QAE5B,MAAMC,cAAc,GAAGF,WAAWxB,QAAQ,GAAG,EAAEC,aAAa;QAE5D,MAAM0B,MAAMD,aAAaE,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBf,KAAIgB,KAAK,CAAC,CAAC,oCAAoC,EAAEN,aAAa;YAChE;YAEA,IAAI,CAACI,IAAI;gBACP,MAAM,qBAAqD,CAArD,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,EAAE,GAApD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAoD;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,qBAA2C,CAA3C,IAAIE,MAAM,mCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0C;YAClD;YACA,MAAME,mBAAmBrB,WAAE,CAACsB,iBAAiB,CAACf;YAC9C,OAAOU,KAAKM,MAAM,CAChB,IAAI1C,eAAe;gBACjB2C,OAAMC,KAAK;oBACT,OAAO,IAAIC,QAAc,CAACC,SAASC,SACjCP,iBAAiBG,KAAK,CAACC,OAAO,CAACP;4BAC7B,IAAIA,OAAO;gCACTU,OAAOV;gCACP;4BACF;4BAEAS;wBACF;gBAEJ;gBACAE;oBACE,OAAO,IAAIH,QAAc,CAACC,SAASC,SACjCP,iBAAiBQ,KAAK,CAAC,CAACX;4BACtB,IAAIA,OAAO;gCACTU,OAAOV;gCACP;4BACF;4BAEAS;wBACF;gBAEJ;YACF;QAEJ;QAEA,MAAM3B,WAAE,CAACI,QAAQ,CAAC0B,MAAM,CAACvB,UAAU,+BAA+B;;QAClE,MAAMP,WAAE,CAACI,QAAQ,CAAC2B,MAAM,CAACxB,UAAUX,aAAI,CAACC,IAAI,CAACT,gBAAgBD;IAC/D,OAAO;QACLe,KAAIC,IAAI,CAAC,CAAC,yBAAyB,EAAEjB,QAAQ,GAAG,CAAC;IACnD;IACA,MAAMM;IAEN,MAAMwC,aAAa,MAAMhC,WAAE,CAACI,QAAQ,CAAC6B,OAAO,CAAC7C;IAE7C,IAAI4C,WAAWE,MAAM,GAAGnD,uBAAuB;QAC7CiD,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAGnD,sBAAuB;YAClE,MAAMiB,WAAE,CAACI,QAAQ,CACdoC,MAAM,CAAC5C,aAAI,CAACC,IAAI,CAACT,gBAAgB4C,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEO,eAAe9D,sBACpB+D,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAM1D,UAAU,CAAC,UAAU,EAAE2D,QAAQ;QACrC,MAAM1D,cAAc,GAAGD,QAAQ4D,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAMzD,kBAAkBW,aAAI,CAACC,IAAI,CAAC8C,mBAAmBzD;QAErD,IAAIc,WAAE,CAACC,UAAU,CAAChB,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMe,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACpB,iBAAiB;YAAEqB,WAAW;QAAK;QAC3D,MAAMtB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEO,eAAeP,gBACpB8D,OAAe,EACfK,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAM9D,UAAU,CAAC,eAAe,EAAE8D,SAAS;IAC3C,MAAM7D,cAAc,GAAGD,QAAQ4D,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAMzD,kBAAkBW,aAAI,CAACC,IAAI,CAACkD,eAAe7D;IAEjD,IAAIc,WAAE,CAACC,UAAU,CAAChB,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMe,WAAE,CAACI,QAAQ,CAACC,KAAK,CAACpB,iBAAiB;QAAEqB,WAAW;IAAK;IAC3D,MAAMtB,cAAcC,iBAAiBC,SAASC;AAChD"}