"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    async checkWCTEligibility(address) {\n        try {\n            const response = await fetch(\"/api/wct-airdrop/\".concat(address), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            return {\n                address: data.address,\n                balance: data.eligibleAmount,\n                isEligible: data.isEligible,\n                eligibleAmount: data.eligibleAmount,\n                error: data.error\n            };\n        } catch (error) {\n            console.error('WCT Airdrop API Error:', error);\n            // Handle network errors or CORS issues\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: 'Network error - please check your connection'\n                };\n            }\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc29sYW5hLWFwaS50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLGdEQUFnRDtBQXlCekMsTUFBTUE7SUFDWCxNQUFNQyxvQkFBb0JDLE9BQWUsRUFBNkI7UUFDcEUsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxvQkFBNEIsT0FBUkYsVUFBVztnQkFDMURHLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsVUFBVTtvQkFDVixnQkFBZ0I7Z0JBQ2xCO1lBQ0Y7WUFFQSxJQUFJLENBQUNILFNBQVNJLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNLHVCQUF1QyxPQUFoQkwsU0FBU00sTUFBTTtZQUN4RDtZQUVBLE1BQU1DLE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtZQUVoQyxPQUFPO2dCQUNMVCxTQUFTUSxLQUFLUixPQUFPO2dCQUNyQlUsU0FBU0YsS0FBS0csY0FBYztnQkFDNUJDLFlBQVlKLEtBQUtJLFVBQVU7Z0JBQzNCRCxnQkFBZ0JILEtBQUtHLGNBQWM7Z0JBQ25DRSxPQUFPTCxLQUFLSyxLQUFLO1lBQ25CO1FBRUYsRUFBRSxPQUFPQSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1lBRXhDLHVDQUF1QztZQUN2QyxJQUFJQSxpQkFBaUJFLGFBQWFGLE1BQU1HLE9BQU8sQ0FBQ0MsUUFBUSxDQUFDLFVBQVU7Z0JBQ2pFLE9BQU87b0JBQ0xqQjtvQkFDQVUsU0FBUztvQkFDVEUsWUFBWTtvQkFDWkQsZ0JBQWdCO29CQUNoQkUsT0FBTztnQkFDVDtZQUNGO1lBRUEsT0FBTztnQkFDTGI7Z0JBQ0FVLFNBQVM7Z0JBQ1RFLFlBQVk7Z0JBQ1pELGdCQUFnQjtnQkFDaEJFLE9BQU9BLGlCQUFpQlAsUUFBUU8sTUFBTUcsT0FBTyxHQUFHO1lBQ2xEO1FBQ0Y7SUFDRjtJQUVBLE1BQU1FLDRCQUE0QkMsU0FBbUIsRUFBK0I7UUFDbEYsTUFBTUMsV0FBV0QsVUFBVUUsR0FBRyxDQUFDckIsQ0FBQUEsVUFBVyxJQUFJLENBQUNELG1CQUFtQixDQUFDQztRQUNuRSxPQUFPc0IsUUFBUUMsR0FBRyxDQUFDSDtJQUNyQjtJQUVBLGlDQUFpQztJQUNqQ0kscUJBQXFCeEIsT0FBZSxFQUFXO1FBQzdDLGlFQUFpRTtRQUNqRSxNQUFNeUIsY0FBYztRQUNwQixPQUFPQSxZQUFZQyxJQUFJLENBQUMxQjtJQUMxQjtBQUNGO0FBRU8sTUFBTTJCLGdCQUFnQixJQUFJN0IsZ0JBQWdCIiwic291cmNlcyI6WyJEOlxcY2hlY2VrciB3Y3Qgc29sYW5hIG9nXFxzb2xhbmEtYWlyZHJvcC1jaGVja2VyXFxzcmNcXGxpYlxcc29sYW5hLWFwaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBXYWxsZXRDb25uZWN0IFRva2VuIChXQ1QpIEFpcmRyb3AgQVBJIHNlcnZpY2VcbmV4cG9ydCBpbnRlcmZhY2UgV0NUQWlyZHJvcFJlc3VsdCB7XG4gIGFkZHJlc3M6IHN0cmluZztcbiAgYmFsYW5jZTogbnVtYmVyO1xuICBpc0VsaWdpYmxlOiBib29sZWFuO1xuICBlbGlnaWJsZUFtb3VudDogbnVtYmVyO1xuICBlcnJvcj86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBTb2xhbmFSUENSZXNwb25zZSB7XG4gIGpzb25ycGM6IHN0cmluZztcbiAgcmVzdWx0Pzoge1xuICAgIGNvbnRleHQ6IHtcbiAgICAgIGFwaVZlcnNpb246IHN0cmluZztcbiAgICAgIHNsb3Q6IG51bWJlcjtcbiAgICB9O1xuICAgIHZhbHVlOiBudW1iZXI7XG4gIH07XG4gIGVycm9yPzoge1xuICAgIGNvZGU6IG51bWJlcjtcbiAgICBtZXNzYWdlOiBzdHJpbmc7XG4gIH07XG4gIGlkOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjbGFzcyBXQ1RBaXJkcm9wQVBJIHtcbiAgYXN5bmMgY2hlY2tXQ1RFbGlnaWJpbGl0eShhZGRyZXNzOiBzdHJpbmcpOiBQcm9taXNlPFdDVEFpcmRyb3BSZXN1bHQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS93Y3QtYWlyZHJvcC8ke2FkZHJlc3N9YCwge1xuICAgICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9XG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IhIHN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGFkZHJlc3M6IGRhdGEuYWRkcmVzcyxcbiAgICAgICAgYmFsYW5jZTogZGF0YS5lbGlnaWJsZUFtb3VudCxcbiAgICAgICAgaXNFbGlnaWJsZTogZGF0YS5pc0VsaWdpYmxlLFxuICAgICAgICBlbGlnaWJsZUFtb3VudDogZGF0YS5lbGlnaWJsZUFtb3VudCxcbiAgICAgICAgZXJyb3I6IGRhdGEuZXJyb3JcbiAgICAgIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignV0NUIEFpcmRyb3AgQVBJIEVycm9yOicsIGVycm9yKTtcblxuICAgICAgLy8gSGFuZGxlIG5ldHdvcmsgZXJyb3JzIG9yIENPUlMgaXNzdWVzXG4gICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBUeXBlRXJyb3IgJiYgZXJyb3IubWVzc2FnZS5pbmNsdWRlcygnZmV0Y2gnKSkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIGFkZHJlc3MsXG4gICAgICAgICAgYmFsYW5jZTogMCxcbiAgICAgICAgICBpc0VsaWdpYmxlOiBmYWxzZSxcbiAgICAgICAgICBlbGlnaWJsZUFtb3VudDogMCxcbiAgICAgICAgICBlcnJvcjogJ05ldHdvcmsgZXJyb3IgLSBwbGVhc2UgY2hlY2sgeW91ciBjb25uZWN0aW9uJ1xuICAgICAgICB9O1xuICAgICAgfVxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBhZGRyZXNzLFxuICAgICAgICBiYWxhbmNlOiAwLFxuICAgICAgICBpc0VsaWdpYmxlOiBmYWxzZSxcbiAgICAgICAgZWxpZ2libGVBbW91bnQ6IDAsXG4gICAgICAgIGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yIG9jY3VycmVkJ1xuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBhc3luYyBjaGVja011bHRpcGxlV0NURWxpZ2liaWxpdHkoYWRkcmVzc2VzOiBzdHJpbmdbXSk6IFByb21pc2U8V0NUQWlyZHJvcFJlc3VsdFtdPiB7XG4gICAgY29uc3QgcHJvbWlzZXMgPSBhZGRyZXNzZXMubWFwKGFkZHJlc3MgPT4gdGhpcy5jaGVja1dDVEVsaWdpYmlsaXR5KGFkZHJlc3MpKTtcbiAgICByZXR1cm4gUHJvbWlzZS5hbGwocHJvbWlzZXMpO1xuICB9XG5cbiAgLy8gVmFsaWRhdGUgU29sYW5hIGFkZHJlc3MgZm9ybWF0XG4gIGlzVmFsaWRTb2xhbmFBZGRyZXNzKGFkZHJlc3M6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIC8vIEJhc2ljIHZhbGlkYXRpb24gZm9yIFNvbGFuYSBhZGRyZXNzIChiYXNlNTgsIDMyLTQ0IGNoYXJhY3RlcnMpXG4gICAgY29uc3QgYmFzZTU4UmVnZXggPSAvXlsxLTlBLUhKLU5QLVphLWttLXpdezMyLDQ0fSQvO1xuICAgIHJldHVybiBiYXNlNThSZWdleC50ZXN0KGFkZHJlc3MpO1xuICB9XG59XG5cbmV4cG9ydCBjb25zdCB3Y3RBaXJkcm9wQVBJID0gbmV3IFdDVEFpcmRyb3BBUEkoKTtcbiJdLCJuYW1lcyI6WyJXQ1RBaXJkcm9wQVBJIiwiY2hlY2tXQ1RFbGlnaWJpbGl0eSIsImFkZHJlc3MiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJkYXRhIiwianNvbiIsImJhbGFuY2UiLCJlbGlnaWJsZUFtb3VudCIsImlzRWxpZ2libGUiLCJlcnJvciIsImNvbnNvbGUiLCJUeXBlRXJyb3IiLCJtZXNzYWdlIiwiaW5jbHVkZXMiLCJjaGVja011bHRpcGxlV0NURWxpZ2liaWxpdHkiLCJhZGRyZXNzZXMiLCJwcm9taXNlcyIsIm1hcCIsIlByb21pc2UiLCJhbGwiLCJpc1ZhbGlkU29sYW5hQWRkcmVzcyIsImJhc2U1OFJlZ2V4IiwidGVzdCIsIndjdEFpcmRyb3BBUEkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/solana-api.ts\n"));

/***/ })

});