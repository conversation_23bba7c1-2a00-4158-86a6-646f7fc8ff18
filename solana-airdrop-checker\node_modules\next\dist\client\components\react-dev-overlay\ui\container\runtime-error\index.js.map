{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/container/runtime-error/index.tsx"], "sourcesContent": ["import { useMemo } from 'react'\nimport { CodeFrame } from '../../components/code-frame/code-frame'\nimport { CallStack } from '../../components/errors/call-stack/call-stack'\nimport { PSEUDO_HTML_DIFF_STYLES } from './component-stack-pseudo-html'\nimport {\n  useFrames,\n  type ReadyRuntimeError,\n} from '../../../utils/get-error-by-type'\n\nexport type RuntimeErrorProps = {\n  error: ReadyRuntimeError\n  dialogResizerRef: React.RefObject<HTMLDivElement | null>\n}\n\nexport function RuntimeError({ error, dialogResizerRef }: RuntimeErrorProps) {\n  const frames = useFrames(error)\n\n  const firstFrame = useMemo(() => {\n    const firstFirstPartyFrameIndex = frames.findIndex(\n      (entry) =>\n        !entry.ignored &&\n        Boolean(entry.originalCodeFrame) &&\n        Boolean(entry.originalStackFrame)\n    )\n\n    return frames[firstFirstPartyFrameIndex] ?? null\n  }, [frames])\n\n  return (\n    <>\n      {firstFrame && (\n        <CodeFrame\n          stackFrame={firstFrame.originalStackFrame!}\n          codeFrame={firstFrame.originalCodeFrame!}\n        />\n      )}\n\n      {frames.length > 0 && (\n        <CallStack dialogResizerRef={dialogResizerRef} frames={frames} />\n      )}\n    </>\n  )\n}\n\nexport const styles = `\n  ${PSEUDO_HTML_DIFF_STYLES}\n`\n"], "names": ["RuntimeError", "styles", "error", "dialogResizerRef", "frames", "useFrames", "firstFrame", "useMemo", "firstFirstPartyFrameIndex", "findIndex", "entry", "ignored", "Boolean", "originalCodeFrame", "originalStackFrame", "CodeFrame", "stackFrame", "codeFrame", "length", "CallStack", "PSEUDO_HTML_DIFF_STYLES"], "mappings": ";;;;;;;;;;;;;;;IAcgBA,YAAY;eAAZA;;IA8BHC,MAAM;eAANA;;;;uBA5CW;2BACE;2BACA;0CACc;gCAIjC;AAOA,SAASD,aAAa,KAA8C;IAA9C,IAAA,EAAEE,KAAK,EAAEC,gBAAgB,EAAqB,GAA9C;IAC3B,MAAMC,SAASC,IAAAA,yBAAS,EAACH;IAEzB,MAAMI,aAAaC,IAAAA,cAAO,EAAC;QACzB,MAAMC,4BAA4BJ,OAAOK,SAAS,CAChD,CAACC,QACC,CAACA,MAAMC,OAAO,IACdC,QAAQF,MAAMG,iBAAiB,KAC/BD,QAAQF,MAAMI,kBAAkB;YAG7BV;QAAP,OAAOA,CAAAA,oCAAAA,MAAM,CAACI,0BAA0B,YAAjCJ,oCAAqC;IAC9C,GAAG;QAACA;KAAO;IAEX,qBACE;;YACGE,4BACC,qBAACS,oBAAS;gBACRC,YAAYV,WAAWQ,kBAAkB;gBACzCG,WAAWX,WAAWO,iBAAiB;;YAI1CT,OAAOc,MAAM,GAAG,mBACf,qBAACC,oBAAS;gBAAChB,kBAAkBA;gBAAkBC,QAAQA;;;;AAI/D;AAEO,MAAMH,SAAS,AAAC,SACnBmB,iDAAuB,GAAC"}