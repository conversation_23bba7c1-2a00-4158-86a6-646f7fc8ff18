{"version": 3, "sources": ["../../../../src/build/segment-config/middleware/middleware-config.ts"], "sourcesContent": ["import picomatch from 'next/dist/compiled/picomatch'\nimport { z } from 'next/dist/compiled/zod'\nimport { tryToParsePath } from '../../../lib/try-to-parse-path'\nimport type { RouteHas } from '../../../lib/load-custom-routes'\n\nconst RouteHasSchema = z.discriminatedUnion('type', [\n  z\n    .object({\n      type: z.enum(['header', 'query', 'cookie']),\n      key: z.string({\n        required_error: 'key is required when type is header, query or cookie',\n      }),\n      value: z\n        .string({\n          invalid_type_error: 'value must be a string',\n        })\n        .optional(),\n    })\n    .strict(),\n  z\n    .object({\n      type: z.literal('host'),\n      value: z.string({\n        required_error: 'host must have a value',\n      }),\n    })\n    .strict(),\n])\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport const SourceSchema = z\n  .string({\n    required_error: 'source is required',\n  })\n  .max(4096, 'exceeds max built length of 4096 for route')\n  .superRefine((val, ctx) => {\n    if (!val.startsWith('/')) {\n      return ctx.addIssue({\n        code: z.ZodIssueCode.custom,\n        message: `source must start with /`,\n      })\n    }\n\n    const { error, regexStr } = tryToParsePath(val)\n\n    if (error || !regexStr) {\n      ctx.addIssue({\n        code: z.ZodIssueCode.custom,\n        message: `Invalid source '${val}': ${error.message}`,\n      })\n    }\n  })\n\nconst MiddlewareMatcherInputSchema = z\n  .object({\n    locale: z.union([z.literal(false), z.undefined()]).optional(),\n    has: z.array(RouteHasSchema).optional(),\n    missing: z.array(RouteHasSchema).optional(),\n    source: SourceSchema,\n  })\n  .strict()\n\nconst MiddlewareConfigMatcherInputSchema = z.union([\n  SourceSchema,\n  z.array(\n    z.union([SourceSchema, MiddlewareMatcherInputSchema], {\n      invalid_type_error: 'must be an array of strings or middleware matchers',\n    })\n  ),\n])\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport type MiddlewareConfigMatcherInput = z.infer<\n  typeof MiddlewareConfigMatcherInputSchema\n>\n\nconst GlobSchema = z.string().superRefine((val, ctx) => {\n  try {\n    picomatch(val)\n  } catch (err: any) {\n    ctx.addIssue({\n      code: z.ZodIssueCode.custom,\n      message: `Invalid glob pattern '${val}': ${err.message}`,\n    })\n  }\n})\n\n/**\n * @internal - required to exclude zod types from the build\n */\nexport const MiddlewareConfigInputSchema = z.object({\n  /**\n   * The matcher for the middleware.\n   */\n  matcher: MiddlewareConfigMatcherInputSchema.optional(),\n\n  /**\n   * The regions that the middleware should run in.\n   */\n  regions: z.union([z.string(), z.array(z.string())]).optional(),\n\n  /**\n   * A glob, or an array of globs, ignoring dynamic code evaluation for specific\n   * files. The globs are relative to your application root folder.\n   */\n  unstable_allowDynamic: z.union([GlobSchema, z.array(GlobSchema)]).optional(),\n})\n\nexport type MiddlewareConfigInput = {\n  /**\n   * The matcher for the middleware.\n   */\n  matcher?:\n    | string\n    | Array<\n        | {\n            locale?: false\n            has?: RouteHas[]\n            missing?: RouteHas[]\n            source: string\n          }\n        | string\n      >\n\n  /**\n   * The regions that the middleware should run in.\n   */\n  regions?: string | string[]\n\n  /**\n   * A glob, or an array of globs, ignoring dynamic code evaluation for specific\n   * files. The globs are relative to your application root folder.\n   */\n  unstable_allowDynamic?: string | string[]\n}\n\n/**\n * The keys of the configuration for a middleware.\n *\n * @internal - required to exclude zod types from the build\n */\nexport const MiddlewareConfigInputSchemaKeys =\n  MiddlewareConfigInputSchema.keyof().options\n"], "names": ["MiddlewareConfigInputSchema", "MiddlewareConfigInputSchemaKeys", "SourceSchema", "RouteHasSchema", "z", "discriminatedUnion", "object", "type", "enum", "key", "string", "required_error", "value", "invalid_type_error", "optional", "strict", "literal", "max", "superRefine", "val", "ctx", "startsWith", "addIssue", "code", "ZodIssueCode", "custom", "message", "error", "regexStr", "tryToParsePath", "MiddlewareMatcherInputSchema", "locale", "union", "undefined", "has", "array", "missing", "source", "MiddlewareConfigMatcherInputSchema", "GlobSchema", "picomatch", "err", "matcher", "regions", "unstable_allowDynamic", "keyof", "options"], "mappings": ";;;;;;;;;;;;;;;;IA8FaA,2BAA2B;eAA3BA;;IAmDAC,+BAA+B;eAA/BA;;IAjHAC,YAAY;eAAZA;;;kEAhCS;qBACJ;gCACa;;;;;;AAG/B,MAAMC,iBAAiBC,MAAC,CAACC,kBAAkB,CAAC,QAAQ;IAClDD,MAAC,CACEE,MAAM,CAAC;QACNC,MAAMH,MAAC,CAACI,IAAI,CAAC;YAAC;YAAU;YAAS;SAAS;QAC1CC,KAAKL,MAAC,CAACM,MAAM,CAAC;YACZC,gBAAgB;QAClB;QACAC,OAAOR,MAAC,CACLM,MAAM,CAAC;YACNG,oBAAoB;QACtB,GACCC,QAAQ;IACb,GACCC,MAAM;IACTX,MAAC,CACEE,MAAM,CAAC;QACNC,MAAMH,MAAC,CAACY,OAAO,CAAC;QAChBJ,OAAOR,MAAC,CAACM,MAAM,CAAC;YACdC,gBAAgB;QAClB;IACF,GACCI,MAAM;CACV;AAKM,MAAMb,eAAeE,MAAC,CAC1BM,MAAM,CAAC;IACNC,gBAAgB;AAClB,GACCM,GAAG,CAAC,MAAM,8CACVC,WAAW,CAAC,CAACC,KAAKC;IACjB,IAAI,CAACD,IAAIE,UAAU,CAAC,MAAM;QACxB,OAAOD,IAAIE,QAAQ,CAAC;YAClBC,MAAMnB,MAAC,CAACoB,YAAY,CAACC,MAAM;YAC3BC,SAAS,CAAC,wBAAwB,CAAC;QACrC;IACF;IAEA,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,8BAAc,EAACV;IAE3C,IAAIQ,SAAS,CAACC,UAAU;QACtBR,IAAIE,QAAQ,CAAC;YACXC,MAAMnB,MAAC,CAACoB,YAAY,CAACC,MAAM;YAC3BC,SAAS,CAAC,gBAAgB,EAAEP,IAAI,GAAG,EAAEQ,MAAMD,OAAO,EAAE;QACtD;IACF;AACF;AAEF,MAAMI,+BAA+B1B,MAAC,CACnCE,MAAM,CAAC;IACNyB,QAAQ3B,MAAC,CAAC4B,KAAK,CAAC;QAAC5B,MAAC,CAACY,OAAO,CAAC;QAAQZ,MAAC,CAAC6B,SAAS;KAAG,EAAEnB,QAAQ;IAC3DoB,KAAK9B,MAAC,CAAC+B,KAAK,CAAChC,gBAAgBW,QAAQ;IACrCsB,SAAShC,MAAC,CAAC+B,KAAK,CAAChC,gBAAgBW,QAAQ;IACzCuB,QAAQnC;AACV,GACCa,MAAM;AAET,MAAMuB,qCAAqClC,MAAC,CAAC4B,KAAK,CAAC;IACjD9B;IACAE,MAAC,CAAC+B,KAAK,CACL/B,MAAC,CAAC4B,KAAK,CAAC;QAAC9B;QAAc4B;KAA6B,EAAE;QACpDjB,oBAAoB;IACtB;CAEH;AASD,MAAM0B,aAAanC,MAAC,CAACM,MAAM,GAAGQ,WAAW,CAAC,CAACC,KAAKC;IAC9C,IAAI;QACFoB,IAAAA,kBAAS,EAACrB;IACZ,EAAE,OAAOsB,KAAU;QACjBrB,IAAIE,QAAQ,CAAC;YACXC,MAAMnB,MAAC,CAACoB,YAAY,CAACC,MAAM;YAC3BC,SAAS,CAAC,sBAAsB,EAAEP,IAAI,GAAG,EAAEsB,IAAIf,OAAO,EAAE;QAC1D;IACF;AACF;AAKO,MAAM1B,8BAA8BI,MAAC,CAACE,MAAM,CAAC;IAClD;;GAEC,GACDoC,SAASJ,mCAAmCxB,QAAQ;IAEpD;;GAEC,GACD6B,SAASvC,MAAC,CAAC4B,KAAK,CAAC;QAAC5B,MAAC,CAACM,MAAM;QAAIN,MAAC,CAAC+B,KAAK,CAAC/B,MAAC,CAACM,MAAM;KAAI,EAAEI,QAAQ;IAE5D;;;GAGC,GACD8B,uBAAuBxC,MAAC,CAAC4B,KAAK,CAAC;QAACO;QAAYnC,MAAC,CAAC+B,KAAK,CAACI;KAAY,EAAEzB,QAAQ;AAC5E;AAmCO,MAAMb,kCACXD,4BAA4B6C,KAAK,GAAGC,OAAO"}