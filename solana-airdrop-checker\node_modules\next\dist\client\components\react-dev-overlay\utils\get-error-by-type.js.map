{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/utils/get-error-by-type.ts"], "sourcesContent": ["import { ACTION_UNHANDLED_ERROR, ACTION_UNHANDLED_REJECTION } from '../shared'\nimport type { SupportedErrorEvent } from '../ui/container/runtime-error/render-error'\nimport { getOriginalStackFrames } from './stack-frame'\nimport type { OriginalStackFrame } from './stack-frame'\nimport type { ComponentStackFrame } from './parse-component-stack'\nimport { getErrorSource } from '../../../../shared/lib/error-source'\nimport React from 'react'\n\nexport type ReadyRuntimeError = {\n  id: number\n  runtime: true\n  error: Error & { environmentName?: string }\n  frames: OriginalStackFrame[] | (() => Promise<OriginalStackFrame[]>)\n  componentStackFrames?: ComponentStackFrame[]\n}\n\nexport const useFrames = (error: ReadyRuntimeError): OriginalStackFrame[] => {\n  if ('use' in React) {\n    const frames = error.frames\n\n    if (typeof frames !== 'function') {\n      throw new Error(\n        'Invariant: frames must be a function when the React version has React.use. This is a bug in Next.js.'\n      )\n    }\n\n    return React.use((frames as () => Promise<OriginalStackFrame[]>)())\n  } else {\n    if (!Array.isArray(error.frames)) {\n      throw new Error(\n        'Invariant: frames must be an array when the React version does not have React.use. This is a bug in Next.js.'\n      )\n    }\n\n    return error.frames\n  }\n}\n\nexport async function getErrorByType(\n  ev: SupportedErrorEvent,\n  isAppDir: boolean\n): Promise<ReadyRuntimeError> {\n  const { id, event } = ev\n  switch (event.type) {\n    case ACTION_UNHANDLED_ERROR:\n    case ACTION_UNHANDLED_REJECTION: {\n      const baseError = {\n        id,\n        runtime: true,\n        error: event.reason,\n      } as const\n\n      if ('use' in React) {\n        const readyRuntimeError: ReadyRuntimeError = {\n          ...baseError,\n          // createMemoizedPromise dedups calls to getOriginalStackFrames\n          frames: createMemoizedPromise(async () => {\n            return await getOriginalStackFrames(\n              event.frames,\n              getErrorSource(event.reason),\n              isAppDir\n            )\n          }),\n        }\n        if (event.type === ACTION_UNHANDLED_ERROR) {\n          readyRuntimeError.componentStackFrames = event.componentStackFrames\n        }\n        return readyRuntimeError\n      } else {\n        const readyRuntimeError: ReadyRuntimeError = {\n          ...baseError,\n          // createMemoizedPromise dedups calls to getOriginalStackFrames\n          frames: await getOriginalStackFrames(\n            event.frames,\n            getErrorSource(event.reason),\n            isAppDir\n          ),\n        }\n        if (event.type === ACTION_UNHANDLED_ERROR) {\n          readyRuntimeError.componentStackFrames = event.componentStackFrames\n        }\n        return readyRuntimeError\n      }\n    }\n    default: {\n      break\n    }\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const _: never = event\n  throw new Error('type system invariant violation')\n}\n\nfunction createMemoizedPromise<T>(\n  promiseFactory: () => Promise<T>\n): () => Promise<T> {\n  const cachedPromise = promiseFactory()\n  return function (): Promise<T> {\n    return cachedPromise\n  }\n}\n"], "names": ["getErrorByType", "useFrames", "error", "React", "frames", "Error", "use", "Array", "isArray", "ev", "isAppDir", "id", "event", "type", "ACTION_UNHANDLED_ERROR", "ACTION_UNHANDLED_REJECTION", "baseError", "runtime", "reason", "readyRuntimeError", "createMemoizedPromise", "getOriginalStackFrames", "getErrorSource", "componentStackFrames", "_", "promiseFactory", "cachedPromise"], "mappings": ";;;;;;;;;;;;;;;IAsCsBA,cAAc;eAAdA;;IAtBTC,SAAS;eAATA;;;;wBAhBsD;4BAE5B;6BAGR;gEACb;AAUX,MAAMA,YAAY,CAACC;IACxB,IAAI,SAASC,cAAK,EAAE;QAClB,MAAMC,SAASF,MAAME,MAAM;QAE3B,IAAI,OAAOA,WAAW,YAAY;YAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,yGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOF,cAAK,CAACG,GAAG,CAAC,AAACF;IACpB,OAAO;QACL,IAAI,CAACG,MAAMC,OAAO,CAACN,MAAME,MAAM,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIC,MACR,iHADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOH,MAAME,MAAM;IACrB;AACF;AAEO,eAAeJ,eACpBS,EAAuB,EACvBC,QAAiB;IAEjB,MAAM,EAAEC,EAAE,EAAEC,KAAK,EAAE,GAAGH;IACtB,OAAQG,MAAMC,IAAI;QAChB,KAAKC,8BAAsB;QAC3B,KAAKC,kCAA0B;YAAE;gBAC/B,MAAMC,YAAY;oBAChBL;oBACAM,SAAS;oBACTf,OAAOU,MAAMM,MAAM;gBACrB;gBAEA,IAAI,SAASf,cAAK,EAAE;oBAClB,MAAMgB,oBAAuC;wBAC3C,GAAGH,SAAS;wBACZ,+DAA+D;wBAC/DZ,QAAQgB,sBAAsB;4BAC5B,OAAO,MAAMC,IAAAA,kCAAsB,EACjCT,MAAMR,MAAM,EACZkB,IAAAA,2BAAc,EAACV,MAAMM,MAAM,GAC3BR;wBAEJ;oBACF;oBACA,IAAIE,MAAMC,IAAI,KAAKC,8BAAsB,EAAE;wBACzCK,kBAAkBI,oBAAoB,GAAGX,MAAMW,oBAAoB;oBACrE;oBACA,OAAOJ;gBACT,OAAO;oBACL,MAAMA,oBAAuC;wBAC3C,GAAGH,SAAS;wBACZ,+DAA+D;wBAC/DZ,QAAQ,MAAMiB,IAAAA,kCAAsB,EAClCT,MAAMR,MAAM,EACZkB,IAAAA,2BAAc,EAACV,MAAMM,MAAM,GAC3BR;oBAEJ;oBACA,IAAIE,MAAMC,IAAI,KAAKC,8BAAsB,EAAE;wBACzCK,kBAAkBI,oBAAoB,GAAGX,MAAMW,oBAAoB;oBACrE;oBACA,OAAOJ;gBACT;YACF;QACA;YAAS;gBACP;YACF;IACF;IACA,6DAA6D;IAC7D,MAAMK,IAAWZ;IACjB,MAAM,qBAA4C,CAA5C,IAAIP,MAAM,oCAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA2C;AACnD;AAEA,SAASe,sBACPK,cAAgC;IAEhC,MAAMC,gBAAgBD;IACtB,OAAO;QACL,OAAOC;IACT;AACF"}