/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wct-airdrop/[address]/route";
exports.ids = ["app/api/wct-airdrop/[address]/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&page=%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute.ts&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&page=%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute.ts&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_checekr_wct_solana_og_solana_airdrop_checker_src_app_api_wct_airdrop_address_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wct-airdrop/[address]/route.ts */ \"(rsc)/./src/app/api/wct-airdrop/[address]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wct-airdrop/[address]/route\",\n        pathname: \"/api/wct-airdrop/[address]\",\n        filename: \"route\",\n        bundlePath: \"app/api/wct-airdrop/[address]/route\"\n    },\n    resolvedPagePath: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\api\\\\wct-airdrop\\\\[address]\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_checekr_wct_solana_og_solana_airdrop_checker_src_app_api_wct_airdrop_address_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&page=%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute.ts&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/wct-airdrop/[address]/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/wct-airdrop/[address]/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET(request, { params }) {\n    try {\n        const { address } = params;\n        if (!address) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Address parameter is required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate Solana address format\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        if (!base58Regex.test(address)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid Solana address format'\n            }, {\n                status: 400\n            });\n        }\n        // Fetch from WalletConnect API\n        const response = await fetch(`https://api.walletconnect.network/airdrop/solana/${address}`, {\n            method: 'GET',\n            headers: {\n                'Accept': 'application/json',\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            // Handle different error cases\n            if (response.status === 404) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    address,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: null\n                });\n            }\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        // Check if recipient not found (not eligible)\n        if (data.error && data.error.includes('not found')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                address,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: null\n            });\n        }\n        // If there's any other error\n        if (data.error) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                address,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: data.error\n            });\n        }\n        // Extract eligible amount from response (hex format)\n        let eligibleAmount = 0;\n        if (data.amount) {\n            // Convert hex amount to decimal and then to WCT tokens\n            const hexAmount = data.amount.startsWith('0x') ? data.amount : '0x' + data.amount;\n            const decimalAmount = parseInt(hexAmount, 16);\n            // Convert from smallest unit to WCT tokens (9 decimals like SOL)\n            eligibleAmount = decimalAmount / Math.pow(10, 9);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address,\n            isEligible: eligibleAmount > 0,\n            eligibleAmount,\n            error: null,\n            // Include original data for debugging\n            originalData: data\n        });\n    } catch (error) {\n        console.error('WCT Airdrop API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            address: params.address,\n            isEligible: false,\n            eligibleAmount: 0,\n            error: error instanceof Error ? error.message : 'Unknown error occurred'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wct-airdrop/[address]/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&page=%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwct-airdrop%2F%5Baddress%5D%2Froute.ts&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();