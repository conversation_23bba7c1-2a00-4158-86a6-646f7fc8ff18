{"version": 3, "sources": ["../../../src/shared/lib/dset.js"], "sourcesContent": ["/*\nThe MIT License (MIT)\n\nCopyright (c) <PERSON> <<EMAIL>> (lukeed.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n// This file is based on https://github.com/lukeed/dset/blob/v3.1.3/src/index.js\n// It's been edited for the needs of this script\n// See the LICENSE at the top of the file\n\nexport function dset(obj, keys, val) {\n  keys.split && (keys = keys.split('.'))\n  var i = 0,\n    l = keys.length,\n    t = obj,\n    x,\n    k\n  while (i < l) {\n    k = keys[i++]\n    if (k === '__proto__' || k === 'constructor' || k === 'prototype') break\n    t = t[k] =\n      i === l\n        ? val\n        : typeof (x = t[k]) === typeof keys\n          ? x\n          : keys[i] * 0 !== 0 || !!~('' + keys[i]).indexOf('.')\n            ? {}\n            : []\n  }\n}\n"], "names": ["dset", "obj", "keys", "val", "split", "i", "l", "length", "t", "x", "k", "indexOf"], "mappings": "AAAA;;;;;;;;;;AAUA,GAEA,gFAAgF;AAChF,gDAAgD;AAChD,yCAAyC;AAEzC,OAAO,SAASA,KAAKC,GAAG,EAAEC,IAAI,EAAEC,GAAG;IACjCD,KAAKE,KAAK,IAAKF,CAAAA,OAAOA,KAAKE,KAAK,CAAC,IAAG;IACpC,IAAIC,IAAI,GACNC,IAAIJ,KAAKK,MAAM,EACfC,IAAIP,KACJQ,GACAC;IACF,MAAOL,IAAIC,EAAG;QACZI,IAAIR,IAAI,CAACG,IAAI;QACb,IAAIK,MAAM,eAAeA,MAAM,iBAAiBA,MAAM,aAAa;QACnEF,IAAIA,CAAC,CAACE,EAAE,GACNL,MAAMC,IACFH,MACA,OAAQM,CAAAA,IAAID,CAAC,CAACE,EAAE,AAAD,MAAO,OAAOR,OAC3BO,IACAP,IAAI,CAACG,EAAE,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC,AAAC,CAAA,KAAKH,IAAI,CAACG,EAAE,AAAD,EAAGM,OAAO,CAAC,OAC7C,CAAC,IACD,EAAE;IACd;AACF"}