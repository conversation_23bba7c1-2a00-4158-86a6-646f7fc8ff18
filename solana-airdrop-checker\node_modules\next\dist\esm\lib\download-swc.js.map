{"version": 3, "sources": ["../../src/lib/download-swc.ts"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\nimport * as Log from '../build/output/log'\nimport tar from 'next/dist/compiled/tar'\nconst { WritableStream } = require('node:stream/web') as {\n  WritableStream: typeof global.WritableStream\n}\nimport { getRegistry } from './helpers/get-registry'\nimport { getCacheDirectory } from './helpers/get-cache-directory'\n\nconst MAX_VERSIONS_TO_CACHE = 8\n\nasync function extractBinary(\n  outputDirectory: string,\n  pkgName: string,\n  tarFileName: string\n) {\n  const cacheDirectory = getCacheDirectory(\n    'next-swc',\n    process.env['NEXT_SWC_PATH']\n  )\n\n  const extractFromTar = () =>\n    tar.x({\n      file: path.join(cacheDirectory, tarFileName),\n      cwd: outputDirectory,\n      strip: 1,\n    })\n\n  if (!fs.existsSync(path.join(cacheDirectory, tarFileName))) {\n    Log.info(`Downloading swc package ${pkgName}... to ${cacheDirectory}`)\n    await fs.promises.mkdir(cacheDirectory, { recursive: true })\n    const tempFile = path.join(\n      cacheDirectory,\n      `${tarFileName}.temp-${Date.now()}`\n    )\n\n    const registry = getRegistry()\n\n    const downloadUrl = `${registry}${pkgName}/-/${tarFileName}`\n\n    await fetch(downloadUrl).then((res) => {\n      const { ok, body } = res\n      if (!ok || !body) {\n        Log.error(`Failed to download swc package from ${downloadUrl}`)\n      }\n\n      if (!ok) {\n        throw new Error(`request failed with status ${res.status}`)\n      }\n      if (!body) {\n        throw new Error('request failed with empty body')\n      }\n      const cacheWriteStream = fs.createWriteStream(tempFile)\n      return body.pipeTo(\n        new WritableStream({\n          write(chunk) {\n            return new Promise<void>((resolve, reject) =>\n              cacheWriteStream.write(chunk, (error) => {\n                if (error) {\n                  reject(error)\n                  return\n                }\n\n                resolve()\n              })\n            )\n          },\n          close() {\n            return new Promise<void>((resolve, reject) =>\n              cacheWriteStream.close((error) => {\n                if (error) {\n                  reject(error)\n                  return\n                }\n\n                resolve()\n              })\n            )\n          },\n        })\n      )\n    })\n\n    await fs.promises.access(tempFile) // ensure the temp file existed\n    await fs.promises.rename(tempFile, path.join(cacheDirectory, tarFileName))\n  } else {\n    Log.info(`Using cached swc package ${pkgName}...`)\n  }\n  await extractFromTar()\n\n  const cacheFiles = await fs.promises.readdir(cacheDirectory)\n\n  if (cacheFiles.length > MAX_VERSIONS_TO_CACHE) {\n    cacheFiles.sort((a, b) => {\n      if (a.length < b.length) return -1\n      return a.localeCompare(b)\n    })\n\n    // prune oldest versions in cache\n    for (let i = 0; i++; i < cacheFiles.length - MAX_VERSIONS_TO_CACHE) {\n      await fs.promises\n        .unlink(path.join(cacheDirectory, cacheFiles[i]))\n        .catch(() => {})\n    }\n  }\n}\n\nexport async function downloadNativeNextSwc(\n  version: string,\n  bindingsDirectory: string,\n  triplesABI: Array<string>\n) {\n  for (const triple of triplesABI) {\n    const pkgName = `@next/swc-${triple}`\n    const tarFileName = `${pkgName.substring(6)}-${version}.tgz`\n    const outputDirectory = path.join(bindingsDirectory, pkgName)\n\n    if (fs.existsSync(outputDirectory)) {\n      // if the package is already downloaded a different\n      // failure occurred than not being present\n      return\n    }\n\n    await fs.promises.mkdir(outputDirectory, { recursive: true })\n    await extractBinary(outputDirectory, pkgName, tarFileName)\n  }\n}\n\nexport async function downloadWasmSwc(\n  version: string,\n  wasmDirectory: string,\n  variant: 'nodejs' | 'web' = 'nodejs'\n) {\n  const pkgName = `@next/swc-wasm-${variant}`\n  const tarFileName = `${pkgName.substring(6)}-${version}.tgz`\n  const outputDirectory = path.join(wasmDirectory, pkgName)\n\n  if (fs.existsSync(outputDirectory)) {\n    // if the package is already downloaded a different\n    // failure occurred than not being present\n    return\n  }\n\n  await fs.promises.mkdir(outputDirectory, { recursive: true })\n  await extractBinary(outputDirectory, pkgName, tarFileName)\n}\n"], "names": ["fs", "path", "Log", "tar", "WritableStream", "require", "getRegistry", "getCacheDirectory", "MAX_VERSIONS_TO_CACHE", "extractBinary", "outputDirectory", "pkgName", "tarFileName", "cacheDirectory", "process", "env", "extractFromTar", "x", "file", "join", "cwd", "strip", "existsSync", "info", "promises", "mkdir", "recursive", "tempFile", "Date", "now", "registry", "downloadUrl", "fetch", "then", "res", "ok", "body", "error", "Error", "status", "cacheWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "close", "access", "rename", "cacheFiles", "readdir", "length", "sort", "a", "b", "localeCompare", "i", "unlink", "catch", "downloadNativeNextSwc", "version", "bindingsDirectory", "triplesABI", "triple", "substring", "downloadWasmSwc", "wasmDirectory", "variant"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,YAAYC,SAAS,sBAAqB;AAC1C,OAAOC,SAAS,yBAAwB;AACxC,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAGnC,SAASC,WAAW,QAAQ,yBAAwB;AACpD,SAASC,iBAAiB,QAAQ,gCAA+B;AAEjE,MAAMC,wBAAwB;AAE9B,eAAeC,cACbC,eAAuB,EACvBC,OAAe,EACfC,WAAmB;IAEnB,MAAMC,iBAAiBN,kBACrB,YACAO,QAAQC,GAAG,CAAC,gBAAgB;IAG9B,MAAMC,iBAAiB,IACrBb,IAAIc,CAAC,CAAC;YACJC,MAAMjB,KAAKkB,IAAI,CAACN,gBAAgBD;YAChCQ,KAAKV;YACLW,OAAO;QACT;IAEF,IAAI,CAACrB,GAAGsB,UAAU,CAACrB,KAAKkB,IAAI,CAACN,gBAAgBD,eAAe;QAC1DV,IAAIqB,IAAI,CAAC,CAAC,wBAAwB,EAAEZ,QAAQ,OAAO,EAAEE,gBAAgB;QACrE,MAAMb,GAAGwB,QAAQ,CAACC,KAAK,CAACZ,gBAAgB;YAAEa,WAAW;QAAK;QAC1D,MAAMC,WAAW1B,KAAKkB,IAAI,CACxBN,gBACA,GAAGD,YAAY,MAAM,EAAEgB,KAAKC,GAAG,IAAI;QAGrC,MAAMC,WAAWxB;QAEjB,MAAMyB,cAAc,GAAGD,WAAWnB,QAAQ,GAAG,EAAEC,aAAa;QAE5D,MAAMoB,MAAMD,aAAaE,IAAI,CAAC,CAACC;YAC7B,MAAM,EAAEC,EAAE,EAAEC,IAAI,EAAE,GAAGF;YACrB,IAAI,CAACC,MAAM,CAACC,MAAM;gBAChBlC,IAAImC,KAAK,CAAC,CAAC,oCAAoC,EAAEN,aAAa;YAChE;YAEA,IAAI,CAACI,IAAI;gBACP,MAAM,qBAAqD,CAArD,IAAIG,MAAM,CAAC,2BAA2B,EAAEJ,IAAIK,MAAM,EAAE,GAApD,qBAAA;2BAAA;gCAAA;kCAAA;gBAAoD;YAC5D;YACA,IAAI,CAACH,MAAM;gBACT,MAAM,qBAA2C,CAA3C,IAAIE,MAAM,mCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0C;YAClD;YACA,MAAME,mBAAmBxC,GAAGyC,iBAAiB,CAACd;YAC9C,OAAOS,KAAKM,MAAM,CAChB,IAAItC,eAAe;gBACjBuC,OAAMC,KAAK;oBACT,OAAO,IAAIC,QAAc,CAACC,SAASC,SACjCP,iBAAiBG,KAAK,CAACC,OAAO,CAACP;4BAC7B,IAAIA,OAAO;gCACTU,OAAOV;gCACP;4BACF;4BAEAS;wBACF;gBAEJ;gBACAE;oBACE,OAAO,IAAIH,QAAc,CAACC,SAASC,SACjCP,iBAAiBQ,KAAK,CAAC,CAACX;4BACtB,IAAIA,OAAO;gCACTU,OAAOV;gCACP;4BACF;4BAEAS;wBACF;gBAEJ;YACF;QAEJ;QAEA,MAAM9C,GAAGwB,QAAQ,CAACyB,MAAM,CAACtB,UAAU,+BAA+B;;QAClE,MAAM3B,GAAGwB,QAAQ,CAAC0B,MAAM,CAACvB,UAAU1B,KAAKkB,IAAI,CAACN,gBAAgBD;IAC/D,OAAO;QACLV,IAAIqB,IAAI,CAAC,CAAC,yBAAyB,EAAEZ,QAAQ,GAAG,CAAC;IACnD;IACA,MAAMK;IAEN,MAAMmC,aAAa,MAAMnD,GAAGwB,QAAQ,CAAC4B,OAAO,CAACvC;IAE7C,IAAIsC,WAAWE,MAAM,GAAG7C,uBAAuB;QAC7C2C,WAAWG,IAAI,CAAC,CAACC,GAAGC;YAClB,IAAID,EAAEF,MAAM,GAAGG,EAAEH,MAAM,EAAE,OAAO,CAAC;YACjC,OAAOE,EAAEE,aAAa,CAACD;QACzB;QAEA,iCAAiC;QACjC,IAAK,IAAIE,IAAI,GAAGA,KAAKA,IAAIP,WAAWE,MAAM,GAAG7C,sBAAuB;YAClE,MAAMR,GAAGwB,QAAQ,CACdmC,MAAM,CAAC1D,KAAKkB,IAAI,CAACN,gBAAgBsC,UAAU,CAACO,EAAE,GAC9CE,KAAK,CAAC,KAAO;QAClB;IACF;AACF;AAEA,OAAO,eAAeC,sBACpBC,OAAe,EACfC,iBAAyB,EACzBC,UAAyB;IAEzB,KAAK,MAAMC,UAAUD,WAAY;QAC/B,MAAMrD,UAAU,CAAC,UAAU,EAAEsD,QAAQ;QACrC,MAAMrD,cAAc,GAAGD,QAAQuD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;QAC5D,MAAMpD,kBAAkBT,KAAKkB,IAAI,CAAC4C,mBAAmBpD;QAErD,IAAIX,GAAGsB,UAAU,CAACZ,kBAAkB;YAClC,mDAAmD;YACnD,0CAA0C;YAC1C;QACF;QAEA,MAAMV,GAAGwB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;YAAEgB,WAAW;QAAK;QAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;IAChD;AACF;AAEA,OAAO,eAAeuD,gBACpBL,OAAe,EACfM,aAAqB,EACrBC,UAA4B,QAAQ;IAEpC,MAAM1D,UAAU,CAAC,eAAe,EAAE0D,SAAS;IAC3C,MAAMzD,cAAc,GAAGD,QAAQuD,SAAS,CAAC,GAAG,CAAC,EAAEJ,QAAQ,IAAI,CAAC;IAC5D,MAAMpD,kBAAkBT,KAAKkB,IAAI,CAACiD,eAAezD;IAEjD,IAAIX,GAAGsB,UAAU,CAACZ,kBAAkB;QAClC,mDAAmD;QACnD,0CAA0C;QAC1C;IACF;IAEA,MAAMV,GAAGwB,QAAQ,CAACC,KAAK,CAACf,iBAAiB;QAAEgB,WAAW;IAAK;IAC3D,MAAMjB,cAAcC,iBAAiBC,SAASC;AAChD"}