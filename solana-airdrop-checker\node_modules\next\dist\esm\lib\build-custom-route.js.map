{"version": 3, "sources": ["../../src/lib/build-custom-route.ts"], "sourcesContent": ["import { pathToRegexp } from 'next/dist/compiled/path-to-regexp'\nimport type {\n  ManifestHeaderRoute,\n  ManifestRedirectRoute,\n  ManifestRewriteRoute,\n} from '../build'\nimport {\n  normalizeRouteRegex,\n  type Header,\n  type Redirect,\n  type Rewrite,\n  type RouteType,\n} from './load-custom-routes'\nimport { getRedirectStatus, modifyRouteRegex } from './redirect-status'\n\nexport function buildCustomRoute(\n  type: 'header',\n  route: Header\n): ManifestHeaderRoute\nexport function buildCustomRoute(\n  type: 'rewrite',\n  route: Rewrite\n): ManifestRewriteRoute\nexport function buildCustomRoute(\n  type: 'redirect',\n  route: Redirect,\n  restrictedRedirectPaths: string[]\n): ManifestRedirectRoute\nexport function buildCustomRoute(\n  type: RouteType,\n  route: Redirect | Rewrite | Header,\n  restrictedRedirectPaths?: string[]\n): ManifestHeaderRoute | ManifestRewriteRoute | ManifestRedirectRoute {\n  const compiled = pathToRegexp(route.source, [], {\n    strict: true,\n    sensitive: false,\n    delimiter: '/', // default is `/#?`, but Next does not pass query info\n  })\n\n  let source = compiled.source\n  if (!route.internal) {\n    source = modifyRouteRegex(\n      source,\n      type === 'redirect' ? restrictedRedirectPaths : undefined\n    )\n  }\n\n  const regex = normalizeRouteRegex(source)\n\n  if (type !== 'redirect') {\n    return { ...route, regex }\n  }\n\n  return {\n    ...route,\n    statusCode: getRedirectStatus(route as Redirect),\n    permanent: undefined,\n    regex,\n  }\n}\n"], "names": ["pathToRegexp", "normalizeRouteRegex", "getRedirectStatus", "modifyRouteRegex", "buildCustomRoute", "type", "route", "restrictedRedirectPaths", "compiled", "source", "strict", "sensitive", "delimiter", "internal", "undefined", "regex", "statusCode", "permanent"], "mappings": "AAAA,SAASA,YAAY,QAAQ,oCAAmC;AAMhE,SACEC,mBAAmB,QAKd,uBAAsB;AAC7B,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,oBAAmB;AAevE,OAAO,SAASC,iBACdC,IAAe,EACfC,KAAkC,EAClCC,uBAAkC;IAElC,MAAMC,WAAWR,aAAaM,MAAMG,MAAM,EAAE,EAAE,EAAE;QAC9CC,QAAQ;QACRC,WAAW;QACXC,WAAW;IACb;IAEA,IAAIH,SAASD,SAASC,MAAM;IAC5B,IAAI,CAACH,MAAMO,QAAQ,EAAE;QACnBJ,SAASN,iBACPM,QACAJ,SAAS,aAAaE,0BAA0BO;IAEpD;IAEA,MAAMC,QAAQd,oBAAoBQ;IAElC,IAAIJ,SAAS,YAAY;QACvB,OAAO;YAAE,GAAGC,KAAK;YAAES;QAAM;IAC3B;IAEA,OAAO;QACL,GAAGT,KAAK;QACRU,YAAYd,kBAAkBI;QAC9BW,WAAWH;QACXC;IACF;AACF"}