"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    async checkWCTEligibility(address) {\n        try {\n            const response = await fetch(\"/api/wct-airdrop/\".concat(address), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response) {\n                throw new Error('No response received from server');\n            }\n            if (!response.ok) {\n                // Try to get error message from response\n                try {\n                    const errorData = await response.json();\n                    if (errorData.error && errorData.error.includes('not found')) {\n                        return {\n                            address,\n                            balance: 0,\n                            isEligible: false,\n                            eligibleAmount: 0,\n                            error: undefined\n                        };\n                    }\n                } catch (e) {\n                // If can't parse JSON, continue with generic error\n                }\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            // Check if recipient not found (not eligible)\n            if (data.error && data.error.includes('not found')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: undefined\n                };\n            }\n            // If there's any other error\n            if (data.error) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: data.error\n                };\n            }\n            // Extract eligible amount from response (hex format)\n            let eligibleAmount = 0;\n            if (data.amount) {\n                // Convert hex amount to decimal and then to WCT tokens\n                const hexAmount = data.amount.startsWith('0x') ? data.amount : '0x' + data.amount;\n                const decimalAmount = parseInt(hexAmount, 16);\n                // Convert from smallest unit to WCT tokens (9 decimals like SOL)\n                eligibleAmount = decimalAmount / Math.pow(10, 9);\n            }\n            return {\n                address,\n                balance: eligibleAmount,\n                isEligible: eligibleAmount > 0,\n                eligibleAmount,\n                error: undefined\n            };\n        } catch (error) {\n            console.error('WCT Airdrop API Error:', error);\n            // Handle network errors or CORS issues\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: 'Network error - please check your connection'\n                };\n            }\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/solana-api.ts\n"));

/***/ })

});