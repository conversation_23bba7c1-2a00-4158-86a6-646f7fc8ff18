{"version": 3, "sources": ["../../src/server/load-default-error-components.ts"], "sourcesContent": ["import type {\n  AppType,\n  DocumentType,\n  NextComponentType,\n} from '../shared/lib/utils'\nimport type { ClientReferenceManifest } from '../build/webpack/plugins/flight-manifest-plugin'\nimport type {\n  PageConfig,\n  GetStaticPaths,\n  GetServerSideProps,\n  GetStaticProps,\n} from '../types'\nimport type { RouteModule } from './route-modules/route-module'\nimport type { BuildManifest } from './get-page-files'\n\nimport { BUILD_MANIFEST } from '../shared/lib/constants'\nimport { join } from 'path'\nimport { interopDefault } from '../lib/interop-default'\nimport { getTracer } from './lib/trace/tracer'\nimport { LoadComponentsSpan } from './lib/trace/constants'\nimport { loadManifestWithRetries } from './load-components'\nexport type ManifestItem = {\n  id: number | string\n  files: string[]\n}\n\nexport type ReactLoadableManifest = { [moduleId: string]: ManifestItem }\n\nexport type LoadComponentsReturnType = {\n  Component: NextComponentType\n  pageConfig: PageConfig\n  buildManifest: BuildManifest\n  subresourceIntegrityManifest?: Record<string, string>\n  reactLoadableManifest: ReactLoadableManifest\n  clientReferenceManifest?: ClientReferenceManifest\n  serverActionsManifest?: any\n  Document: DocumentType\n  App: AppType\n  getStaticProps?: GetStaticProps\n  getStaticPaths?: GetStaticPaths\n  getServerSideProps?: GetServerSideProps\n  ComponentMod: any\n  routeModule: RouteModule\n  isAppPath?: boolean\n  page: string\n}\n\nasync function loadDefaultErrorComponentsImpl(\n  distDir: string\n): Promise<LoadComponentsReturnType> {\n  const Document = interopDefault(require('next/dist/pages/_document'))\n  const AppMod = require('next/dist/pages/_app')\n  const App = interopDefault(AppMod)\n\n  // Load the compiled route module for this builtin error.\n  // TODO: (wyattjoh) replace this with just exporting the route module when the transition is complete\n  const ComponentMod =\n    require('./route-modules/pages/builtin/_error') as typeof import('./route-modules/pages/builtin/_error')\n  const Component = ComponentMod.routeModule.userland.default\n\n  return {\n    App,\n    Document,\n    Component,\n    pageConfig: {},\n    buildManifest: (await loadManifestWithRetries(\n      join(distDir, `fallback-${BUILD_MANIFEST}`)\n    )) as BuildManifest,\n    reactLoadableManifest: {},\n    ComponentMod,\n    page: '/_error',\n    routeModule: ComponentMod.routeModule,\n  }\n}\nexport const loadDefaultErrorComponents = getTracer().wrap(\n  LoadComponentsSpan.loadDefaultErrorComponents,\n  loadDefaultErrorComponentsImpl\n)\n"], "names": ["loadDefaultErrorComponents", "loadDefaultErrorComponentsImpl", "distDir", "Document", "interopDefault", "require", "AppMod", "App", "ComponentMod", "Component", "routeModule", "userland", "default", "pageConfig", "buildManifest", "loadManifestWithRetries", "join", "BUILD_MANIFEST", "reactLoadableManifest", "page", "getTracer", "wrap", "LoadComponentsSpan"], "mappings": ";;;;+BA0EaA;;;eAAAA;;;2BA3DkB;sBACV;gCACU;wBACL;4BACS;gCACK;AA2BxC,eAAeC,+BACbC,OAAe;IAEf,MAAMC,WAAWC,IAAAA,8BAAc,EAACC,QAAQ;IACxC,MAAMC,SAASD,QAAQ;IACvB,MAAME,MAAMH,IAAAA,8BAAc,EAACE;IAE3B,yDAAyD;IACzD,qGAAqG;IACrG,MAAME,eACJH,QAAQ;IACV,MAAMI,YAAYD,aAAaE,WAAW,CAACC,QAAQ,CAACC,OAAO;IAE3D,OAAO;QACLL;QACAJ;QACAM;QACAI,YAAY,CAAC;QACbC,eAAgB,MAAMC,IAAAA,uCAAuB,EAC3CC,IAAAA,UAAI,EAACd,SAAS,CAAC,SAAS,EAAEe,yBAAc,EAAE;QAE5CC,uBAAuB,CAAC;QACxBV;QACAW,MAAM;QACNT,aAAaF,aAAaE,WAAW;IACvC;AACF;AACO,MAAMV,6BAA6BoB,IAAAA,iBAAS,IAAGC,IAAI,CACxDC,8BAAkB,CAACtB,0BAA0B,EAC7CC"}