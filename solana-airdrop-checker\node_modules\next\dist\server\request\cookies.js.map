{"version": 3, "sources": ["../../../src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  type ResponseCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\nimport { getExpectedRequestStore } from '../app-render/work-unit-async-storage.external'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * In this version of Next.js `cookies()` returns a Promise however you can still reference the properties of the underlying cookies object\n * synchronously to facilitate migration. The `UnsafeUnwrappedCookies` type is added to your code by a codemod that attempts to automatically\n * updates callsites to reflect the new Promise return type. There are some cases where `cookies()` cannot be automatically converted, namely\n * when it is used inside a synchronous function and we can't be sure the function can be made async automatically. In these cases we add an\n * explicit type case to `UnsafeUnwrappedCookies` to enable typescript to allow for the synchronous usage only where it is actually necessary.\n *\n * You should should update these callsites to either be async functions where the `cookies()` value can be awaited or you should call `cookies()`\n * from outside and await the return value before passing it into this function.\n *\n * You can find instances that require manual migration by searching for `UnsafeUnwrappedCookies` in your codebase or by search for a comment that\n * starts with `@next-codemod-error`.\n *\n * In a future version of Next.js `cookies()` will only return a Promise and you will not be able to access the underlying cookies object directly\n * without awaiting the return value first. When this change happens the type `UnsafeUnwrappedCookies` will be updated to reflect that is it no longer\n * usable.\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedCookies = ReadonlyRequestCookies\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \"cookies\" inside \"after(...)\". This is not supported. If you need this data inside an \"after\" callback, use \"cookies\" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedExoticCookies(underlyingCookies)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"cookies\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"cookies\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We don't track dynamic access here because access will be tracked when you access\n        // one of the properties of the cookies object.\n        return makeDynamicallyTrackedExoticCookies(\n          workStore.route,\n          workUnitStore\n        )\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We are prerendering with PPR. We need track dynamic access here eagerly\n        // to keep continuity with how cookies has worked in PPR without dynamicIO.\n        postponeWithTracking(\n          workStore.route,\n          callingExpression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We track dynamic access here so we don't need to wrap the cookies in\n        // individual property access tracking.\n        throwToInterruptStaticGeneration(\n          callingExpression,\n          workStore,\n          workUnitStore\n        )\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using cookies inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  // cookies is being called in a dynamic context\n\n  const requestStore = getExpectedRequestStore(callingExpression)\n\n  let underlyingCookies: ReadonlyRequestCookies\n\n  if (areCookiesMutableInCurrentPhase(requestStore)) {\n    // We can't conditionally return different types here based on the context.\n    // To avoid confusion, we always return the readonly type here.\n    underlyingCookies =\n      requestStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n  } else {\n    underlyingCookies = requestStore.cookies\n  }\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    return makeUntrackedExoticCookiesWithDevWarnings(\n      underlyingCookies,\n      workStore?.route\n    )\n  } else {\n    return makeUntrackedExoticCookies(underlyingCookies)\n  }\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeDynamicallyTrackedExoticCookies(\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`cookies()[Symbol.iterator]()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    size: {\n      get() {\n        const expression = '`cookies().size`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    has: {\n      value: function has() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()`'\n        const error = createCookiesAccessError(route, expression)\n        abortAndThrowOnSynchronousRequestDataAccess(\n          route,\n          expression,\n          error,\n          prerenderStore\n        )\n      },\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: underlyingCookies[Symbol.iterator]\n        ? underlyingCookies[Symbol.iterator].bind(underlyingCookies)\n        : // TODO this is a polyfill for when the underlying type is ResponseCookies\n          // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n          // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n          // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n          // has extra properties not available on RequestCookie instances.\n          polyfilledResponseCookiesIterator.bind(underlyingCookies),\n    },\n    size: {\n      get(): number {\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: underlyingCookies.get.bind(underlyingCookies),\n    },\n    getAll: {\n      value: underlyingCookies.getAll.bind(underlyingCookies),\n    },\n    has: {\n      value: underlyingCookies.has.bind(underlyingCookies),\n    },\n    set: {\n      value: underlyingCookies.set.bind(underlyingCookies),\n    },\n    delete: {\n      value: underlyingCookies.delete.bind(underlyingCookies),\n    },\n    clear: {\n      value:\n        // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n        typeof underlyingCookies.clear === 'function'\n          ? // @ts-expect-error clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.bind(underlyingCookies)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.bind(underlyingCookies, promise),\n    },\n    toString: {\n      value: underlyingCookies.toString.bind(underlyingCookies),\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction makeUntrackedExoticCookiesWithDevWarnings(\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = new Promise<ReadonlyRequestCookies>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingCookies))\n  )\n  CachedCookies.set(underlyingCookies, promise)\n\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: {\n      value: function () {\n        const expression = '`...cookies()` or similar iteration'\n        syncIODev(route, expression)\n        return underlyingCookies[Symbol.iterator]\n          ? underlyingCookies[Symbol.iterator].apply(\n              underlyingCookies,\n              arguments as any\n            )\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesIterator.call(underlyingCookies)\n      },\n      writable: false,\n    },\n    size: {\n      get(): number {\n        const expression = '`cookies().size`'\n        syncIODev(route, expression)\n        return underlyingCookies.size\n      },\n    },\n    get: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().get()`'\n        } else {\n          expression = `\\`cookies().get(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.get.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    getAll: {\n      value: function getAll() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().getAll()`'\n        } else {\n          expression = `\\`cookies().getAll(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.getAll.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    has: {\n      value: function get() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().has()`'\n        } else {\n          expression = `\\`cookies().has(${describeNameArg(arguments[0])})\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.has.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    set: {\n      value: function set() {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().set()`'\n        } else {\n          const arg = arguments[0]\n          if (arg) {\n            expression = `\\`cookies().set(${describeNameArg(arg)}, ...)\\``\n          } else {\n            expression = '`cookies().set(...)`'\n          }\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.set.apply(underlyingCookies, arguments as any)\n      },\n      writable: false,\n    },\n    delete: {\n      value: function () {\n        let expression: string\n        if (arguments.length === 0) {\n          expression = '`cookies().delete()`'\n        } else if (arguments.length === 1) {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])})\\``\n        } else {\n          expression = `\\`cookies().delete(${describeNameArg(arguments[0])}, ...)\\``\n        }\n        syncIODev(route, expression)\n        return underlyingCookies.delete.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n    clear: {\n      value: function clear() {\n        const expression = '`cookies().clear()`'\n        syncIODev(route, expression)\n        // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n        return typeof underlyingCookies.clear === 'function'\n          ? // @ts-ignore clear is defined in RequestCookies implementation but not in the type\n            underlyingCookies.clear.apply(underlyingCookies, arguments)\n          : // TODO this is a polyfill for when the underlying type is ResponseCookies\n            // We should remove this and unify our cookies types. We could just let this continue to throw lazily\n            // but that's already a hard thing to debug so we may as well implement it consistently. The biggest problem with\n            // implementing this in this way is the underlying cookie type is a ResponseCookie and not a RequestCookie and so it\n            // has extra properties not available on RequestCookie instances.\n            polyfilledResponseCookiesClear.call(underlyingCookies, promise)\n      },\n      writable: false,\n    },\n    toString: {\n      value: function toString() {\n        const expression = '`cookies().toString()` or implicit casting'\n        syncIODev(route, expression)\n        return underlyingCookies.toString.apply(\n          underlyingCookies,\n          arguments as any\n        )\n      },\n      writable: false,\n    },\n  } satisfies CookieExtensions)\n\n  return promise\n}\n\nfunction describeNameArg(arg: unknown) {\n  return typeof arg === 'object' &&\n    arg !== null &&\n    typeof (arg as any).name === 'string'\n    ? `'${(arg as any).name}'`\n    : typeof arg === 'string'\n      ? `'${arg}'`\n      : '...'\n}\n\nfunction syncIODev(route: string | undefined, expression: string) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  warnForSyncAccess(route, expression)\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` should be awaited before using its value. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction polyfilledResponseCookiesIterator(\n  this: ResponseCookies\n): ReturnType<ReadonlyRequestCookies[typeof Symbol.iterator]> {\n  return this.getAll()\n    .map((c) => [c.name, c] as [string, any])\n    .values()\n}\n\nfunction polyfilledResponseCookiesClear(\n  this: ResponseCookies,\n  returnable: Promise<ReadonlyRequestCookies>\n): typeof returnable {\n  for (const cookie of this.getAll()) {\n    this.delete(cookie.name)\n  }\n  return returnable\n}\n\ntype CookieExtensions = {\n  [K in keyof ReadonlyRequestCookies | 'clear']: unknown\n}\n"], "names": ["cookies", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedExoticCookies", "type", "dynamicShouldError", "StaticGenBailoutError", "makeDynamicallyTrackedExoticCookies", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender", "requestStore", "getExpectedRequestStore", "areCookiesMutableInCurrentPhase", "userspaceMutableCookies", "process", "env", "NODE_ENV", "isPrefetchRequest", "makeUntrackedExoticCookiesWithDevWarnings", "RequestCookiesAdapter", "seal", "RequestCookies", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Object", "defineProperties", "Symbol", "iterator", "value", "expression", "error", "createCookiesAccessError", "abortAndThrowOnSynchronousRequestDataAccess", "size", "arguments", "length", "describeNameArg", "getAll", "has", "arg", "delete", "clear", "toString", "cachedCookies", "Promise", "resolve", "bind", "polyfilledResponseCookiesIterator", "polyfilledResponseCookiesClear", "scheduleImmediate", "syncIODev", "apply", "call", "writable", "name", "prerenderPhase", "trackSynchronousRequestDataAccessInDev", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "prefix", "map", "c", "values", "returnable", "cookie"], "mappings": ";;;;+BAiDgBA;;;eAAAA;;;gCA5CT;yBACwB;0CACE;8CAI1B;kCAOA;yCAE+B;uCACH;0DACyB;2BAC1B;uBACc;AAyBzC,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,IAAAA,sCAA+B,KAChC;YACA,MAAM,qBAGL,CAHK,IAAIC,MACR,wDAAwD;YACxD,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,yOAAyO,CAAC,GAF/P,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,2BAA2BF;QACpC;QAEA,IAAIP,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0UAA0U,CAAC,GADhW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIL,cAAcU,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,mXAAmX,CAAC,GADzY,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIR,UAAUc,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEf,UAAUQ,KAAK,CAAC,iNAAiN,CAAC,GADvO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,oFAAoF;gBACpF,+CAA+C;gBAC/C,OAAOG,oCACLhB,UAAUQ,KAAK,EACfL;YAEJ,OAAO,IAAIA,cAAcU,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,0EAA0E;gBAC1E,2EAA2E;gBAC3EI,IAAAA,sCAAoB,EAClBjB,UAAUQ,KAAK,EACfT,mBACAI,cAAce,eAAe;YAEjC,OAAO,IAAIf,cAAcU,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,uEAAuE;gBACvE,uCAAuC;gBACvCM,IAAAA,kDAAgC,EAC9BpB,mBACAC,WACAG;YAEJ;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFiB,IAAAA,iDAA+B,EAACpB,WAAWG;IAC7C;IAEA,+CAA+C;IAE/C,MAAMkB,eAAeC,IAAAA,qDAAuB,EAACvB;IAE7C,IAAIW;IAEJ,IAAIa,IAAAA,+CAA+B,EAACF,eAAe;QACjD,2EAA2E;QAC3E,+DAA+D;QAC/DX,oBACEW,aAAaG,uBAAuB;IACxC,OAAO;QACLd,oBAAoBW,aAAavB,OAAO;IAC1C;IAEA,IAAI2B,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAAC3B,6BAAAA,UAAW4B,iBAAiB,GAAE;QAC3E,OAAOC,0CACLnB,mBACAV,6BAAAA,UAAWQ,KAAK;IAEpB,OAAO;QACL,OAAOI,2BAA2BF;IACpC;AACF;AAEA,SAASC;IACP,OAAOmB,qCAAqB,CAACC,IAAI,CAAC,IAAIC,uBAAc,CAAC,IAAIC,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASnB,oCACPR,KAAa,EACb4B,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,IAAAA,yCAAkB,EAChCJ,eAAeK,YAAY,EAC3B;IAEFP,cAAcQ,GAAG,CAACN,gBAAgBG;IAElCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAgB,MAAM;YACJd;gBACE,MAAMU,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAE,KAAK;YACHS,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAoB,QAAQ;YACNT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAqB,KAAK;YACHV,OAAO,SAASU;gBACd,IAAIT;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAM,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMU,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPV,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLV,aAAa;oBACf;gBACF;gBACA,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAuB,QAAQ;YACNZ,OAAO;gBACL,IAAIC;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLL,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACA,MAAMJ,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAwB,OAAO;YACLb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;QACAyB,UAAU;YACRd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnB,MAAMC,QAAQC,yBAAyB1C,OAAOwC;gBAC9CG,IAAAA,6DAA2C,EACzC3C,OACAwC,YACAC,OACAb;YAEJ;QACF;IACF;IAEA,OAAOG;AACT;AAEA,SAAS3B,2BACPF,iBAAyC;IAEzC,MAAMoD,gBAAgB5B,cAAcI,GAAG,CAAC5B;IACxC,IAAIoD,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMvB,UAAUwB,QAAQC,OAAO,CAACtD;IAChCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAOrC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,GACrCpC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,CAACmB,IAAI,CAACvD,qBAExC,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjEwD,kCAAkCD,IAAI,CAACvD;QAC7C;QACA0C,MAAM;YACJd;gBACE,OAAO5B,kBAAkB0C,IAAI;YAC/B;QACF;QACAd,KAAK;YACHS,OAAOrC,kBAAkB4B,GAAG,CAAC2B,IAAI,CAACvD;QACpC;QACA8C,QAAQ;YACNT,OAAOrC,kBAAkB8C,MAAM,CAACS,IAAI,CAACvD;QACvC;QACA+C,KAAK;YACHV,OAAOrC,kBAAkB+C,GAAG,CAACQ,IAAI,CAACvD;QACpC;QACAgC,KAAK;YACHK,OAAOrC,kBAAkBgC,GAAG,CAACuB,IAAI,CAACvD;QACpC;QACAiD,QAAQ;YACNZ,OAAOrC,kBAAkBiD,MAAM,CAACM,IAAI,CAACvD;QACvC;QACAkD,OAAO;YACLb,OACE,yFAAyF;YACzF,OAAOrC,kBAAkBkD,KAAK,KAAK,aAE/BlD,kBAAkBkD,KAAK,CAACK,IAAI,CAACvD,qBAE7B,qGAAqG;YACrG,iHAAiH;YACjH,oHAAoH;YACpH,iEAAiE;YACjEyD,+BAA+BF,IAAI,CAACvD,mBAAmB6B;QAC/D;QACAsB,UAAU;YACRd,OAAOrC,kBAAkBmD,QAAQ,CAACI,IAAI,CAACvD;QACzC;IACF;IAEA,OAAO6B;AACT;AAEA,SAASV,0CACPnB,iBAAyC,EACzCF,KAAc;IAEd,MAAMsD,gBAAgB5B,cAAcI,GAAG,CAAC5B;IACxC,IAAIoD,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMvB,UAAU,IAAIwB,QAAgC,CAACC,UACnDI,IAAAA,4BAAiB,EAAC,IAAMJ,QAAQtD;IAElCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErCI,OAAOC,gBAAgB,CAACL,SAAS;QAC/B,CAACM,OAAOC,QAAQ,CAAC,EAAE;YACjBC,OAAO;gBACL,MAAMC,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,GACrCpC,iBAAiB,CAACmC,OAAOC,QAAQ,CAAC,CAACwB,KAAK,CACtC5D,mBACA2C,aAGF,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEa,kCAAkCK,IAAI,CAAC7D;YAC7C;YACA8D,UAAU;QACZ;QACApB,MAAM;YACJd;gBACE,MAAMU,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB0C,IAAI;YAC/B;QACF;QACAd,KAAK;YACHS,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB4B,GAAG,CAACgC,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACAhB,QAAQ;YACNT,OAAO,SAASS;gBACd,IAAIR;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB8C,MAAM,CAACc,KAAK,CACnC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;QACAf,KAAK;YACHV,OAAO,SAAST;gBACd,IAAIU;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACLA,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACpE;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkB+C,GAAG,CAACa,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACA9B,KAAK;YACHK,OAAO,SAASL;gBACd,IAAIM;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO;oBACL,MAAMU,MAAML,SAAS,CAAC,EAAE;oBACxB,IAAIK,KAAK;wBACPV,aAAa,CAAC,gBAAgB,EAAEO,gBAAgBG,KAAK,QAAQ,CAAC;oBAChE,OAAO;wBACLV,aAAa;oBACf;gBACF;gBACAqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBgC,GAAG,CAAC4B,KAAK,CAAC5D,mBAAmB2C;YACxD;YACAmB,UAAU;QACZ;QACAb,QAAQ;YACNZ,OAAO;gBACL,IAAIC;gBACJ,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBAC1BN,aAAa;gBACf,OAAO,IAAIK,UAAUC,MAAM,KAAK,GAAG;oBACjCN,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,GAAG,CAAC;gBACvE,OAAO;oBACLL,aAAa,CAAC,mBAAmB,EAAEO,gBAAgBF,SAAS,CAAC,EAAE,EAAE,QAAQ,CAAC;gBAC5E;gBACAgB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBiD,MAAM,CAACW,KAAK,CACnC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;QACAZ,OAAO;YACLb,OAAO,SAASa;gBACd,MAAMZ,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,mFAAmF;gBACnF,OAAO,OAAOtC,kBAAkBkD,KAAK,KAAK,aAEtClD,kBAAkBkD,KAAK,CAACU,KAAK,CAAC5D,mBAAmB2C,aAEjD,qGAAqG;gBACrG,iHAAiH;gBACjH,oHAAoH;gBACpH,iEAAiE;gBACjEc,+BAA+BI,IAAI,CAAC7D,mBAAmB6B;YAC7D;YACAiC,UAAU;QACZ;QACAX,UAAU;YACRd,OAAO,SAASc;gBACd,MAAMb,aAAa;gBACnBqB,UAAU7D,OAAOwC;gBACjB,OAAOtC,kBAAkBmD,QAAQ,CAACS,KAAK,CACrC5D,mBACA2C;YAEJ;YACAmB,UAAU;QACZ;IACF;IAEA,OAAOjC;AACT;AAEA,SAASgB,gBAAgBG,GAAY;IACnC,OAAO,OAAOA,QAAQ,YACpBA,QAAQ,QACR,OAAO,AAACA,IAAYe,IAAI,KAAK,WAC3B,CAAC,CAAC,EAAE,AAACf,IAAYe,IAAI,CAAC,CAAC,CAAC,GACxB,OAAOf,QAAQ,WACb,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,GACV;AACR;AAEA,SAASW,UAAU7D,KAAyB,EAAEwC,UAAkB;IAC9D,MAAM7C,gBAAgBC,kDAAoB,CAACF,QAAQ;IACnD,IACEC,iBACAA,cAAcU,IAAI,KAAK,aACvBV,cAAcuE,cAAc,KAAK,MACjC;QACA,wEAAwE;QACxE,gEAAgE;QAChE,MAAMrD,eAAelB;QACrBwE,IAAAA,wDAAsC,EAACtD;IACzC;IACA,gCAAgC;IAChCuD,kBAAkBpE,OAAOwC;AAC3B;AAEA,MAAM4B,oBAAoBC,IAAAA,qFAA2C,EACnE3B;AAGF,SAASA,yBACP1C,KAAyB,EACzBwC,UAAkB;IAElB,MAAM8B,SAAStE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAGuE,OAAO,KAAK,EAAE9B,WAAW,EAAE,CAAC,GAC7B,CAAC,wDAAwD,CAAC,GAC1D,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASkB;IAGP,OAAO,IAAI,CAACV,MAAM,GACfuB,GAAG,CAAC,CAACC,IAAM;YAACA,EAAEP,IAAI;YAAEO;SAAE,EACtBC,MAAM;AACX;AAEA,SAASd,+BAEPe,UAA2C;IAE3C,KAAK,MAAMC,UAAU,IAAI,CAAC3B,MAAM,GAAI;QAClC,IAAI,CAACG,MAAM,CAACwB,OAAOV,IAAI;IACzB;IACA,OAAOS;AACT"}