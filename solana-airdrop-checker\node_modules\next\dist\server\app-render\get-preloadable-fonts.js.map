{"version": 3, "sources": ["../../../src/server/app-render/get-preloadable-fonts.tsx"], "sourcesContent": ["import type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\n\n/**\n * Get hrefs for fonts to preload\n * Returns null if there are no fonts at all.\n * Returns string[] if there are fonts to preload (font paths)\n * Returns empty string[] if there are fonts but none to preload and no other fonts have been preloaded\n * Returns null if there are fonts but none to preload and at least some were previously preloaded\n */\nexport function getPreloadableFonts(\n  nextFontManifest: DeepReadonly<NextFontManifest> | undefined,\n  filePath: string | undefined,\n  injectedFontPreloadTags: Set<string>\n): string[] | null {\n  if (!nextFontManifest || !filePath) {\n    return null\n  }\n  const filepathWithoutExtension = filePath.replace(/\\.[^.]+$/, '')\n  const fontFiles = new Set<string>()\n  let foundFontUsage = false\n\n  const preloadedFontFiles = nextFontManifest.app[filepathWithoutExtension]\n  if (preloadedFontFiles) {\n    foundFontUsage = true\n    for (const fontFile of preloadedFontFiles) {\n      if (!injectedFontPreloadTags.has(fontFile)) {\n        fontFiles.add(fontFile)\n        injectedFontPreloadTags.add(fontFile)\n      }\n    }\n  }\n\n  if (fontFiles.size) {\n    return [...fontFiles].sort()\n  } else if (foundFontUsage && injectedFontPreloadTags.size === 0) {\n    return []\n  } else {\n    return null\n  }\n}\n"], "names": ["getPreloadableFonts", "nextFontManifest", "filePath", "injectedFontPreloadTags", "filepathWithoutExtension", "replace", "fontFiles", "Set", "foundFontUsage", "preloadedFontFiles", "app", "fontFile", "has", "add", "size", "sort"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;AAAT,SAASA,oBACdC,gBAA4D,EAC5DC,QAA4B,EAC5BC,uBAAoC;IAEpC,IAAI,CAACF,oBAAoB,CAACC,UAAU;QAClC,OAAO;IACT;IACA,MAAME,2BAA2BF,SAASG,OAAO,CAAC,YAAY;IAC9D,MAAMC,YAAY,IAAIC;IACtB,IAAIC,iBAAiB;IAErB,MAAMC,qBAAqBR,iBAAiBS,GAAG,CAACN,yBAAyB;IACzE,IAAIK,oBAAoB;QACtBD,iBAAiB;QACjB,KAAK,MAAMG,YAAYF,mBAAoB;YACzC,IAAI,CAACN,wBAAwBS,GAAG,CAACD,WAAW;gBAC1CL,UAAUO,GAAG,CAACF;gBACdR,wBAAwBU,GAAG,CAACF;YAC9B;QACF;IACF;IAEA,IAAIL,UAAUQ,IAAI,EAAE;QAClB,OAAO;eAAIR;SAAU,CAACS,IAAI;IAC5B,OAAO,IAAIP,kBAAkBL,wBAAwBW,IAAI,KAAK,GAAG;QAC/D,OAAO,EAAE;IACX,OAAO;QACL,OAAO;IACT;AACF"}