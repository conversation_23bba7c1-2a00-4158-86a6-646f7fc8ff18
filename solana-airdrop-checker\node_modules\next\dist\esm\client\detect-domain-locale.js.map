{"version": 3, "sources": ["../../src/client/detect-domain-locale.ts"], "sourcesContent": ["import type { detectDomainLocale as Fn } from '../shared/lib/i18n/detect-domain-locale'\n\nexport const detectDomainLocale: typeof Fn = (...args) => {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    return require('../shared/lib/i18n/detect-domain-locale').detectDomainLocale(\n      ...args\n    )\n  }\n}\n"], "names": ["detectDomainLocale", "args", "process", "env", "__NEXT_I18N_SUPPORT", "require"], "mappings": "AAEA,OAAO,MAAMA,qBAAgC;qCAAIC;QAAAA;;IAC/C,IAAIC,QAAQC,GAAG,CAACC,mBAAmB,EAAE;QACnC,OAAOC,QAAQ,2CAA2CL,kBAAkB,IACvEC;IAEP;AACF,EAAC"}