/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q2NoZWNla3IlMjB3Y3QlMjBzb2xhbmElMjBvZyU1QyU1Q3NvbGFuYS1haXJkcm9wLWNoZWNrZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcY2hlY2VrciB3Y3Qgc29sYW5hIG9nXFxcXHNvbGFuYS1haXJkcm9wLWNoZWNrZXJcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxjaGVjZWtyIHdjdCBzb2xhbmEgb2dcXHNvbGFuYS1haXJkcm9wLWNoZWNrZXJcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SingleAddressChecker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SingleAddressChecker */ \"(app-pages-browser)/./src/components/SingleAddressChecker.tsx\");\n/* harmony import */ var _components_BulkAddressChecker__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/BulkAddressChecker */ \"(app-pages-browser)/./src/components/BulkAddressChecker.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Home() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('single');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"WalletConnect Token (WCT) Airdrop Checker\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Check WCT airdrop eligibility and amounts\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Powered by WalletConnect RPC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('single'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'single' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"Single Address\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('bulk'),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === 'bulk' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: \"Bulk Checker\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            activeTab === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SingleAddressChecker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 38\n                            }, this),\n                            activeTab === 'bulk' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BulkAddressChecker__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 36\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-blue-50 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-blue-900 mb-3\",\n                                children: \"How it works\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6 text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Single Address Checker\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Enter a single Solana wallet address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Get instant WCT airdrop eligibility status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• See eligible WCT token amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Perfect for quick checks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Bulk Checker\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Check up to 100 addresses at once\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Upload text file or paste addresses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• See all eligible amounts in one table\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Export results to CSV\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Progress tracking for large batches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Built for checking WalletConnect Token (WCT) airdrop eligibility\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: \"Using WalletConnect RPC API for reliable data\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"izfTSwz6B90F0EZ66lDgOHAjqeo=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/BulkAddressChecker.tsx":
/*!***********************************************!*\
  !*** ./src/components/BulkAddressChecker.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BulkAddressChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/solana-api */ \"(app-pages-browser)/./src/lib/solana-api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction BulkAddressChecker() {\n    _s();\n    const [addresses, setAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0\n    });\n    const parseAddresses = (input)=>{\n        return input.split(/[\\n,;]/).map((addr)=>addr.trim()).filter((addr)=>addr.length > 0);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!addresses.trim()) {\n            alert('Please enter wallet addresses');\n            return;\n        }\n        const addressList = parseAddresses(addresses);\n        if (addressList.length === 0) {\n            alert('No valid addresses found');\n            return;\n        }\n        if (addressList.length > 100) {\n            alert('Maximum 100 addresses allowed at once');\n            return;\n        }\n        // Validate addresses\n        const invalidAddresses = addressList.filter((addr)=>!_lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.isValidSolanaAddress(addr));\n        if (invalidAddresses.length > 0) {\n            alert(\"Invalid addresses found: \".concat(invalidAddresses.slice(0, 3).join(', ')).concat(invalidAddresses.length > 3 ? '...' : ''));\n            return;\n        }\n        setLoading(true);\n        setResults([]);\n        setProgress({\n            current: 0,\n            total: addressList.length\n        });\n        try {\n            // Process addresses in batches to avoid overwhelming the API\n            const batchSize = 10;\n            const allResults = [];\n            for(let i = 0; i < addressList.length; i += batchSize){\n                const batch = addressList.slice(i, i + batchSize);\n                const batchResults = await _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.checkMultipleWCTEligibility(batch);\n                allResults.push(...batchResults);\n                setProgress({\n                    current: allResults.length,\n                    total: addressList.length\n                });\n                setResults([\n                    ...allResults\n                ]); // Update results progressively\n                // Small delay between batches to be respectful to the API\n                if (i + batchSize < addressList.length) {\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setResults(allResults);\n        } catch (error) {\n            console.error('Error checking WCT eligibility:', error);\n            alert('Error occurred while checking WCT eligibility');\n        } finally{\n            setLoading(false);\n            setProgress({\n                current: 0,\n                total: 0\n            });\n        }\n    };\n    const handleClear = ()=>{\n        setAddresses('');\n        setResults([]);\n    };\n    const handleFileUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            var _event_target;\n            const content = (_event_target = event.target) === null || _event_target === void 0 ? void 0 : _event_target.result;\n            setAddresses(content);\n        };\n        reader.readAsText(file);\n    };\n    const exportResults = ()=>{\n        if (results.length === 0) return;\n        const csvContent = [\n            'Address,WCT Airdrop Status,Eligible Amount (WCT),Error',\n            ...results.map((result)=>'\"'.concat(result.address, '\",').concat(result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE', \",\").concat(result.eligibleAmount.toLocaleString(), ',\"').concat(result.error || '', '\"'))\n        ].join('\\n');\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"wct-airdrop-results-\".concat(new Date().toISOString().split('T')[0], \".csv\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const eligibleCount = results.filter((r)=>r.isEligible).length;\n    const errorCount = results.filter((r)=>r.error).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"WCT Airdrop Checker - Bulk\"\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"addresses\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Wallet Addresses (one per line, or comma/semicolon separated)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"addresses\",\n                                value: addresses,\n                                onChange: (e)=>setAddresses(e.target.value),\n                                placeholder: \"Enter multiple Solana wallet addresses...\",\n                                rows: 8,\n                                className: \"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                disabled: loading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Or upload a text file\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                accept: \".txt,.csv\",\n                                onChange: handleFileUpload,\n                                disabled: loading,\n                                className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: loading ? \"Checking... (\".concat(progress.current, \"/\").concat(progress.total, \")\") : 'Check WCT Eligibility'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleClear,\n                                disabled: loading,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: \"\".concat(progress.total > 0 ? progress.current / progress.total * 100 : 0, \"%\")\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: [\n                            \"Processing \",\n                            progress.current,\n                            \" of \",\n                            progress.total,\n                            \" addresses...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this),\n            results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg\",\n                                children: [\n                                    \"Results (\",\n                                    results.length,\n                                    \" addresses)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportResults,\n                                className: \"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\",\n                                children: \"Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-3 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: results.length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: \"Total Checked\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-3 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: eligibleCount\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Eligible\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-3 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-red-600\",\n                                        children: errorCount\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto border rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 sticky top-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"WCT Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"Eligible Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-t\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2 font-mono text-xs break-all\",\n                                                    children: result.address\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded text-xs font-medium \".concat(result.isEligible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                                        children: result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: result.isEligible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: [\n                                                            result.eligibleAmount.toLocaleString(),\n                                                            \" WCT\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: result.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-600 text-xs\",\n                                                        children: result.error\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 text-xs\",\n                                                        children: \"✓ Success\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(BulkAddressChecker, \"/vr4MsNBxa6AQ414uLLxBElrahg=\");\n_c = BulkAddressChecker;\nvar _c;\n$RefreshReg$(_c, \"BulkAddressChecker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/BulkAddressChecker.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SingleAddressChecker.tsx":
/*!*************************************************!*\
  !*** ./src/components/SingleAddressChecker.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SingleAddressChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/solana-api */ \"(app-pages-browser)/./src/lib/solana-api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SingleAddressChecker() {\n    _s();\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!address.trim()) {\n            alert('Please enter a Solana address');\n            return;\n        }\n        if (!_lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.isValidSolanaAddress(address.trim())) {\n            alert('Please enter a valid Solana address');\n            return;\n        }\n        setLoading(true);\n        setResult(null);\n        try {\n            const result = await _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.checkWCTEligibility(address.trim());\n            setResult(result);\n        } catch (error) {\n            console.error('Error checking WCT eligibility:', error);\n            setResult({\n                address: address.trim(),\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: 'Failed to check WCT eligibility'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClear = ()=>{\n        setAddress('');\n        setResult(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"WCT Airdrop Checker - Single Address\"\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"address\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Solana Wallet Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"address\",\n                                value: address,\n                                onChange: (e)=>setAddress(e.target.value),\n                                placeholder: \"Enter Solana wallet address (e.g., 54iFFejXJWnkydLqExz3tk8aTkaY1mYtdVtmhaxv44rw)\",\n                                className: \"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                disabled: loading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: loading ? 'Checking...' : 'Check WCT Eligibility'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleClear,\n                                disabled: loading,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 border rounded-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-lg mb-3\",\n                        children: \"Result\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-sm break-all\",\n                                        children: result.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"WCT Airdrop Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 px-2 py-1 rounded text-sm font-medium \".concat(result.isEligible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'),\n                                        children: result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            result.isEligible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Eligible Amount:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-semibold text-green-600\",\n                                        children: [\n                                            result.eligibleAmount.toLocaleString(),\n                                            \" WCT\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-red-600\",\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-red-600\",\n                                        children: result.error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n_s(SingleAddressChecker, \"jIqy6f9lXcxOi50B8LpbKBptlTw=\");\n_c = SingleAddressChecker;\nvar _c;\n$RefreshReg$(_c, \"SingleAddressChecker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SingleAddressChecker.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    async checkWCTEligibility(address) {\n        try {\n            const response = await fetch(\"/api/wct-airdrop/\".concat(address), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json',\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            return {\n                address: data.address,\n                balance: data.eligibleAmount,\n                isEligible: data.isEligible,\n                eligibleAmount: data.eligibleAmount,\n                error: data.error\n            };\n        } catch (error) {\n            console.error('WCT Airdrop API Error:', error);\n            // Handle network errors or CORS issues\n            if (error instanceof TypeError && error.message.includes('fetch')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: 'Network error - please check your connection'\n                };\n            }\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/solana-api.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);