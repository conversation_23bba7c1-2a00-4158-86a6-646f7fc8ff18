{"version": 3, "sources": ["../../../src/client/components/is-hydration-error.ts"], "sourcesContent": ["import isError from '../../lib/is-error'\n\nconst hydrationErrorRegex =\n  /hydration failed|while hydrating|content does not match|did not match|HTML didn't match|text didn't match/i\n\nconst reactUnifiedMismatchWarning = `Hydration failed because the server rendered HTML didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:`\n\nconst reactHydrationStartMessages = [\n  reactUnifiedMismatchWarning,\n  `Hydration failed because the server rendered text didn't match the client. As a result this tree will be regenerated on the client. This can happen if a SSR-ed Client Component used:`,\n  `A tree hydrated but some attributes of the server rendered HTML didn't match the client properties. This won't be patched up. This can happen if a SSR-ed Client Component used:`,\n]\n\nexport const REACT_HYDRATION_ERROR_LINK =\n  'https://react.dev/link/hydration-mismatch'\nexport const NEXTJS_HYDRATION_ERROR_LINK =\n  'https://nextjs.org/docs/messages/react-hydration-error'\n\nexport const getDefaultHydrationErrorMessage = () => {\n  return reactUnifiedMismatchWarning\n}\n\nexport function isHydrationError(error: unknown): boolean {\n  return isError(error) && hydrationErrorRegex.test(error.message)\n}\n\nexport function isReactHydrationErrorMessage(msg: string): boolean {\n  return reactHydrationStartMessages.some((prefix) => msg.startsWith(prefix))\n}\n\nconst hydrationWarningRegexes = [\n  /^In HTML, (.+?) cannot be a child of <(.+?)>\\.(.*)\\nThis will cause a hydration error\\.(.*)/,\n  /^In HTML, (.+?) cannot be a descendant of <(.+?)>\\.\\nThis will cause a hydration error\\.(.*)/,\n  /^In HTML, text nodes cannot be a child of <(.+?)>\\.\\nThis will cause a hydration error\\./,\n  /^In HTML, whitespace text nodes cannot be a child of <(.+?)>\\. Make sure you don't have any extra whitespace between tags on each line of your source code\\.\\nThis will cause a hydration error\\./,\n  /^Expected server HTML to contain a matching <(.+?)> in <(.+?)>\\.(.*)/,\n  /^Did not expect server HTML to contain a <(.+?)> in <(.+?)>\\.(.*)/,\n  /^Expected server HTML to contain a matching text node for \"(.+?)\" in <(.+?)>\\.(.*)/,\n  /^Did not expect server HTML to contain the text node \"(.+?)\" in <(.+?)>\\.(.*)/,\n  /^Text content did not match\\. Server: \"(.+?)\" Client: \"(.+?)\"(.*)/,\n]\n\nexport function testReactHydrationWarning(msg: string): boolean {\n  if (typeof msg !== 'string' || !msg) return false\n  // React 18 has the `Warning: ` prefix.\n  // React 19 does not.\n  if (msg.startsWith('Warning: ')) {\n    msg = msg.slice('Warning: '.length)\n  }\n  return hydrationWarningRegexes.some((regex) => regex.test(msg))\n}\n\nexport function getHydrationErrorStackInfo(rawMessage: string): {\n  message: string | null\n  stack?: string\n  diff?: string\n} {\n  rawMessage = rawMessage.replace(/^Error: /, '')\n  rawMessage = rawMessage.replace('Warning: ', '')\n  const isReactHydrationWarning = testReactHydrationWarning(rawMessage)\n\n  if (!isReactHydrationErrorMessage(rawMessage) && !isReactHydrationWarning) {\n    return {\n      message: null,\n      stack: rawMessage,\n      diff: '',\n    }\n  }\n\n  if (isReactHydrationWarning) {\n    const [message, diffLog] = rawMessage.split('\\n\\n')\n    return {\n      message: message.trim(),\n      stack: '',\n      diff: (diffLog || '').trim(),\n    }\n  }\n\n  const firstLineBreak = rawMessage.indexOf('\\n')\n  rawMessage = rawMessage.slice(firstLineBreak + 1).trim()\n\n  const [message, trailing] = rawMessage.split(`${REACT_HYDRATION_ERROR_LINK}`)\n  const trimmedMessage = message.trim()\n  // React built-in hydration diff starts with a newline, checking if length is > 1\n  if (trailing && trailing.length > 1) {\n    const stacks: string[] = []\n    const diffs: string[] = []\n    trailing.split('\\n').forEach((line) => {\n      if (line.trim() === '') return\n      if (line.trim().startsWith('at ')) {\n        stacks.push(line)\n      } else {\n        diffs.push(line)\n      }\n    })\n\n    return {\n      message: trimmedMessage,\n      diff: diffs.join('\\n'),\n      stack: stacks.join('\\n'),\n    }\n  } else {\n    return {\n      message: trimmedMessage,\n      stack: trailing, // without hydration diff\n    }\n  }\n}\n"], "names": ["NEXTJS_HYDRATION_ERROR_LINK", "REACT_HYDRATION_ERROR_LINK", "getDefaultHydrationErrorMessage", "getHydrationErrorStackInfo", "isHydrationError", "isReactHydrationErrorMessage", "testReactHydrationWarning", "hydrationErrorRegex", "reactUnifiedMismatchWarning", "reactHydrationStartMessages", "error", "isError", "test", "message", "msg", "some", "prefix", "startsWith", "hydrationWarningRegexes", "slice", "length", "regex", "rawMessage", "replace", "isReactHydrationWarning", "stack", "diff", "diffLog", "split", "trim", "firstLineBreak", "indexOf", "trailing", "trimmedMessage", "stacks", "diffs", "for<PERSON>ach", "line", "push", "join"], "mappings": ";;;;;;;;;;;;;;;;;;;;IAeaA,2BAA2B;eAA3BA;;IAFAC,0BAA0B;eAA1BA;;IAKAC,+BAA+B;eAA/BA;;IAkCGC,0BAA0B;eAA1BA;;IA9BAC,gBAAgB;eAAhBA;;IAIAC,4BAA4B;eAA5BA;;IAgBAC,yBAAyB;eAAzBA;;;;kEA1CI;AAEpB,MAAMC,sBACJ;AAEF,MAAMC,8BAA+B;AAErC,MAAMC,8BAA8B;IAClCD;IACC;IACA;CACF;AAEM,MAAMP,6BACX;AACK,MAAMD,8BACX;AAEK,MAAME,kCAAkC;IAC7C,OAAOM;AACT;AAEO,SAASJ,iBAAiBM,KAAc;IAC7C,OAAOC,IAAAA,gBAAO,EAACD,UAAUH,oBAAoBK,IAAI,CAACF,MAAMG,OAAO;AACjE;AAEO,SAASR,6BAA6BS,GAAW;IACtD,OAAOL,4BAA4BM,IAAI,CAAC,CAACC,SAAWF,IAAIG,UAAU,CAACD;AACrE;AAEA,MAAME,0BAA0B;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASZ,0BAA0BQ,GAAW;IACnD,IAAI,OAAOA,QAAQ,YAAY,CAACA,KAAK,OAAO;IAC5C,uCAAuC;IACvC,qBAAqB;IACrB,IAAIA,IAAIG,UAAU,CAAC,cAAc;QAC/BH,MAAMA,IAAIK,KAAK,CAAC,YAAYC,MAAM;IACpC;IACA,OAAOF,wBAAwBH,IAAI,CAAC,CAACM,QAAUA,MAAMT,IAAI,CAACE;AAC5D;AAEO,SAASX,2BAA2BmB,UAAkB;IAK3DA,aAAaA,WAAWC,OAAO,CAAC,YAAY;IAC5CD,aAAaA,WAAWC,OAAO,CAAC,aAAa;IAC7C,MAAMC,0BAA0BlB,0BAA0BgB;IAE1D,IAAI,CAACjB,6BAA6BiB,eAAe,CAACE,yBAAyB;QACzE,OAAO;YACLX,SAAS;YACTY,OAAOH;YACPI,MAAM;QACR;IACF;IAEA,IAAIF,yBAAyB;QAC3B,MAAM,CAACX,SAASc,QAAQ,GAAGL,WAAWM,KAAK,CAAC;QAC5C,OAAO;YACLf,SAASA,QAAQgB,IAAI;YACrBJ,OAAO;YACPC,MAAM,AAACC,CAAAA,WAAW,EAAC,EAAGE,IAAI;QAC5B;IACF;IAEA,MAAMC,iBAAiBR,WAAWS,OAAO,CAAC;IAC1CT,aAAaA,WAAWH,KAAK,CAACW,iBAAiB,GAAGD,IAAI;IAEtD,MAAM,CAAChB,SAASmB,SAAS,GAAGV,WAAWM,KAAK,CAAC,AAAC,KAAE3B;IAChD,MAAMgC,iBAAiBpB,QAAQgB,IAAI;IACnC,iFAAiF;IACjF,IAAIG,YAAYA,SAASZ,MAAM,GAAG,GAAG;QACnC,MAAMc,SAAmB,EAAE;QAC3B,MAAMC,QAAkB,EAAE;QAC1BH,SAASJ,KAAK,CAAC,MAAMQ,OAAO,CAAC,CAACC;YAC5B,IAAIA,KAAKR,IAAI,OAAO,IAAI;YACxB,IAAIQ,KAAKR,IAAI,GAAGZ,UAAU,CAAC,QAAQ;gBACjCiB,OAAOI,IAAI,CAACD;YACd,OAAO;gBACLF,MAAMG,IAAI,CAACD;YACb;QACF;QAEA,OAAO;YACLxB,SAASoB;YACTP,MAAMS,MAAMI,IAAI,CAAC;YACjBd,OAAOS,OAAOK,IAAI,CAAC;QACrB;IACF,OAAO;QACL,OAAO;YACL1B,SAASoB;YACTR,OAAOO;QACT;IACF;AACF"}