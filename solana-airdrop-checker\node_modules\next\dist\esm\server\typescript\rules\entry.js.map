{"version": 3, "sources": ["../../../../src/server/typescript/rules/entry.ts"], "sourcesContent": ["import path from 'path'\nimport fs from 'fs'\n\nimport {\n  ALLOWED_LAYOUT_PROPS,\n  ALLOWED_PAGE_PROPS,\n  NEXT_TS_ERRORS,\n} from '../constant'\nimport { getTs, isPageFile, isPositionInsideNode } from '../utils'\n\nimport type tsModule from 'typescript/lib/tsserverlibrary'\n\nconst entry = {\n  // Give auto completion for the component's props\n  getCompletionsAtPosition(\n    fileName: string,\n    node: tsModule.FunctionDeclaration,\n    position: number\n  ) {\n    const ts = getTs()\n    const entries: tsModule.CompletionEntry[] = []\n\n    // Default export function might not accept parameters\n    const paramNode = node.parameters?.[0] as\n      | tsModule.ParameterDeclaration\n      | undefined\n\n    if (paramNode && isPositionInsideNode(position, paramNode)) {\n      const props = paramNode?.name\n      if (props && ts.isObjectBindingPattern(props)) {\n        let validProps = []\n        let validPropsWithType = []\n        let type: string\n\n        if (isPageFile(fileName)) {\n          // For page entries (page.js), it can only have `params` and `searchParams`\n          // as the prop names.\n          validProps = ALLOWED_PAGE_PROPS\n          validPropsWithType = ALLOWED_PAGE_PROPS\n          type = 'page'\n        } else {\n          // For layout entires, check if it has any named slots.\n          const currentDir = path.dirname(fileName)\n          const items = fs.readdirSync(currentDir, {\n            withFileTypes: true,\n          })\n          const slots = []\n          for (const item of items) {\n            if (item.isDirectory() && item.name.startsWith('@')) {\n              slots.push(item.name.slice(1))\n            }\n          }\n          validProps = ALLOWED_LAYOUT_PROPS.concat(slots)\n          validPropsWithType = ALLOWED_LAYOUT_PROPS.concat(\n            slots.map((s) => `${s}: React.ReactNode`)\n          )\n          type = 'layout'\n        }\n\n        // Auto completion for props\n        for (const element of props.elements) {\n          if (isPositionInsideNode(position, element)) {\n            const nameNode = element.propertyName || element.name\n\n            if (isPositionInsideNode(position, nameNode)) {\n              for (const name of validProps) {\n                entries.push({\n                  name,\n                  insertText: name,\n                  sortText: '_' + name,\n                  kind: ts.ScriptElementKind.memberVariableElement,\n                  kindModifiers: ts.ScriptElementKindModifier.none,\n                  labelDetails: {\n                    description: `Next.js ${type} prop`,\n                  },\n                } as tsModule.CompletionEntry)\n              }\n            }\n\n            break\n          }\n        }\n\n        // Auto completion for types\n        if (paramNode.type && ts.isTypeLiteralNode(paramNode.type)) {\n          for (const member of paramNode.type.members) {\n            if (isPositionInsideNode(position, member)) {\n              for (const name of validPropsWithType) {\n                entries.push({\n                  name,\n                  insertText: name,\n                  sortText: '_' + name,\n                  kind: ts.ScriptElementKind.memberVariableElement,\n                  kindModifiers: ts.ScriptElementKindModifier.none,\n                  labelDetails: {\n                    description: `Next.js ${type} prop type`,\n                  },\n                } as tsModule.CompletionEntry)\n              }\n\n              break\n            }\n          }\n        }\n      }\n    }\n\n    return entries\n  },\n\n  // Give error diagnostics for the component\n  getSemanticDiagnostics(\n    fileName: string,\n    source: tsModule.SourceFile,\n    node: tsModule.FunctionDeclaration\n  ) {\n    const ts = getTs()\n\n    let validProps = []\n    let type: string\n\n    if (isPageFile(fileName)) {\n      // For page entries (page.js), it can only have `params` and `searchParams`\n      // as the prop names.\n      validProps = ALLOWED_PAGE_PROPS\n      type = 'page'\n    } else {\n      // For layout entires, check if it has any named slots.\n      const currentDir = path.dirname(fileName)\n      const items = fs.readdirSync(currentDir, { withFileTypes: true })\n      const slots = []\n      for (const item of items) {\n        if (item.isDirectory() && item.name.startsWith('@')) {\n          slots.push(item.name.slice(1))\n        }\n      }\n      validProps = ALLOWED_LAYOUT_PROPS.concat(slots)\n      type = 'layout'\n    }\n\n    const diagnostics: tsModule.Diagnostic[] = []\n\n    const props = node.parameters?.[0]?.name\n    if (props && ts.isObjectBindingPattern(props)) {\n      for (const prop of props.elements) {\n        const propName = (prop.propertyName || prop.name).getText()\n        if (!validProps.includes(propName)) {\n          diagnostics.push({\n            file: source,\n            category: ts.DiagnosticCategory.Error,\n            code: NEXT_TS_ERRORS.INVALID_PAGE_PROP,\n            messageText: `\"${propName}\" is not a valid ${type} prop.`,\n            start: prop.getStart(),\n            length: prop.getWidth(),\n          })\n        }\n      }\n    }\n\n    return diagnostics\n  },\n}\n\nexport default entry\n"], "names": ["path", "fs", "ALLOWED_LAYOUT_PROPS", "ALLOWED_PAGE_PROPS", "NEXT_TS_ERRORS", "getTs", "isPageFile", "isPositionInsideNode", "entry", "getCompletionsAtPosition", "fileName", "node", "position", "ts", "entries", "paramNode", "parameters", "props", "name", "isObjectBindingPattern", "validProps", "validPropsWithType", "type", "currentDir", "dirname", "items", "readdirSync", "withFileTypes", "slots", "item", "isDirectory", "startsWith", "push", "slice", "concat", "map", "s", "element", "elements", "nameNode", "propertyName", "insertText", "sortText", "kind", "ScriptElementKind", "memberVariableElement", "kindModifiers", "ScriptElementKindModifier", "none", "labelDetails", "description", "isTypeLiteralNode", "member", "members", "getSemanticDiagnostics", "source", "diagnostics", "prop", "propName", "getText", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "INVALID_PAGE_PROP", "messageText", "start", "getStart", "length", "getWidth"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,OAAOC,QAAQ,KAAI;AAEnB,SACEC,oBAAoB,EACpBC,kBAAkB,EAClBC,cAAc,QACT,cAAa;AACpB,SAASC,KAAK,EAAEC,UAAU,EAAEC,oBAAoB,QAAQ,WAAU;AAIlE,MAAMC,QAAQ;IACZ,iDAAiD;IACjDC,0BACEC,QAAgB,EAChBC,IAAkC,EAClCC,QAAgB;YAMED;QAJlB,MAAME,KAAKR;QACX,MAAMS,UAAsC,EAAE;QAE9C,sDAAsD;QACtD,MAAMC,aAAYJ,mBAAAA,KAAKK,UAAU,qBAAfL,gBAAiB,CAAC,EAAE;QAItC,IAAII,aAAaR,qBAAqBK,UAAUG,YAAY;YAC1D,MAAME,QAAQF,6BAAAA,UAAWG,IAAI;YAC7B,IAAID,SAASJ,GAAGM,sBAAsB,CAACF,QAAQ;gBAC7C,IAAIG,aAAa,EAAE;gBACnB,IAAIC,qBAAqB,EAAE;gBAC3B,IAAIC;gBAEJ,IAAIhB,WAAWI,WAAW;oBACxB,2EAA2E;oBAC3E,qBAAqB;oBACrBU,aAAajB;oBACbkB,qBAAqBlB;oBACrBmB,OAAO;gBACT,OAAO;oBACL,uDAAuD;oBACvD,MAAMC,aAAavB,KAAKwB,OAAO,CAACd;oBAChC,MAAMe,QAAQxB,GAAGyB,WAAW,CAACH,YAAY;wBACvCI,eAAe;oBACjB;oBACA,MAAMC,QAAQ,EAAE;oBAChB,KAAK,MAAMC,QAAQJ,MAAO;wBACxB,IAAII,KAAKC,WAAW,MAAMD,KAAKX,IAAI,CAACa,UAAU,CAAC,MAAM;4BACnDH,MAAMI,IAAI,CAACH,KAAKX,IAAI,CAACe,KAAK,CAAC;wBAC7B;oBACF;oBACAb,aAAalB,qBAAqBgC,MAAM,CAACN;oBACzCP,qBAAqBnB,qBAAqBgC,MAAM,CAC9CN,MAAMO,GAAG,CAAC,CAACC,IAAM,GAAGA,EAAE,iBAAiB,CAAC;oBAE1Cd,OAAO;gBACT;gBAEA,4BAA4B;gBAC5B,KAAK,MAAMe,WAAWpB,MAAMqB,QAAQ,CAAE;oBACpC,IAAI/B,qBAAqBK,UAAUyB,UAAU;wBAC3C,MAAME,WAAWF,QAAQG,YAAY,IAAIH,QAAQnB,IAAI;wBAErD,IAAIX,qBAAqBK,UAAU2B,WAAW;4BAC5C,KAAK,MAAMrB,QAAQE,WAAY;gCAC7BN,QAAQkB,IAAI,CAAC;oCACXd;oCACAuB,YAAYvB;oCACZwB,UAAU,MAAMxB;oCAChByB,MAAM9B,GAAG+B,iBAAiB,CAACC,qBAAqB;oCAChDC,eAAejC,GAAGkC,yBAAyB,CAACC,IAAI;oCAChDC,cAAc;wCACZC,aAAa,CAAC,QAAQ,EAAE5B,KAAK,KAAK,CAAC;oCACrC;gCACF;4BACF;wBACF;wBAEA;oBACF;gBACF;gBAEA,4BAA4B;gBAC5B,IAAIP,UAAUO,IAAI,IAAIT,GAAGsC,iBAAiB,CAACpC,UAAUO,IAAI,GAAG;oBAC1D,KAAK,MAAM8B,UAAUrC,UAAUO,IAAI,CAAC+B,OAAO,CAAE;wBAC3C,IAAI9C,qBAAqBK,UAAUwC,SAAS;4BAC1C,KAAK,MAAMlC,QAAQG,mBAAoB;gCACrCP,QAAQkB,IAAI,CAAC;oCACXd;oCACAuB,YAAYvB;oCACZwB,UAAU,MAAMxB;oCAChByB,MAAM9B,GAAG+B,iBAAiB,CAACC,qBAAqB;oCAChDC,eAAejC,GAAGkC,yBAAyB,CAACC,IAAI;oCAChDC,cAAc;wCACZC,aAAa,CAAC,QAAQ,EAAE5B,KAAK,UAAU,CAAC;oCAC1C;gCACF;4BACF;4BAEA;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOR;IACT;IAEA,2CAA2C;IAC3CwC,wBACE5C,QAAgB,EAChB6C,MAA2B,EAC3B5C,IAAkC;YA4BpBA,mBAAAA;QA1Bd,MAAME,KAAKR;QAEX,IAAIe,aAAa,EAAE;QACnB,IAAIE;QAEJ,IAAIhB,WAAWI,WAAW;YACxB,2EAA2E;YAC3E,qBAAqB;YACrBU,aAAajB;YACbmB,OAAO;QACT,OAAO;YACL,uDAAuD;YACvD,MAAMC,aAAavB,KAAKwB,OAAO,CAACd;YAChC,MAAMe,QAAQxB,GAAGyB,WAAW,CAACH,YAAY;gBAAEI,eAAe;YAAK;YAC/D,MAAMC,QAAQ,EAAE;YAChB,KAAK,MAAMC,QAAQJ,MAAO;gBACxB,IAAII,KAAKC,WAAW,MAAMD,KAAKX,IAAI,CAACa,UAAU,CAAC,MAAM;oBACnDH,MAAMI,IAAI,CAACH,KAAKX,IAAI,CAACe,KAAK,CAAC;gBAC7B;YACF;YACAb,aAAalB,qBAAqBgC,MAAM,CAACN;YACzCN,OAAO;QACT;QAEA,MAAMkC,cAAqC,EAAE;QAE7C,MAAMvC,SAAQN,mBAAAA,KAAKK,UAAU,sBAAfL,oBAAAA,gBAAiB,CAAC,EAAE,qBAApBA,kBAAsBO,IAAI;QACxC,IAAID,SAASJ,GAAGM,sBAAsB,CAACF,QAAQ;YAC7C,KAAK,MAAMwC,QAAQxC,MAAMqB,QAAQ,CAAE;gBACjC,MAAMoB,WAAW,AAACD,CAAAA,KAAKjB,YAAY,IAAIiB,KAAKvC,IAAI,AAAD,EAAGyC,OAAO;gBACzD,IAAI,CAACvC,WAAWwC,QAAQ,CAACF,WAAW;oBAClCF,YAAYxB,IAAI,CAAC;wBACf6B,MAAMN;wBACNO,UAAUjD,GAAGkD,kBAAkB,CAACC,KAAK;wBACrCC,MAAM7D,eAAe8D,iBAAiB;wBACtCC,aAAa,CAAC,CAAC,EAAET,SAAS,iBAAiB,EAAEpC,KAAK,MAAM,CAAC;wBACzD8C,OAAOX,KAAKY,QAAQ;wBACpBC,QAAQb,KAAKc,QAAQ;oBACvB;gBACF;YACF;QACF;QAEA,OAAOf;IACT;AACF;AAEA,eAAehD,MAAK"}