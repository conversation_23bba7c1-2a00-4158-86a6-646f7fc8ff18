{"version": 3, "sources": ["../../src/client/app-index.tsx"], "sourcesContent": ["// imports polyfill from `@next/polyfill-module` after build.\nimport '../build/polyfills/polyfill-module'\n\nimport './components/globals/patch-console'\nimport './components/globals/handle-global-errors'\n\nimport ReactDOMClient from 'react-dom/client'\nimport React, { use } from 'react'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { createFromReadableStream } from 'react-server-dom-webpack/client'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context.shared-runtime'\nimport { onRecoverableError } from './react-client-callbacks/on-recoverable-error'\nimport {\n  onCaughtError,\n  onUncaughtError,\n} from './react-client-callbacks/error-boundary-callbacks'\nimport { callServer } from './app-call-server'\nimport { findSourceMapURL } from './app-find-source-map-url'\nimport {\n  type AppRouterActionQueue,\n  createMutableActionQueue,\n} from './components/app-router-instance'\nimport AppRouter from './components/app-router'\nimport type { InitialRSCPayload } from '../server/app-render/types'\nimport { createInitialRouterState } from './components/router-reducer/create-initial-router-state'\nimport { MissingSlotContext } from '../shared/lib/app-router-context.shared-runtime'\nimport { setAppBuildId } from './app-build-id'\n\n/// <reference types=\"react-dom/experimental\" />\n\nconst appElement: HTMLElement | Document = document\n\nconst encoder = new TextEncoder()\n\nlet initialServerDataBuffer: (string | Uint8Array)[] | undefined = undefined\nlet initialServerDataWriter: ReadableStreamDefaultController | undefined =\n  undefined\nlet initialServerDataLoaded = false\nlet initialServerDataFlushed = false\n\nlet initialFormStateData: null | any = null\n\ntype FlightSegment =\n  | [isBootStrap: 0]\n  | [isNotBootstrap: 1, responsePartial: string]\n  | [isFormState: 2, formState: any]\n  | [isBinary: 3, responseBase64Partial: string]\n\ntype NextFlight = Omit<Array<FlightSegment>, 'push'> & {\n  push: (seg: FlightSegment) => void\n}\n\ndeclare global {\n  // If you're working in a browser environment\n  interface Window {\n    __next_f: NextFlight\n  }\n}\n\nfunction nextServerDataCallback(seg: FlightSegment): void {\n  if (seg[0] === 0) {\n    initialServerDataBuffer = []\n  } else if (seg[0] === 1) {\n    if (!initialServerDataBuffer)\n      throw new Error('Unexpected server data: missing bootstrap script.')\n\n    if (initialServerDataWriter) {\n      initialServerDataWriter.enqueue(encoder.encode(seg[1]))\n    } else {\n      initialServerDataBuffer.push(seg[1])\n    }\n  } else if (seg[0] === 2) {\n    initialFormStateData = seg[1]\n  } else if (seg[0] === 3) {\n    if (!initialServerDataBuffer)\n      throw new Error('Unexpected server data: missing bootstrap script.')\n\n    // Decode the base64 string back to binary data.\n    const binaryString = atob(seg[1])\n    const decodedChunk = new Uint8Array(binaryString.length)\n    for (var i = 0; i < binaryString.length; i++) {\n      decodedChunk[i] = binaryString.charCodeAt(i)\n    }\n\n    if (initialServerDataWriter) {\n      initialServerDataWriter.enqueue(decodedChunk)\n    } else {\n      initialServerDataBuffer.push(decodedChunk)\n    }\n  }\n}\n\nfunction isStreamErrorOrUnfinished(ctr: ReadableStreamDefaultController) {\n  // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n  return ctr.desiredSize === null || ctr.desiredSize < 0\n}\n\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr: ReadableStreamDefaultController) {\n  if (initialServerDataBuffer) {\n    initialServerDataBuffer.forEach((val) => {\n      ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val)\n    })\n    if (initialServerDataLoaded && !initialServerDataFlushed) {\n      if (isStreamErrorOrUnfinished(ctr)) {\n        ctr.error(\n          new Error(\n            'The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'\n          )\n        )\n      } else {\n        ctr.close()\n      }\n      initialServerDataFlushed = true\n      initialServerDataBuffer = undefined\n    }\n  }\n\n  initialServerDataWriter = ctr\n}\n\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function () {\n  if (initialServerDataWriter && !initialServerDataFlushed) {\n    initialServerDataWriter.close()\n    initialServerDataFlushed = true\n    initialServerDataBuffer = undefined\n  }\n  initialServerDataLoaded = true\n}\n\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n  document.addEventListener('DOMContentLoaded', DOMContentLoaded, false)\n} else {\n  // Delayed in marco task to ensure it's executed later than hydration\n  setTimeout(DOMContentLoaded)\n}\n\nconst nextServerDataLoadingGlobal = (self.__next_f = self.__next_f || [])\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback)\nnextServerDataLoadingGlobal.push = nextServerDataCallback\n\nconst readable = new ReadableStream({\n  start(controller) {\n    nextServerDataRegisterWriter(controller)\n  },\n})\n\nconst initialServerResponse = createFromReadableStream<InitialRSCPayload>(\n  readable,\n  { callServer, findSourceMapURL }\n)\n\nfunction ServerRoot({\n  pendingActionQueue,\n}: {\n  pendingActionQueue: Promise<AppRouterActionQueue>\n}): React.ReactNode {\n  const initialRSCPayload = use(initialServerResponse)\n  const actionQueue = use<AppRouterActionQueue>(pendingActionQueue)\n\n  const router = (\n    <AppRouter\n      actionQueue={actionQueue}\n      globalErrorComponentAndStyles={initialRSCPayload.G}\n      assetPrefix={initialRSCPayload.p}\n    />\n  )\n\n  if (process.env.NODE_ENV === 'development' && initialRSCPayload.m) {\n    // We provide missing slot information in a context provider only during development\n    // as we log some additional information about the missing slots in the console.\n    return (\n      <MissingSlotContext value={initialRSCPayload.m}>\n        {router}\n      </MissingSlotContext>\n    )\n  }\n\n  return router\n}\n\nconst StrictModeIfEnabled = process.env.__NEXT_STRICT_MODE_APP\n  ? React.StrictMode\n  : React.Fragment\n\nfunction Root({ children }: React.PropsWithChildren<{}>) {\n  if (process.env.__NEXT_TEST_MODE) {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      window.__NEXT_HYDRATED = true\n      window.__NEXT_HYDRATED_AT = performance.now()\n      window.__NEXT_HYDRATED_CB?.()\n    }, [])\n  }\n\n  return children\n}\n\nconst reactRootOptions: ReactDOMClient.RootOptions = {\n  onRecoverableError,\n  onCaughtError,\n  onUncaughtError,\n}\n\nexport type ClientInstrumentationHooks = {\n  onRouterTransitionStart?: (\n    url: string,\n    navigationType: 'push' | 'replace' | 'traverse'\n  ) => void\n}\n\nexport function hydrate(\n  instrumentationHooks: ClientInstrumentationHooks | null\n) {\n  // React overrides `.then` and doesn't return a new promise chain,\n  // so we wrap the action queue in a promise to ensure that its value\n  // is defined when the promise resolves.\n  // https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\n  const pendingActionQueue: Promise<AppRouterActionQueue> = new Promise(\n    (resolve, reject) => {\n      initialServerResponse.then(\n        (initialRSCPayload) => {\n          // setAppBuildId should be called only once, during JS initialization\n          // and before any components have hydrated.\n          setAppBuildId(initialRSCPayload.b)\n\n          const initialTimestamp = Date.now()\n\n          resolve(\n            createMutableActionQueue(\n              createInitialRouterState({\n                navigatedAt: initialTimestamp,\n                initialFlightData: initialRSCPayload.f,\n                initialCanonicalUrlParts: initialRSCPayload.c,\n                initialParallelRoutes: new Map(),\n                location: window.location,\n                couldBeIntercepted: initialRSCPayload.i,\n                postponed: initialRSCPayload.s,\n                prerendered: initialRSCPayload.S,\n              }),\n              instrumentationHooks\n            )\n          )\n        },\n        (err: Error) => reject(err)\n      )\n    }\n  )\n\n  const reactEl = (\n    <StrictModeIfEnabled>\n      <HeadManagerContext.Provider value={{ appDir: true }}>\n        <Root>\n          <ServerRoot pendingActionQueue={pendingActionQueue} />\n        </Root>\n      </HeadManagerContext.Provider>\n    </StrictModeIfEnabled>\n  )\n\n  if (document.documentElement.id === '__next_error__') {\n    let element = reactEl\n    // Server rendering failed, fall back to client-side rendering\n    if (process.env.NODE_ENV !== 'production') {\n      const { createRootLevelDevOverlayElement } =\n        require('./components/react-dev-overlay/app/client-entry') as typeof import('./components/react-dev-overlay/app/client-entry')\n\n      // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n      element = createRootLevelDevOverlayElement(element)\n    }\n\n    ReactDOMClient.createRoot(appElement, reactRootOptions).render(element)\n  } else {\n    React.startTransition(() => {\n      ReactDOMClient.hydrateRoot(appElement, reactEl, {\n        ...reactRootOptions,\n        formState: initialFormStateData,\n      })\n    })\n  }\n\n  // TODO-APP: Remove this logic when Float has GC built-in in development.\n  if (process.env.NODE_ENV !== 'production') {\n    const { linkGc } =\n      require('./app-link-gc') as typeof import('./app-link-gc')\n    linkGc()\n  }\n}\n"], "names": ["ReactDOMClient", "React", "use", "createFromReadableStream", "HeadManagerContext", "onRecoverableError", "onCaughtError", "onUncaughtError", "callServer", "findSourceMapURL", "createMutableActionQueue", "AppRouter", "createInitialRouterState", "MissingSlotContext", "setAppBuildId", "appElement", "document", "encoder", "TextEncoder", "initialServerDataBuffer", "undefined", "initialServerDataWriter", "initialServerDataLoaded", "initialServerDataFlushed", "initialFormStateData", "nextServerDataCallback", "seg", "Error", "enqueue", "encode", "push", "binaryString", "atob", "decodedChunk", "Uint8Array", "length", "i", "charCodeAt", "isStreamErrorOrUnfinished", "ctr", "desiredSize", "nextServerDataRegisterWriter", "for<PERSON>ach", "val", "error", "close", "DOMContentLoaded", "readyState", "addEventListener", "setTimeout", "nextServerDataLoadingGlobal", "self", "__next_f", "readable", "ReadableStream", "start", "controller", "initialServerResponse", "ServerRoot", "pendingActionQueue", "initialRSCPayload", "actionQueue", "router", "globalErrorComponentAndStyles", "G", "assetPrefix", "p", "process", "env", "NODE_ENV", "m", "value", "StrictModeIfEnabled", "__NEXT_STRICT_MODE_APP", "StrictMode", "Fragment", "Root", "children", "__NEXT_TEST_MODE", "useEffect", "window", "__NEXT_HYDRATED", "__NEXT_HYDRATED_AT", "performance", "now", "__NEXT_HYDRATED_CB", "reactRootOptions", "hydrate", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "then", "b", "initialTimestamp", "Date", "navigatedAt", "initialFlightData", "f", "initialCanonicalUrlParts", "c", "initialParallelRoutes", "Map", "location", "couldBeIntercepted", "postponed", "s", "prerendered", "S", "err", "reactEl", "Provider", "appDir", "documentElement", "id", "element", "createRootLevelDevOverlayElement", "require", "createRoot", "render", "startTransition", "hydrateRoot", "formState", "linkGc"], "mappings": "AAAA,6DAA6D;;AAC7D,OAAO,qCAAoC;AAE3C,OAAO,qCAAoC;AAC3C,OAAO,4CAA2C;AAElD,OAAOA,oBAAoB,mBAAkB;AAC7C,OAAOC,SAASC,GAAG,QAAQ,QAAO;AAClC,6DAA6D;AAC7D,SAASC,wBAAwB,QAAQ,kCAAiC;AAC1E,SAASC,kBAAkB,QAAQ,oDAAmD;AACtF,SAASC,kBAAkB,QAAQ,gDAA+C;AAClF,SACEC,aAAa,EACbC,eAAe,QACV,oDAAmD;AAC1D,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAEEC,wBAAwB,QACnB,mCAAkC;AACzC,OAAOC,eAAe,0BAAyB;AAE/C,SAASC,wBAAwB,QAAQ,0DAAyD;AAClG,SAASC,kBAAkB,QAAQ,kDAAiD;AACpF,SAASC,aAAa,QAAQ,iBAAgB;AAE9C,gDAAgD;AAEhD,MAAMC,aAAqCC;AAE3C,MAAMC,UAAU,IAAIC;AAEpB,IAAIC,0BAA+DC;AACnE,IAAIC,0BACFD;AACF,IAAIE,0BAA0B;AAC9B,IAAIC,2BAA2B;AAE/B,IAAIC,uBAAmC;AAmBvC,SAASC,uBAAuBC,GAAkB;IAChD,IAAIA,GAAG,CAAC,EAAE,KAAK,GAAG;QAChBP,0BAA0B,EAAE;IAC9B,OAAO,IAAIO,GAAG,CAAC,EAAE,KAAK,GAAG;QACvB,IAAI,CAACP,yBACH,MAAM,qBAA8D,CAA9D,IAAIQ,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;QAErE,IAAIN,yBAAyB;YAC3BA,wBAAwBO,OAAO,CAACX,QAAQY,MAAM,CAACH,GAAG,CAAC,EAAE;QACvD,OAAO;YACLP,wBAAwBW,IAAI,CAACJ,GAAG,CAAC,EAAE;QACrC;IACF,OAAO,IAAIA,GAAG,CAAC,EAAE,KAAK,GAAG;QACvBF,uBAAuBE,GAAG,CAAC,EAAE;IAC/B,OAAO,IAAIA,GAAG,CAAC,EAAE,KAAK,GAAG;QACvB,IAAI,CAACP,yBACH,MAAM,qBAA8D,CAA9D,IAAIQ,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;QAErE,gDAAgD;QAChD,MAAMI,eAAeC,KAAKN,GAAG,CAAC,EAAE;QAChC,MAAMO,eAAe,IAAIC,WAAWH,aAAaI,MAAM;QACvD,IAAK,IAAIC,IAAI,GAAGA,IAAIL,aAAaI,MAAM,EAAEC,IAAK;YAC5CH,YAAY,CAACG,EAAE,GAAGL,aAAaM,UAAU,CAACD;QAC5C;QAEA,IAAIf,yBAAyB;YAC3BA,wBAAwBO,OAAO,CAACK;QAClC,OAAO;YACLd,wBAAwBW,IAAI,CAACG;QAC/B;IACF;AACF;AAEA,SAASK,0BAA0BC,GAAoC;IACrE,6HAA6H;IAC7H,OAAOA,IAAIC,WAAW,KAAK,QAAQD,IAAIC,WAAW,GAAG;AACvD;AAEA,4EAA4E;AAC5E,6EAA6E;AAC7E,oEAAoE;AACpE,sEAAsE;AACtE,qDAAqD;AACrD,4DAA4D;AAC5D,wEAAwE;AACxE,+DAA+D;AAC/D,SAASC,6BAA6BF,GAAoC;IACxE,IAAIpB,yBAAyB;QAC3BA,wBAAwBuB,OAAO,CAAC,CAACC;YAC/BJ,IAAIX,OAAO,CAAC,OAAOe,QAAQ,WAAW1B,QAAQY,MAAM,CAACc,OAAOA;QAC9D;QACA,IAAIrB,2BAA2B,CAACC,0BAA0B;YACxD,IAAIe,0BAA0BC,MAAM;gBAClCA,IAAIK,KAAK,CACP,qBAEC,CAFD,IAAIjB,MACF,0JADF,qBAAA;2BAAA;gCAAA;kCAAA;gBAEA;YAEJ,OAAO;gBACLY,IAAIM,KAAK;YACX;YACAtB,2BAA2B;YAC3BJ,0BAA0BC;QAC5B;IACF;IAEAC,0BAA0BkB;AAC5B;AAEA,iFAAiF;AACjF,MAAMO,mBAAmB;IACvB,IAAIzB,2BAA2B,CAACE,0BAA0B;QACxDF,wBAAwBwB,KAAK;QAC7BtB,2BAA2B;QAC3BJ,0BAA0BC;IAC5B;IACAE,0BAA0B;AAC5B;AAEA,gDAAgD;AAChD,IAAIN,SAAS+B,UAAU,KAAK,WAAW;IACrC/B,SAASgC,gBAAgB,CAAC,oBAAoBF,kBAAkB;AAClE,OAAO;IACL,qEAAqE;IACrEG,WAAWH;AACb;AAEA,MAAMI,8BAA+BC,KAAKC,QAAQ,GAAGD,KAAKC,QAAQ,IAAI,EAAE;AACxEF,4BAA4BR,OAAO,CAACjB;AACpCyB,4BAA4BpB,IAAI,GAAGL;AAEnC,MAAM4B,WAAW,IAAIC,eAAe;IAClCC,OAAMC,UAAU;QACdf,6BAA6Be;IAC/B;AACF;AAEA,MAAMC,wBAAwBtD,yBAC5BkD,UACA;IAAE7C;IAAYC;AAAiB;AAGjC,SAASiD,WAAW,KAInB;IAJmB,IAAA,EAClBC,kBAAkB,EAGnB,GAJmB;IAKlB,MAAMC,oBAAoB1D,IAAIuD;IAC9B,MAAMI,cAAc3D,IAA0ByD;IAE9C,MAAMG,uBACJ,KAACnD;QACCkD,aAAaA;QACbE,+BAA+BH,kBAAkBI,CAAC;QAClDC,aAAaL,kBAAkBM,CAAC;;IAIpC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBT,kBAAkBU,CAAC,EAAE;QACjE,oFAAoF;QACpF,gFAAgF;QAChF,qBACE,KAACzD;YAAmB0D,OAAOX,kBAAkBU,CAAC;sBAC3CR;;IAGP;IAEA,OAAOA;AACT;AAEA,MAAMU,sBAAsBL,QAAQC,GAAG,CAACK,sBAAsB,GAC1DxE,MAAMyE,UAAU,GAChBzE,MAAM0E,QAAQ;AAElB,SAASC,KAAK,KAAyC;IAAzC,IAAA,EAAEC,QAAQ,EAA+B,GAAzC;IACZ,IAAIV,QAAQC,GAAG,CAACU,gBAAgB,EAAE;QAChC,sDAAsD;QACtD7E,MAAM8E,SAAS,CAAC;YACdC,OAAOC,eAAe,GAAG;YACzBD,OAAOE,kBAAkB,GAAGC,YAAYC,GAAG;YAC3CJ,OAAOK,kBAAkB,oBAAzBL,OAAOK,kBAAkB,MAAzBL;QACF,GAAG,EAAE;IACP;IAEA,OAAOH;AACT;AAEA,MAAMS,mBAA+C;IACnDjF;IACAC;IACAC;AACF;AASA,OAAO,SAASgF,QACdC,oBAAuD;IAEvD,kEAAkE;IAClE,oEAAoE;IACpE,wCAAwC;IACxC,2IAA2I;IAC3I,MAAM7B,qBAAoD,IAAI8B,QAC5D,CAACC,SAASC;QACRlC,sBAAsBmC,IAAI,CACxB,CAAChC;YACC,qEAAqE;YACrE,2CAA2C;YAC3C9C,cAAc8C,kBAAkBiC,CAAC;YAEjC,MAAMC,mBAAmBC,KAAKX,GAAG;YAEjCM,QACEhF,yBACEE,yBAAyB;gBACvBoF,aAAaF;gBACbG,mBAAmBrC,kBAAkBsC,CAAC;gBACtCC,0BAA0BvC,kBAAkBwC,CAAC;gBAC7CC,uBAAuB,IAAIC;gBAC3BC,UAAUvB,OAAOuB,QAAQ;gBACzBC,oBAAoB5C,kBAAkBxB,CAAC;gBACvCqE,WAAW7C,kBAAkB8C,CAAC;gBAC9BC,aAAa/C,kBAAkBgD,CAAC;YAClC,IACApB;QAGN,GACA,CAACqB,MAAelB,OAAOkB;IAE3B;IAGF,MAAMC,wBACJ,KAACtC;kBACC,cAAA,KAACpE,mBAAmB2G,QAAQ;YAACxC,OAAO;gBAAEyC,QAAQ;YAAK;sBACjD,cAAA,KAACpC;0BACC,cAAA,KAAClB;oBAAWC,oBAAoBA;;;;;IAMxC,IAAI3C,SAASiG,eAAe,CAACC,EAAE,KAAK,kBAAkB;QACpD,IAAIC,UAAUL;QACd,8DAA8D;QAC9D,IAAI3C,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,MAAM,EAAE+C,gCAAgC,EAAE,GACxCC,QAAQ;YAEV,kFAAkF;YAClFF,UAAUC,iCAAiCD;QAC7C;QAEAnH,eAAesH,UAAU,CAACvG,YAAYuE,kBAAkBiC,MAAM,CAACJ;IACjE,OAAO;QACLlH,MAAMuH,eAAe,CAAC;YACpBxH,eAAeyH,WAAW,CAAC1G,YAAY+F,SAAS;gBAC9C,GAAGxB,gBAAgB;gBACnBoC,WAAWlG;YACb;QACF;IACF;IAEA,yEAAyE;IACzE,IAAI2C,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEsD,MAAM,EAAE,GACdN,QAAQ;QACVM;IACF;AACF"}