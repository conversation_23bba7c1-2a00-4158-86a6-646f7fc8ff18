{"version": 3, "sources": ["../../../../src/server/lib/router-utils/filesystem.ts"], "sourcesContent": ["import type {\n  FunctionsConfigManifest,\n  ManifestRoute,\n  PrerenderManifest,\n  RoutesManifest,\n} from '../../../build'\nimport type { NextConfigComplete } from '../../config-shared'\nimport type { MiddlewareManifest } from '../../../build/webpack/plugins/middleware-plugin'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport type { PatchMatcher } from '../../../shared/lib/router/utils/path-match'\nimport type { MiddlewareRouteMatch } from '../../../shared/lib/router/utils/middleware-route-matcher'\n\nimport path from 'path'\nimport fs from 'fs/promises'\nimport * as Log from '../../../build/output/log'\nimport setupDebug from 'next/dist/compiled/debug'\nimport { LRUCache } from '../lru-cache'\nimport loadCustomRoutes, { type Rewrite } from '../../../lib/load-custom-routes'\nimport { modifyRouteRegex } from '../../../lib/redirect-status'\nimport { FileType, fileExists } from '../../../lib/file-exists'\nimport { recursiveReadDir } from '../../../lib/recursive-readdir'\nimport { isDynamicRoute } from '../../../shared/lib/router/utils'\nimport { escapeStringRegexp } from '../../../shared/lib/escape-regexp'\nimport { getPathMatch } from '../../../shared/lib/router/utils/path-match'\nimport { getRouteRegex } from '../../../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../../../shared/lib/router/utils/route-matcher'\nimport { pathHasPrefix } from '../../../shared/lib/router/utils/path-has-prefix'\nimport { normalizeLocalePath } from '../../../shared/lib/i18n/normalize-locale-path'\nimport { removePathPrefix } from '../../../shared/lib/router/utils/remove-path-prefix'\nimport { getMiddlewareRouteMatcher } from '../../../shared/lib/router/utils/middleware-route-matcher'\nimport {\n  APP_PATH_ROUTES_MANIFEST,\n  BUILD_ID_FILE,\n  FUNCTIONS_CONFIG_MANIFEST,\n  MIDDLEWARE_MANIFEST,\n  PAGES_MANIFEST,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n} from '../../../shared/lib/constants'\nimport { normalizePathSep } from '../../../shared/lib/page-path/normalize-path-sep'\nimport { normalizeMetadataRoute } from '../../../lib/metadata/get-metadata-route'\nimport { RSCPathnameNormalizer } from '../../normalizers/request/rsc'\nimport { PrefetchRSCPathnameNormalizer } from '../../normalizers/request/prefetch-rsc'\nimport { encodeURIPath } from '../../../shared/lib/encode-uri-path'\n\nexport type FsOutput = {\n  type:\n    | 'appFile'\n    | 'pageFile'\n    | 'nextImage'\n    | 'publicFolder'\n    | 'nextStaticFolder'\n    | 'legacyStaticFolder'\n    | 'devVirtualFsItem'\n\n  itemPath: string\n  fsPath?: string\n  itemsRoot?: string\n  locale?: string\n}\n\nconst debug = setupDebug('next:router-server:filesystem')\n\nexport type FilesystemDynamicRoute = ManifestRoute & {\n  /**\n   * The path matcher that can be used to match paths against this route.\n   */\n  match: PatchMatcher\n}\n\nexport const buildCustomRoute = <T>(\n  type: 'redirect' | 'header' | 'rewrite' | 'before_files_rewrite',\n  item: T & { source: string },\n  basePath?: string,\n  caseSensitive?: boolean\n): T & { match: PatchMatcher; check?: boolean } => {\n  const restrictedRedirectPaths = ['/_next'].map((p) =>\n    basePath ? `${basePath}${p}` : p\n  )\n  const match = getPathMatch(item.source, {\n    strict: true,\n    removeUnnamedParams: true,\n    regexModifier: !(item as any).internal\n      ? (regex: string) =>\n          modifyRouteRegex(\n            regex,\n            type === 'redirect' ? restrictedRedirectPaths : undefined\n          )\n      : undefined,\n    sensitive: caseSensitive,\n  })\n  return {\n    ...item,\n    ...(type === 'rewrite' ? { check: true } : {}),\n    match,\n  }\n}\n\nexport async function setupFsCheck(opts: {\n  dir: string\n  dev: boolean\n  minimalMode?: boolean\n  config: NextConfigComplete\n  addDevWatcherCallback?: (\n    arg: (files: Map<string, { timestamp: number }>) => void\n  ) => void\n}) {\n  const getItemsLru = !opts.dev\n    ? new LRUCache<FsOutput | null>(1024 * 1024, function length(value) {\n        if (!value) return 0\n        return (\n          (value.fsPath || '').length +\n          value.itemPath.length +\n          value.type.length\n        )\n      })\n    : undefined\n\n  // routes that have _next/data endpoints (SSG/SSP)\n  const nextDataRoutes = new Set<string>()\n  const publicFolderItems = new Set<string>()\n  const nextStaticFolderItems = new Set<string>()\n  const legacyStaticFolderItems = new Set<string>()\n\n  const appFiles = new Set<string>()\n  const pageFiles = new Set<string>()\n  let dynamicRoutes: FilesystemDynamicRoute[] = []\n\n  let middlewareMatcher:\n    | ReturnType<typeof getMiddlewareRouteMatcher>\n    | undefined = () => false\n\n  const distDir = path.join(opts.dir, opts.config.distDir)\n  const publicFolderPath = path.join(opts.dir, 'public')\n  const nextStaticFolderPath = path.join(distDir, 'static')\n  const legacyStaticFolderPath = path.join(opts.dir, 'static')\n  let customRoutes: UnwrapPromise<ReturnType<typeof loadCustomRoutes>> = {\n    redirects: [],\n    rewrites: {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    },\n    headers: [],\n  }\n  let buildId = 'development'\n  let prerenderManifest: PrerenderManifest\n\n  if (!opts.dev) {\n    const buildIdPath = path.join(opts.dir, opts.config.distDir, BUILD_ID_FILE)\n    try {\n      buildId = await fs.readFile(buildIdPath, 'utf8')\n    } catch (err: any) {\n      if (err.code !== 'ENOENT') throw err\n      throw new Error(\n        `Could not find a production build in the '${opts.config.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n      )\n    }\n\n    try {\n      for (const file of await recursiveReadDir(publicFolderPath)) {\n        // Ensure filename is encoded and normalized.\n        publicFolderItems.add(encodeURIPath(normalizePathSep(file)))\n      }\n    } catch (err: any) {\n      if (err.code !== 'ENOENT') {\n        throw err\n      }\n    }\n\n    try {\n      for (const file of await recursiveReadDir(legacyStaticFolderPath)) {\n        // Ensure filename is encoded and normalized.\n        legacyStaticFolderItems.add(encodeURIPath(normalizePathSep(file)))\n      }\n      Log.warn(\n        `The static directory has been deprecated in favor of the public directory. https://nextjs.org/docs/messages/static-dir-deprecated`\n      )\n    } catch (err: any) {\n      if (err.code !== 'ENOENT') {\n        throw err\n      }\n    }\n\n    try {\n      for (const file of await recursiveReadDir(nextStaticFolderPath)) {\n        // Ensure filename is encoded and normalized.\n        nextStaticFolderItems.add(\n          path.posix.join(\n            '/_next/static',\n            encodeURIPath(normalizePathSep(file))\n          )\n        )\n      }\n    } catch (err) {\n      if (opts.config.output !== 'standalone') throw err\n    }\n\n    const routesManifestPath = path.join(distDir, ROUTES_MANIFEST)\n    const prerenderManifestPath = path.join(distDir, PRERENDER_MANIFEST)\n    const middlewareManifestPath = path.join(\n      distDir,\n      'server',\n      MIDDLEWARE_MANIFEST\n    )\n    const functionsConfigManifestPath = path.join(\n      distDir,\n      'server',\n      FUNCTIONS_CONFIG_MANIFEST\n    )\n    const pagesManifestPath = path.join(distDir, 'server', PAGES_MANIFEST)\n    const appRoutesManifestPath = path.join(distDir, APP_PATH_ROUTES_MANIFEST)\n\n    const routesManifest = JSON.parse(\n      await fs.readFile(routesManifestPath, 'utf8')\n    ) as RoutesManifest\n\n    prerenderManifest = JSON.parse(\n      await fs.readFile(prerenderManifestPath, 'utf8')\n    ) as PrerenderManifest\n\n    const middlewareManifest = JSON.parse(\n      await fs.readFile(middlewareManifestPath, 'utf8').catch(() => '{}')\n    ) as MiddlewareManifest\n\n    const functionsConfigManifest = JSON.parse(\n      await fs.readFile(functionsConfigManifestPath, 'utf8').catch(() => '{}')\n    ) as FunctionsConfigManifest\n\n    const pagesManifest = JSON.parse(\n      await fs.readFile(pagesManifestPath, 'utf8')\n    )\n    const appRoutesManifest = JSON.parse(\n      await fs.readFile(appRoutesManifestPath, 'utf8').catch(() => '{}')\n    )\n\n    for (const key of Object.keys(pagesManifest)) {\n      // ensure the non-locale version is in the set\n      if (opts.config.i18n) {\n        pageFiles.add(\n          normalizeLocalePath(key, opts.config.i18n.locales).pathname\n        )\n      } else {\n        pageFiles.add(key)\n      }\n    }\n    for (const key of Object.keys(appRoutesManifest)) {\n      appFiles.add(appRoutesManifest[key])\n    }\n\n    const escapedBuildId = escapeStringRegexp(buildId)\n\n    for (const route of routesManifest.dataRoutes) {\n      if (isDynamicRoute(route.page)) {\n        const routeRegex = getRouteRegex(route.page)\n        dynamicRoutes.push({\n          ...route,\n          regex: routeRegex.re.toString(),\n          match: getRouteMatcher({\n            // TODO: fix this in the manifest itself, must also be fixed in\n            // upstream builder that relies on this\n            re: opts.config.i18n\n              ? new RegExp(\n                  route.dataRouteRegex.replace(\n                    `/${escapedBuildId}/`,\n                    `/${escapedBuildId}/(?<nextLocale>[^/]+?)/`\n                  )\n                )\n              : new RegExp(route.dataRouteRegex),\n            groups: routeRegex.groups,\n          }),\n        })\n      }\n      nextDataRoutes.add(route.page)\n    }\n\n    for (const route of routesManifest.dynamicRoutes) {\n      dynamicRoutes.push({\n        ...route,\n        match: getRouteMatcher(getRouteRegex(route.page)),\n      })\n    }\n\n    if (middlewareManifest.middleware?.['/']?.matchers) {\n      middlewareMatcher = getMiddlewareRouteMatcher(\n        middlewareManifest.middleware?.['/']?.matchers\n      )\n    } else if (functionsConfigManifest?.functions['/_middleware']) {\n      middlewareMatcher = getMiddlewareRouteMatcher(\n        functionsConfigManifest.functions['/_middleware'].matchers ?? [\n          { regexp: '.*', originalSource: '/:path*' },\n        ]\n      )\n    }\n\n    customRoutes = {\n      redirects: routesManifest.redirects,\n      rewrites: routesManifest.rewrites\n        ? Array.isArray(routesManifest.rewrites)\n          ? {\n              beforeFiles: [],\n              afterFiles: routesManifest.rewrites,\n              fallback: [],\n            }\n          : routesManifest.rewrites\n        : {\n            beforeFiles: [],\n            afterFiles: [],\n            fallback: [],\n          },\n      headers: routesManifest.headers,\n    }\n  } else {\n    // dev handling\n    customRoutes = await loadCustomRoutes(opts.config)\n\n    prerenderManifest = {\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      notFoundRoutes: [],\n      preview: {\n        previewModeId: require('crypto').randomBytes(16).toString('hex'),\n        previewModeSigningKey: require('crypto')\n          .randomBytes(32)\n          .toString('hex'),\n        previewModeEncryptionKey: require('crypto')\n          .randomBytes(32)\n          .toString('hex'),\n      },\n    }\n  }\n\n  const headers = customRoutes.headers.map((item) =>\n    buildCustomRoute(\n      'header',\n      item,\n      opts.config.basePath,\n      opts.config.experimental.caseSensitiveRoutes\n    )\n  )\n  const redirects = customRoutes.redirects.map((item) =>\n    buildCustomRoute(\n      'redirect',\n      item,\n      opts.config.basePath,\n      opts.config.experimental.caseSensitiveRoutes\n    )\n  )\n  const rewrites = {\n    beforeFiles: customRoutes.rewrites.beforeFiles.map((item) =>\n      buildCustomRoute('before_files_rewrite', item)\n    ),\n    afterFiles: customRoutes.rewrites.afterFiles.map((item) =>\n      buildCustomRoute(\n        'rewrite',\n        item,\n        opts.config.basePath,\n        opts.config.experimental.caseSensitiveRoutes\n      )\n    ),\n    fallback: customRoutes.rewrites.fallback.map((item) =>\n      buildCustomRoute(\n        'rewrite',\n        item,\n        opts.config.basePath,\n        opts.config.experimental.caseSensitiveRoutes\n      )\n    ),\n  }\n\n  const { i18n } = opts.config\n\n  const handleLocale = (pathname: string, locales?: string[]) => {\n    let locale: string | undefined\n\n    if (i18n) {\n      const i18nResult = normalizeLocalePath(pathname, locales || i18n.locales)\n\n      pathname = i18nResult.pathname\n      locale = i18nResult.detectedLocale\n    }\n    return { locale, pathname }\n  }\n\n  debug('nextDataRoutes', nextDataRoutes)\n  debug('dynamicRoutes', dynamicRoutes)\n  debug('customRoutes', customRoutes)\n  debug('publicFolderItems', publicFolderItems)\n  debug('nextStaticFolderItems', nextStaticFolderItems)\n  debug('pageFiles', pageFiles)\n  debug('appFiles', appFiles)\n\n  let ensureFn: (item: FsOutput) => Promise<void> | undefined\n\n  const normalizers = {\n    // Because we can't know if the app directory is enabled or not at this\n    // stage, we assume that it is.\n    rsc: new RSCPathnameNormalizer(),\n    prefetchRSC: opts.config.experimental.ppr\n      ? new PrefetchRSCPathnameNormalizer()\n      : undefined,\n  }\n\n  return {\n    headers,\n    rewrites,\n    redirects,\n\n    buildId,\n    handleLocale,\n\n    appFiles,\n    pageFiles,\n    dynamicRoutes,\n    nextDataRoutes,\n\n    exportPathMapRoutes: undefined as\n      | undefined\n      | ReturnType<typeof buildCustomRoute<Rewrite>>[],\n\n    devVirtualFsItems: new Set<string>(),\n\n    prerenderManifest,\n    middlewareMatcher: middlewareMatcher as MiddlewareRouteMatch | undefined,\n\n    ensureCallback(fn: typeof ensureFn) {\n      ensureFn = fn\n    },\n\n    async getItem(itemPath: string): Promise<FsOutput | null> {\n      const originalItemPath = itemPath\n      const itemKey = originalItemPath\n      const lruResult = getItemsLru?.get(itemKey)\n\n      if (lruResult) {\n        return lruResult\n      }\n\n      const { basePath } = opts.config\n\n      const hasBasePath = pathHasPrefix(itemPath, basePath)\n\n      // Return null if path doesn't start with basePath\n      if (basePath && !hasBasePath) {\n        return null\n      }\n\n      // Remove basePath if it exists.\n      if (basePath && hasBasePath) {\n        itemPath = removePathPrefix(itemPath, basePath) || '/'\n      }\n\n      // Simulate minimal mode requests by normalizing RSC and postponed\n      // requests.\n      if (opts.minimalMode) {\n        if (normalizers.prefetchRSC?.match(itemPath)) {\n          itemPath = normalizers.prefetchRSC.normalize(itemPath, true)\n        } else if (normalizers.rsc.match(itemPath)) {\n          itemPath = normalizers.rsc.normalize(itemPath, true)\n        }\n      }\n\n      if (itemPath !== '/' && itemPath.endsWith('/')) {\n        itemPath = itemPath.substring(0, itemPath.length - 1)\n      }\n\n      let decodedItemPath = itemPath\n\n      try {\n        decodedItemPath = decodeURIComponent(itemPath)\n      } catch {}\n\n      if (itemPath === '/_next/image') {\n        return {\n          itemPath,\n          type: 'nextImage',\n        }\n      }\n\n      const itemsToCheck: Array<[Set<string>, FsOutput['type']]> = [\n        [this.devVirtualFsItems, 'devVirtualFsItem'],\n        [nextStaticFolderItems, 'nextStaticFolder'],\n        [legacyStaticFolderItems, 'legacyStaticFolder'],\n        [publicFolderItems, 'publicFolder'],\n        [appFiles, 'appFile'],\n        [pageFiles, 'pageFile'],\n      ]\n\n      for (let [items, type] of itemsToCheck) {\n        let locale: string | undefined\n        let curItemPath = itemPath\n        let curDecodedItemPath = decodedItemPath\n\n        const isDynamicOutput = type === 'pageFile' || type === 'appFile'\n\n        if (i18n) {\n          const localeResult = handleLocale(\n            itemPath,\n            // legacy behavior allows visiting static assets under\n            // default locale but no other locale\n            isDynamicOutput\n              ? undefined\n              : [\n                  i18n?.defaultLocale,\n                  // default locales from domains need to be matched too\n                  ...(i18n.domains?.map((item) => item.defaultLocale) || []),\n                ]\n          )\n\n          if (localeResult.pathname !== curItemPath) {\n            curItemPath = localeResult.pathname\n            locale = localeResult.locale\n\n            try {\n              curDecodedItemPath = decodeURIComponent(curItemPath)\n            } catch {}\n          }\n        }\n\n        if (type === 'legacyStaticFolder') {\n          if (!pathHasPrefix(curItemPath, '/static')) {\n            continue\n          }\n          curItemPath = curItemPath.substring('/static'.length)\n\n          try {\n            curDecodedItemPath = decodeURIComponent(curItemPath)\n          } catch {}\n        }\n\n        if (\n          type === 'nextStaticFolder' &&\n          !pathHasPrefix(curItemPath, '/_next/static')\n        ) {\n          continue\n        }\n\n        const nextDataPrefix = `/_next/data/${buildId}/`\n\n        if (\n          type === 'pageFile' &&\n          curItemPath.startsWith(nextDataPrefix) &&\n          curItemPath.endsWith('.json')\n        ) {\n          items = nextDataRoutes\n          // remove _next/data/<build-id> prefix\n          curItemPath = curItemPath.substring(nextDataPrefix.length - 1)\n\n          // remove .json postfix\n          curItemPath = curItemPath.substring(\n            0,\n            curItemPath.length - '.json'.length\n          )\n          const curLocaleResult = handleLocale(curItemPath)\n          curItemPath =\n            curLocaleResult.pathname === '/index'\n              ? '/'\n              : curLocaleResult.pathname\n\n          locale = curLocaleResult.locale\n\n          try {\n            curDecodedItemPath = decodeURIComponent(curItemPath)\n          } catch {}\n        }\n\n        let matchedItem = items.has(curItemPath)\n\n        // check decoded variant as well\n        if (!matchedItem && !opts.dev) {\n          matchedItem = items.has(curDecodedItemPath)\n          if (matchedItem) curItemPath = curDecodedItemPath\n          else {\n            // x-ref: https://github.com/vercel/next.js/issues/54008\n            // There're cases that urls get decoded before requests, we should support both encoded and decoded ones.\n            // e.g. nginx could decode the proxy urls, the below ones should be treated as the same:\n            // decoded version: `/_next/static/chunks/pages/blog/[slug]-d4858831b91b69f6.js`\n            // encoded version: `/_next/static/chunks/pages/blog/%5Bslug%5D-d4858831b91b69f6.js`\n            try {\n              // encode the special characters in the path and retrieve again to determine if path exists.\n              const encodedCurItemPath = encodeURIPath(curItemPath)\n              matchedItem = items.has(encodedCurItemPath)\n            } catch {}\n          }\n        }\n\n        if (matchedItem || opts.dev) {\n          let fsPath: string | undefined\n          let itemsRoot: string | undefined\n\n          switch (type) {\n            case 'nextStaticFolder': {\n              itemsRoot = nextStaticFolderPath\n              curItemPath = curItemPath.substring('/_next/static'.length)\n              break\n            }\n            case 'legacyStaticFolder': {\n              itemsRoot = legacyStaticFolderPath\n              break\n            }\n            case 'publicFolder': {\n              itemsRoot = publicFolderPath\n              break\n            }\n            default: {\n              break\n            }\n          }\n\n          if (itemsRoot && curItemPath) {\n            fsPath = path.posix.join(itemsRoot, curItemPath)\n          }\n\n          // dynamically check fs in development so we don't\n          // have to wait on the watcher\n          if (!matchedItem && opts.dev) {\n            const isStaticAsset = (\n              [\n                'nextStaticFolder',\n                'publicFolder',\n                'legacyStaticFolder',\n              ] as (typeof type)[]\n            ).includes(type)\n\n            if (isStaticAsset && itemsRoot) {\n              let found = fsPath && (await fileExists(fsPath, FileType.File))\n\n              if (!found) {\n                try {\n                  // In dev, we ensure encoded paths match\n                  // decoded paths on the filesystem so check\n                  // that variation as well\n                  const tempItemPath = decodeURIComponent(curItemPath)\n                  fsPath = path.posix.join(itemsRoot, tempItemPath)\n                  found = await fileExists(fsPath, FileType.File)\n                } catch {}\n\n                if (!found) {\n                  continue\n                }\n              }\n            } else if (type === 'pageFile' || type === 'appFile') {\n              const isAppFile = type === 'appFile'\n              if (\n                ensureFn &&\n                (await ensureFn({\n                  type,\n                  itemPath: isAppFile\n                    ? normalizeMetadataRoute(curItemPath)\n                    : curItemPath,\n                })?.catch(() => 'ENSURE_FAILED')) === 'ENSURE_FAILED'\n              ) {\n                continue\n              }\n            } else {\n              continue\n            }\n          }\n\n          // i18n locales aren't matched for app dir\n          if (type === 'appFile' && locale && locale !== i18n?.defaultLocale) {\n            continue\n          }\n\n          const itemResult = {\n            type,\n            fsPath,\n            locale,\n            itemsRoot,\n            itemPath: curItemPath,\n          }\n\n          getItemsLru?.set(itemKey, itemResult)\n          return itemResult\n        }\n      }\n\n      getItemsLru?.set(itemKey, null)\n      return null\n    },\n    getDynamicRoutes() {\n      // this should include data routes\n      return this.dynamicRoutes\n    },\n    getMiddlewareMatchers() {\n      return this.middlewareMatcher\n    },\n  }\n}\n"], "names": ["buildCustomRoute", "setupFsCheck", "debug", "setupDebug", "type", "item", "basePath", "caseSensitive", "restrictedRedirectPaths", "map", "p", "match", "getPathMatch", "source", "strict", "removeUnnamedP<PERSON>ms", "regexModifier", "internal", "regex", "modifyRouteRegex", "undefined", "sensitive", "check", "opts", "getItemsLru", "dev", "L<PERSON><PERSON><PERSON>", "length", "value", "fsPath", "itemPath", "nextDataRoutes", "Set", "publicFolderItems", "nextStaticFolderItems", "legacyStaticFolderItems", "appFiles", "pageFiles", "dynamicRoutes", "middlewareMatcher", "distDir", "path", "join", "dir", "config", "publicFolderPath", "nextStaticFolderPath", "legacyStaticFolderPath", "customRoutes", "redirects", "rewrites", "beforeFiles", "afterFiles", "fallback", "headers", "buildId", "prerenderManifest", "middlewareManifest", "buildIdPath", "BUILD_ID_FILE", "fs", "readFile", "err", "code", "Error", "file", "recursiveReadDir", "add", "encodeURIPath", "normalizePathSep", "Log", "warn", "posix", "output", "routesManifestPath", "ROUTES_MANIFEST", "prerenderManifestPath", "PRERENDER_MANIFEST", "middlewareManifestPath", "MIDDLEWARE_MANIFEST", "functionsConfigManifestPath", "FUNCTIONS_CONFIG_MANIFEST", "pagesManifestPath", "PAGES_MANIFEST", "appRoutesManifestPath", "APP_PATH_ROUTES_MANIFEST", "routesManifest", "JSON", "parse", "catch", "functionsConfigManifest", "pagesManifest", "appRoutesManifest", "key", "Object", "keys", "i18n", "normalizeLocalePath", "locales", "pathname", "escapedBuildId", "escapeStringRegexp", "route", "dataRoutes", "isDynamicRoute", "page", "routeRegex", "getRouteRegex", "push", "re", "toString", "getRouteMatcher", "RegExp", "dataRouteRegex", "replace", "groups", "middleware", "matchers", "getMiddlewareRouteMatcher", "functions", "regexp", "originalSource", "Array", "isArray", "loadCustomRoutes", "version", "routes", "notFoundRoutes", "preview", "previewModeId", "require", "randomBytes", "previewModeSigningKey", "previewModeEncryptionKey", "experimental", "caseSensitiveRoutes", "handleLocale", "locale", "i18nResult", "detectedLocale", "ensureFn", "normalizers", "rsc", "RSCPathnameNormalizer", "prefetchRSC", "ppr", "PrefetchRSCPathnameNormalizer", "exportPathMapRoutes", "devVirtualFsItems", "ensure<PERSON><PERSON>back", "fn", "getItem", "originalItemPath", "itemKey", "lruResult", "get", "has<PERSON>ase<PERSON><PERSON>", "pathHasPrefix", "removePathPrefix", "minimalMode", "normalize", "endsWith", "substring", "decodedItemPath", "decodeURIComponent", "itemsToCheck", "items", "curI<PERSON><PERSON><PERSON>", "curDecodedItemPath", "isDynamicOutput", "localeResult", "defaultLocale", "domains", "nextDataPrefix", "startsWith", "curLocaleResult", "matchedItem", "has", "encodedCurItemPath", "itemsRoot", "isStaticAsset", "includes", "found", "fileExists", "FileType", "File", "tempItemPath", "isAppFile", "normalizeMetadataRoute", "itemResult", "set", "getDynamicRoutes", "getMiddlewareMatchers"], "mappings": ";;;;;;;;;;;;;;;IAsEaA,gBAAgB;eAAhBA;;IA4BSC,YAAY;eAAZA;;;6DAtFL;iEACF;6DACM;8DACE;0BACE;yEACsB;gCACd;4BACI;kCACJ;uBACF;8BACI;2BACN;4BACC;8BACE;+BACF;qCACM;kCACH;wCACS;2BASnC;kCAC0B;kCACM;qBACD;6BACQ;+BAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB9B,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AASlB,MAAMH,mBAAmB,CAC9BI,MACAC,MACAC,UACAC;IAEA,MAAMC,0BAA0B;QAAC;KAAS,CAACC,GAAG,CAAC,CAACC,IAC9CJ,WAAW,GAAGA,WAAWI,GAAG,GAAGA;IAEjC,MAAMC,QAAQC,IAAAA,uBAAY,EAACP,KAAKQ,MAAM,EAAE;QACtCC,QAAQ;QACRC,qBAAqB;QACrBC,eAAe,CAAC,AAACX,KAAaY,QAAQ,GAClC,CAACC,QACCC,IAAAA,gCAAgB,EACdD,OACAd,SAAS,aAAaI,0BAA0BY,aAEpDA;QACJC,WAAWd;IACb;IACA,OAAO;QACL,GAAGF,IAAI;QACP,GAAID,SAAS,YAAY;YAAEkB,OAAO;QAAK,IAAI,CAAC,CAAC;QAC7CX;IACF;AACF;AAEO,eAAeV,aAAasB,IAQlC;IACC,MAAMC,cAAc,CAACD,KAAKE,GAAG,GACzB,IAAIC,kBAAQ,CAAkB,OAAO,MAAM,SAASC,OAAOC,KAAK;QAC9D,IAAI,CAACA,OAAO,OAAO;QACnB,OACE,AAACA,CAAAA,MAAMC,MAAM,IAAI,EAAC,EAAGF,MAAM,GAC3BC,MAAME,QAAQ,CAACH,MAAM,GACrBC,MAAMxB,IAAI,CAACuB,MAAM;IAErB,KACAP;IAEJ,kDAAkD;IAClD,MAAMW,iBAAiB,IAAIC;IAC3B,MAAMC,oBAAoB,IAAID;IAC9B,MAAME,wBAAwB,IAAIF;IAClC,MAAMG,0BAA0B,IAAIH;IAEpC,MAAMI,WAAW,IAAIJ;IACrB,MAAMK,YAAY,IAAIL;IACtB,IAAIM,gBAA0C,EAAE;IAEhD,IAAIC,oBAEY,IAAM;IAEtB,MAAMC,UAAUC,aAAI,CAACC,IAAI,CAACnB,KAAKoB,GAAG,EAAEpB,KAAKqB,MAAM,CAACJ,OAAO;IACvD,MAAMK,mBAAmBJ,aAAI,CAACC,IAAI,CAACnB,KAAKoB,GAAG,EAAE;IAC7C,MAAMG,uBAAuBL,aAAI,CAACC,IAAI,CAACF,SAAS;IAChD,MAAMO,yBAAyBN,aAAI,CAACC,IAAI,CAACnB,KAAKoB,GAAG,EAAE;IACnD,IAAIK,eAAmE;QACrEC,WAAW,EAAE;QACbC,UAAU;YACRC,aAAa,EAAE;YACfC,YAAY,EAAE;YACdC,UAAU,EAAE;QACd;QACAC,SAAS,EAAE;IACb;IACA,IAAIC,UAAU;IACd,IAAIC;IAEJ,IAAI,CAACjC,KAAKE,GAAG,EAAE;YAuITgC,iCAAAA;QAtIJ,MAAMC,cAAcjB,aAAI,CAACC,IAAI,CAACnB,KAAKoB,GAAG,EAAEpB,KAAKqB,MAAM,CAACJ,OAAO,EAAEmB,wBAAa;QAC1E,IAAI;YACFJ,UAAU,MAAMK,iBAAE,CAACC,QAAQ,CAACH,aAAa;QAC3C,EAAE,OAAOI,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU,MAAMD;YACjC,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,0CAA0C,EAAEzC,KAAKqB,MAAM,CAACJ,OAAO,CAAC,yJAAyJ,CAAC,GADvN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI;YACF,KAAK,MAAMyB,QAAQ,CAAA,MAAMC,IAAAA,kCAAgB,EAACrB,iBAAgB,EAAG;gBAC3D,6CAA6C;gBAC7CZ,kBAAkBkC,GAAG,CAACC,IAAAA,4BAAa,EAACC,IAAAA,kCAAgB,EAACJ;YACvD;QACF,EAAE,OAAOH,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAMG,QAAQ,CAAA,MAAMC,IAAAA,kCAAgB,EAACnB,uBAAsB,EAAG;gBACjE,6CAA6C;gBAC7CZ,wBAAwBgC,GAAG,CAACC,IAAAA,4BAAa,EAACC,IAAAA,kCAAgB,EAACJ;YAC7D;YACAK,KAAIC,IAAI,CACN,CAAC,iIAAiI,CAAC;QAEvI,EAAE,OAAOT,KAAU;YACjB,IAAIA,IAAIC,IAAI,KAAK,UAAU;gBACzB,MAAMD;YACR;QACF;QAEA,IAAI;YACF,KAAK,MAAMG,QAAQ,CAAA,MAAMC,IAAAA,kCAAgB,EAACpB,qBAAoB,EAAG;gBAC/D,6CAA6C;gBAC7CZ,sBAAsBiC,GAAG,CACvB1B,aAAI,CAAC+B,KAAK,CAAC9B,IAAI,CACb,iBACA0B,IAAAA,4BAAa,EAACC,IAAAA,kCAAgB,EAACJ;YAGrC;QACF,EAAE,OAAOH,KAAK;YACZ,IAAIvC,KAAKqB,MAAM,CAAC6B,MAAM,KAAK,cAAc,MAAMX;QACjD;QAEA,MAAMY,qBAAqBjC,aAAI,CAACC,IAAI,CAACF,SAASmC,0BAAe;QAC7D,MAAMC,wBAAwBnC,aAAI,CAACC,IAAI,CAACF,SAASqC,6BAAkB;QACnE,MAAMC,yBAAyBrC,aAAI,CAACC,IAAI,CACtCF,SACA,UACAuC,8BAAmB;QAErB,MAAMC,8BAA8BvC,aAAI,CAACC,IAAI,CAC3CF,SACA,UACAyC,oCAAyB;QAE3B,MAAMC,oBAAoBzC,aAAI,CAACC,IAAI,CAACF,SAAS,UAAU2C,yBAAc;QACrE,MAAMC,wBAAwB3C,aAAI,CAACC,IAAI,CAACF,SAAS6C,mCAAwB;QAEzE,MAAMC,iBAAiBC,KAAKC,KAAK,CAC/B,MAAM5B,iBAAE,CAACC,QAAQ,CAACa,oBAAoB;QAGxClB,oBAAoB+B,KAAKC,KAAK,CAC5B,MAAM5B,iBAAE,CAACC,QAAQ,CAACe,uBAAuB;QAG3C,MAAMnB,qBAAqB8B,KAAKC,KAAK,CACnC,MAAM5B,iBAAE,CAACC,QAAQ,CAACiB,wBAAwB,QAAQW,KAAK,CAAC,IAAM;QAGhE,MAAMC,0BAA0BH,KAAKC,KAAK,CACxC,MAAM5B,iBAAE,CAACC,QAAQ,CAACmB,6BAA6B,QAAQS,KAAK,CAAC,IAAM;QAGrE,MAAME,gBAAgBJ,KAAKC,KAAK,CAC9B,MAAM5B,iBAAE,CAACC,QAAQ,CAACqB,mBAAmB;QAEvC,MAAMU,oBAAoBL,KAAKC,KAAK,CAClC,MAAM5B,iBAAE,CAACC,QAAQ,CAACuB,uBAAuB,QAAQK,KAAK,CAAC,IAAM;QAG/D,KAAK,MAAMI,OAAOC,OAAOC,IAAI,CAACJ,eAAgB;YAC5C,8CAA8C;YAC9C,IAAIpE,KAAKqB,MAAM,CAACoD,IAAI,EAAE;gBACpB3D,UAAU8B,GAAG,CACX8B,IAAAA,wCAAmB,EAACJ,KAAKtE,KAAKqB,MAAM,CAACoD,IAAI,CAACE,OAAO,EAAEC,QAAQ;YAE/D,OAAO;gBACL9D,UAAU8B,GAAG,CAAC0B;YAChB;QACF;QACA,KAAK,MAAMA,OAAOC,OAAOC,IAAI,CAACH,mBAAoB;YAChDxD,SAAS+B,GAAG,CAACyB,iBAAiB,CAACC,IAAI;QACrC;QAEA,MAAMO,iBAAiBC,IAAAA,gCAAkB,EAAC9C;QAE1C,KAAK,MAAM+C,SAAShB,eAAeiB,UAAU,CAAE;YAC7C,IAAIC,IAAAA,qBAAc,EAACF,MAAMG,IAAI,GAAG;gBAC9B,MAAMC,aAAaC,IAAAA,yBAAa,EAACL,MAAMG,IAAI;gBAC3CnE,cAAcsE,IAAI,CAAC;oBACjB,GAAGN,KAAK;oBACRpF,OAAOwF,WAAWG,EAAE,CAACC,QAAQ;oBAC7BnG,OAAOoG,IAAAA,6BAAe,EAAC;wBACrB,+DAA+D;wBAC/D,uCAAuC;wBACvCF,IAAItF,KAAKqB,MAAM,CAACoD,IAAI,GAChB,IAAIgB,OACFV,MAAMW,cAAc,CAACC,OAAO,CAC1B,CAAC,CAAC,EAAEd,eAAe,CAAC,CAAC,EACrB,CAAC,CAAC,EAAEA,eAAe,uBAAuB,CAAC,KAG/C,IAAIY,OAAOV,MAAMW,cAAc;wBACnCE,QAAQT,WAAWS,MAAM;oBAC3B;gBACF;YACF;YACApF,eAAeoC,GAAG,CAACmC,MAAMG,IAAI;QAC/B;QAEA,KAAK,MAAMH,SAAShB,eAAehD,aAAa,CAAE;YAChDA,cAAcsE,IAAI,CAAC;gBACjB,GAAGN,KAAK;gBACR3F,OAAOoG,IAAAA,6BAAe,EAACJ,IAAAA,yBAAa,EAACL,MAAMG,IAAI;YACjD;QACF;QAEA,KAAIhD,iCAAAA,mBAAmB2D,UAAU,sBAA7B3D,kCAAAA,8BAA+B,CAAC,IAAI,qBAApCA,gCAAsC4D,QAAQ,EAAE;gBAEhD5D,kCAAAA;YADFlB,oBAAoB+E,IAAAA,iDAAyB,GAC3C7D,kCAAAA,mBAAmB2D,UAAU,sBAA7B3D,mCAAAA,+BAA+B,CAAC,IAAI,qBAApCA,iCAAsC4D,QAAQ;QAElD,OAAO,IAAI3B,2CAAAA,wBAAyB6B,SAAS,CAAC,eAAe,EAAE;YAC7DhF,oBAAoB+E,IAAAA,iDAAyB,EAC3C5B,wBAAwB6B,SAAS,CAAC,eAAe,CAACF,QAAQ,IAAI;gBAC5D;oBAAEG,QAAQ;oBAAMC,gBAAgB;gBAAU;aAC3C;QAEL;QAEAzE,eAAe;YACbC,WAAWqC,eAAerC,SAAS;YACnCC,UAAUoC,eAAepC,QAAQ,GAC7BwE,MAAMC,OAAO,CAACrC,eAAepC,QAAQ,IACnC;gBACEC,aAAa,EAAE;gBACfC,YAAYkC,eAAepC,QAAQ;gBACnCG,UAAU,EAAE;YACd,IACAiC,eAAepC,QAAQ,GACzB;gBACEC,aAAa,EAAE;gBACfC,YAAY,EAAE;gBACdC,UAAU,EAAE;YACd;YACJC,SAASgC,eAAehC,OAAO;QACjC;IACF,OAAO;QACL,eAAe;QACfN,eAAe,MAAM4E,IAAAA,yBAAgB,EAACrG,KAAKqB,MAAM;QAEjDY,oBAAoB;YAClBqE,SAAS;YACTC,QAAQ,CAAC;YACTxF,eAAe,CAAC;YAChByF,gBAAgB,EAAE;YAClBC,SAAS;gBACPC,eAAeC,QAAQ,UAAUC,WAAW,CAAC,IAAIrB,QAAQ,CAAC;gBAC1DsB,uBAAuBF,QAAQ,UAC5BC,WAAW,CAAC,IACZrB,QAAQ,CAAC;gBACZuB,0BAA0BH,QAAQ,UAC/BC,WAAW,CAAC,IACZrB,QAAQ,CAAC;YACd;QACF;IACF;IAEA,MAAMxD,UAAUN,aAAaM,OAAO,CAAC7C,GAAG,CAAC,CAACJ,OACxCL,iBACE,UACAK,MACAkB,KAAKqB,MAAM,CAACtC,QAAQ,EACpBiB,KAAKqB,MAAM,CAAC0F,YAAY,CAACC,mBAAmB;IAGhD,MAAMtF,YAAYD,aAAaC,SAAS,CAACxC,GAAG,CAAC,CAACJ,OAC5CL,iBACE,YACAK,MACAkB,KAAKqB,MAAM,CAACtC,QAAQ,EACpBiB,KAAKqB,MAAM,CAAC0F,YAAY,CAACC,mBAAmB;IAGhD,MAAMrF,WAAW;QACfC,aAAaH,aAAaE,QAAQ,CAACC,WAAW,CAAC1C,GAAG,CAAC,CAACJ,OAClDL,iBAAiB,wBAAwBK;QAE3C+C,YAAYJ,aAAaE,QAAQ,CAACE,UAAU,CAAC3C,GAAG,CAAC,CAACJ,OAChDL,iBACE,WACAK,MACAkB,KAAKqB,MAAM,CAACtC,QAAQ,EACpBiB,KAAKqB,MAAM,CAAC0F,YAAY,CAACC,mBAAmB;QAGhDlF,UAAUL,aAAaE,QAAQ,CAACG,QAAQ,CAAC5C,GAAG,CAAC,CAACJ,OAC5CL,iBACE,WACAK,MACAkB,KAAKqB,MAAM,CAACtC,QAAQ,EACpBiB,KAAKqB,MAAM,CAAC0F,YAAY,CAACC,mBAAmB;IAGlD;IAEA,MAAM,EAAEvC,IAAI,EAAE,GAAGzE,KAAKqB,MAAM;IAE5B,MAAM4F,eAAe,CAACrC,UAAkBD;QACtC,IAAIuC;QAEJ,IAAIzC,MAAM;YACR,MAAM0C,aAAazC,IAAAA,wCAAmB,EAACE,UAAUD,WAAWF,KAAKE,OAAO;YAExEC,WAAWuC,WAAWvC,QAAQ;YAC9BsC,SAASC,WAAWC,cAAc;QACpC;QACA,OAAO;YAAEF;YAAQtC;QAAS;IAC5B;IAEAjG,MAAM,kBAAkB6B;IACxB7B,MAAM,iBAAiBoC;IACvBpC,MAAM,gBAAgB8C;IACtB9C,MAAM,qBAAqB+B;IAC3B/B,MAAM,yBAAyBgC;IAC/BhC,MAAM,aAAamC;IACnBnC,MAAM,YAAYkC;IAElB,IAAIwG;IAEJ,MAAMC,cAAc;QAClB,uEAAuE;QACvE,+BAA+B;QAC/BC,KAAK,IAAIC,0BAAqB;QAC9BC,aAAazH,KAAKqB,MAAM,CAAC0F,YAAY,CAACW,GAAG,GACrC,IAAIC,0CAA6B,KACjC9H;IACN;IAEA,OAAO;QACLkC;QACAJ;QACAD;QAEAM;QACAiF;QAEApG;QACAC;QACAC;QACAP;QAEAoH,qBAAqB/H;QAIrBgI,mBAAmB,IAAIpH;QAEvBwB;QACAjB,mBAAmBA;QAEnB8G,gBAAeC,EAAmB;YAChCV,WAAWU;QACb;QAEA,MAAMC,SAAQzH,QAAgB;YAC5B,MAAM0H,mBAAmB1H;YACzB,MAAM2H,UAAUD;YAChB,MAAME,YAAYlI,+BAAAA,YAAamI,GAAG,CAACF;YAEnC,IAAIC,WAAW;gBACb,OAAOA;YACT;YAEA,MAAM,EAAEpJ,QAAQ,EAAE,GAAGiB,KAAKqB,MAAM;YAEhC,MAAMgH,cAAcC,IAAAA,4BAAa,EAAC/H,UAAUxB;YAE5C,kDAAkD;YAClD,IAAIA,YAAY,CAACsJ,aAAa;gBAC5B,OAAO;YACT;YAEA,gCAAgC;YAChC,IAAItJ,YAAYsJ,aAAa;gBAC3B9H,WAAWgI,IAAAA,kCAAgB,EAAChI,UAAUxB,aAAa;YACrD;YAEA,kEAAkE;YAClE,YAAY;YACZ,IAAIiB,KAAKwI,WAAW,EAAE;oBAChBlB;gBAAJ,KAAIA,2BAAAA,YAAYG,WAAW,qBAAvBH,yBAAyBlI,KAAK,CAACmB,WAAW;oBAC5CA,WAAW+G,YAAYG,WAAW,CAACgB,SAAS,CAAClI,UAAU;gBACzD,OAAO,IAAI+G,YAAYC,GAAG,CAACnI,KAAK,CAACmB,WAAW;oBAC1CA,WAAW+G,YAAYC,GAAG,CAACkB,SAAS,CAAClI,UAAU;gBACjD;YACF;YAEA,IAAIA,aAAa,OAAOA,SAASmI,QAAQ,CAAC,MAAM;gBAC9CnI,WAAWA,SAASoI,SAAS,CAAC,GAAGpI,SAASH,MAAM,GAAG;YACrD;YAEA,IAAIwI,kBAAkBrI;YAEtB,IAAI;gBACFqI,kBAAkBC,mBAAmBtI;YACvC,EAAE,OAAM,CAAC;YAET,IAAIA,aAAa,gBAAgB;gBAC/B,OAAO;oBACLA;oBACA1B,MAAM;gBACR;YACF;YAEA,MAAMiK,eAAuD;gBAC3D;oBAAC,IAAI,CAACjB,iBAAiB;oBAAE;iBAAmB;gBAC5C;oBAAClH;oBAAuB;iBAAmB;gBAC3C;oBAACC;oBAAyB;iBAAqB;gBAC/C;oBAACF;oBAAmB;iBAAe;gBACnC;oBAACG;oBAAU;iBAAU;gBACrB;oBAACC;oBAAW;iBAAW;aACxB;YAED,KAAK,IAAI,CAACiI,OAAOlK,KAAK,IAAIiK,aAAc;gBACtC,IAAI5B;gBACJ,IAAI8B,cAAczI;gBAClB,IAAI0I,qBAAqBL;gBAEzB,MAAMM,kBAAkBrK,SAAS,cAAcA,SAAS;gBAExD,IAAI4F,MAAM;wBAUIA;oBATZ,MAAM0E,eAAelC,aACnB1G,UACA,sDAAsD;oBACtD,qCAAqC;oBACrC2I,kBACIrJ,YACA;wBACE4E,wBAAAA,KAAM2E,aAAa;wBACnB,sDAAsD;2BAClD3E,EAAAA,gBAAAA,KAAK4E,OAAO,qBAAZ5E,cAAcvF,GAAG,CAAC,CAACJ,OAASA,KAAKsK,aAAa,MAAK,EAAE;qBAC1D;oBAGP,IAAID,aAAavE,QAAQ,KAAKoE,aAAa;wBACzCA,cAAcG,aAAavE,QAAQ;wBACnCsC,SAASiC,aAAajC,MAAM;wBAE5B,IAAI;4BACF+B,qBAAqBJ,mBAAmBG;wBAC1C,EAAE,OAAM,CAAC;oBACX;gBACF;gBAEA,IAAInK,SAAS,sBAAsB;oBACjC,IAAI,CAACyJ,IAAAA,4BAAa,EAACU,aAAa,YAAY;wBAC1C;oBACF;oBACAA,cAAcA,YAAYL,SAAS,CAAC,UAAUvI,MAAM;oBAEpD,IAAI;wBACF6I,qBAAqBJ,mBAAmBG;oBAC1C,EAAE,OAAM,CAAC;gBACX;gBAEA,IACEnK,SAAS,sBACT,CAACyJ,IAAAA,4BAAa,EAACU,aAAa,kBAC5B;oBACA;gBACF;gBAEA,MAAMM,iBAAiB,CAAC,YAAY,EAAEtH,QAAQ,CAAC,CAAC;gBAEhD,IACEnD,SAAS,cACTmK,YAAYO,UAAU,CAACD,mBACvBN,YAAYN,QAAQ,CAAC,UACrB;oBACAK,QAAQvI;oBACR,sCAAsC;oBACtCwI,cAAcA,YAAYL,SAAS,CAACW,eAAelJ,MAAM,GAAG;oBAE5D,uBAAuB;oBACvB4I,cAAcA,YAAYL,SAAS,CACjC,GACAK,YAAY5I,MAAM,GAAG,QAAQA,MAAM;oBAErC,MAAMoJ,kBAAkBvC,aAAa+B;oBACrCA,cACEQ,gBAAgB5E,QAAQ,KAAK,WACzB,MACA4E,gBAAgB5E,QAAQ;oBAE9BsC,SAASsC,gBAAgBtC,MAAM;oBAE/B,IAAI;wBACF+B,qBAAqBJ,mBAAmBG;oBAC1C,EAAE,OAAM,CAAC;gBACX;gBAEA,IAAIS,cAAcV,MAAMW,GAAG,CAACV;gBAE5B,gCAAgC;gBAChC,IAAI,CAACS,eAAe,CAACzJ,KAAKE,GAAG,EAAE;oBAC7BuJ,cAAcV,MAAMW,GAAG,CAACT;oBACxB,IAAIQ,aAAaT,cAAcC;yBAC1B;wBACH,wDAAwD;wBACxD,yGAAyG;wBACzG,wFAAwF;wBACxF,gFAAgF;wBAChF,oFAAoF;wBACpF,IAAI;4BACF,4FAA4F;4BAC5F,MAAMU,qBAAqB9G,IAAAA,4BAAa,EAACmG;4BACzCS,cAAcV,MAAMW,GAAG,CAACC;wBAC1B,EAAE,OAAM,CAAC;oBACX;gBACF;gBAEA,IAAIF,eAAezJ,KAAKE,GAAG,EAAE;oBAC3B,IAAII;oBACJ,IAAIsJ;oBAEJ,OAAQ/K;wBACN,KAAK;4BAAoB;gCACvB+K,YAAYrI;gCACZyH,cAAcA,YAAYL,SAAS,CAAC,gBAAgBvI,MAAM;gCAC1D;4BACF;wBACA,KAAK;4BAAsB;gCACzBwJ,YAAYpI;gCACZ;4BACF;wBACA,KAAK;4BAAgB;gCACnBoI,YAAYtI;gCACZ;4BACF;wBACA;4BAAS;gCACP;4BACF;oBACF;oBAEA,IAAIsI,aAAaZ,aAAa;wBAC5B1I,SAASY,aAAI,CAAC+B,KAAK,CAAC9B,IAAI,CAACyI,WAAWZ;oBACtC;oBAEA,kDAAkD;oBAClD,8BAA8B;oBAC9B,IAAI,CAACS,eAAezJ,KAAKE,GAAG,EAAE;wBAC5B,MAAM2J,gBAAgB,AACpB;4BACE;4BACA;4BACA;yBACD,CACDC,QAAQ,CAACjL;wBAEX,IAAIgL,iBAAiBD,WAAW;4BAC9B,IAAIG,QAAQzJ,UAAW,MAAM0J,IAAAA,sBAAU,EAAC1J,QAAQ2J,oBAAQ,CAACC,IAAI;4BAE7D,IAAI,CAACH,OAAO;gCACV,IAAI;oCACF,wCAAwC;oCACxC,2CAA2C;oCAC3C,yBAAyB;oCACzB,MAAMI,eAAetB,mBAAmBG;oCACxC1I,SAASY,aAAI,CAAC+B,KAAK,CAAC9B,IAAI,CAACyI,WAAWO;oCACpCJ,QAAQ,MAAMC,IAAAA,sBAAU,EAAC1J,QAAQ2J,oBAAQ,CAACC,IAAI;gCAChD,EAAE,OAAM,CAAC;gCAET,IAAI,CAACH,OAAO;oCACV;gCACF;4BACF;wBACF,OAAO,IAAIlL,SAAS,cAAcA,SAAS,WAAW;gCAI3CwI;4BAHT,MAAM+C,YAAYvL,SAAS;4BAC3B,IACEwI,YACA,AAAC,QAAMA,YAAAA,SAAS;gCACdxI;gCACA0B,UAAU6J,YACNC,IAAAA,wCAAsB,EAACrB,eACvBA;4BACN,uBALO3B,UAKHnD,KAAK,CAAC,IAAM,sBAAsB,iBACtC;gCACA;4BACF;wBACF,OAAO;4BACL;wBACF;oBACF;oBAEA,0CAA0C;oBAC1C,IAAIrF,SAAS,aAAaqI,UAAUA,YAAWzC,wBAAAA,KAAM2E,aAAa,GAAE;wBAClE;oBACF;oBAEA,MAAMkB,aAAa;wBACjBzL;wBACAyB;wBACA4G;wBACA0C;wBACArJ,UAAUyI;oBACZ;oBAEA/I,+BAAAA,YAAasK,GAAG,CAACrC,SAASoC;oBAC1B,OAAOA;gBACT;YACF;YAEArK,+BAAAA,YAAasK,GAAG,CAACrC,SAAS;YAC1B,OAAO;QACT;QACAsC;YACE,kCAAkC;YAClC,OAAO,IAAI,CAACzJ,aAAa;QAC3B;QACA0J;YACE,OAAO,IAAI,CAACzJ,iBAAiB;QAC/B;IACF;AACF"}