{"version": 3, "sources": ["../../../src/server/normalizers/absolute-filename-normalizer.ts"], "sourcesContent": ["import type { PAGE_TYPES } from '../../lib/page-types'\nimport { absolutePathToPage } from '../../shared/lib/page-path/absolute-path-to-page'\nimport type { Normalizer } from './normalizer'\n\n/**\n * Normalizes a given filename so that it's relative to the provided directory.\n * It will also strip the extension (if provided) and the trailing `/index`.\n */\nexport class AbsoluteFilenameNormalizer implements Normalizer {\n  /**\n   *\n   * @param dir the directory for which the files should be made relative to\n   * @param extensions the extensions the file could have\n   * @param keepIndex when `true` the trailing `/index` is _not_ removed\n   */\n  constructor(\n    private readonly dir: string,\n    private readonly extensions: ReadonlyArray<string>,\n    private readonly pagesType: PAGE_TYPES\n  ) {}\n\n  public normalize(filename: string): string {\n    return absolutePathToPage(filename, {\n      extensions: this.extensions,\n      keepIndex: false,\n      dir: this.dir,\n      pagesType: this.pagesType,\n    })\n  }\n}\n"], "names": ["AbsoluteFilenameNormalizer", "constructor", "dir", "extensions", "pagesType", "normalize", "filename", "absolutePathToPage", "keepIndex"], "mappings": ";;;;+BAQaA;;;eAAAA;;;oCAPsB;AAO5B,MAAMA;IACX;;;;;GAKC,GACDC,YACE,AAAiBC,GAAW,EAC5B,AAAiBC,UAAiC,EAClD,AAAiBC,SAAqB,CACtC;aAHiBF,MAAAA;aACAC,aAAAA;aACAC,YAAAA;IAChB;IAEIC,UAAUC,QAAgB,EAAU;QACzC,OAAOC,IAAAA,sCAAkB,EAACD,UAAU;YAClCH,YAAY,IAAI,CAACA,UAAU;YAC3BK,WAAW;YACXN,KAAK,IAAI,CAACA,GAAG;YACbE,WAAW,IAAI,CAACA,SAAS;QAC3B;IACF;AACF"}