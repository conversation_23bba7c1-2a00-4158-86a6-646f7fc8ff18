{"version": 3, "sources": ["../../src/client/link.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  NextRouter,\n  PrefetchOptions as RouterPrefetchOptions,\n} from '../shared/lib/router/router'\n\nimport React, { createContext, useContext } from 'react'\nimport type { UrlObject } from 'url'\nimport { resolveHref } from './resolve-href'\nimport { isLocalURL } from '../shared/lib/router/utils/is-local-url'\nimport { formatUrl } from '../shared/lib/router/utils/format-url'\nimport { isAbsoluteUrl } from '../shared/lib/utils'\nimport { addLocale } from './add-locale'\nimport { RouterContext } from '../shared/lib/router-context.shared-runtime'\nimport type { AppRouterInstance } from '../shared/lib/app-router-context.shared-runtime'\nimport { useIntersection } from './use-intersection'\nimport { getDomainLocale } from './get-domain-locale'\nimport { addBasePath } from './add-base-path'\nimport { useMergedRef } from './use-merged-ref'\nimport { errorOnce } from '../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * The path or URL to navigate to. It can also be an object.\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#with-url-object\n   */\n  href: Url\n  /**\n   * Optional decorator for the path that will be shown in the browser URL bar. Before Next.js 9.5.3 this was used for dynamic routes, check our [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes) to see how it worked. Note: when this path differs from the one provided in `href` the previous `href`/`as` behavior is used as shown in the [previous docs](https://github.com/vercel/next.js/blob/v9.5.2/docs/api-reference/next/link.md#dynamic-routes).\n   */\n  as?: Url\n  /**\n   * Replace the current `history` state instead of adding a new url into the stack.\n   *\n   * @defaultValue `false`\n   */\n  replace?: boolean\n  /**\n   * Whether to override the default scroll behavior\n   *\n   * @example https://nextjs.org/docs/api-reference/next/link#disable-scrolling-to-the-top-of-the-page\n   *\n   * @defaultValue `true`\n   */\n  scroll?: boolean\n  /**\n   * Update the path of the current page without rerunning [`getStaticProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-static-props), [`getServerSideProps`](https://nextjs.org/docs/pages/building-your-application/data-fetching/get-server-side-props) or [`getInitialProps`](/docs/pages/api-reference/functions/get-initial-props).\n   *\n   * @defaultValue `false`\n   */\n  shallow?: boolean\n  /**\n   * Forces `Link` to send the `href` property to its child.\n   *\n   * @defaultValue `false`\n   */\n  passHref?: boolean\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`. Prefetching is only enabled in production.\n   *\n   * In App Router:\n   * - `null` (default): For statically generated pages, this will prefetch the full React Server Component data. For dynamic pages, this will prefetch up to the nearest route segment with a [`loading.js`](https://nextjs.org/docs/app/api-reference/file-conventions/loading) file. If there is no loading file, it will not fetch the full tree to avoid fetching too much data.\n   * - `true`: This will prefetch the full React Server Component data for all route segments, regardless of whether they contain a segment with `loading.js`.\n   * - `false`: This will not prefetch any data, even on hover.\n   *\n   * In Pages Router:\n   * - `true` (default): The full route & its data will be prefetched.\n   * - `false`: Prefetching will not happen when entering the viewport, but will still happen on hover.\n   * @defaultValue `true` (pages router) or `null` (app router)\n   */\n  prefetch?: boolean | null\n  /**\n   * The active locale is automatically prepended. `locale` allows for providing a different locale.\n   * When `false` `href` has to include the locale as the default behavior is disabled.\n   * Note: This is only available in the Pages Router.\n   */\n  locale?: string | false\n  /**\n   * Enable legacy link behavior.\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n  /**\n   * Optional event handler for when the mouse pointer is moved onto Link\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when Link is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<InternalLinkProps>\n\nconst prefetched = new Set<string>()\n\ntype PrefetchOptions = RouterPrefetchOptions & {\n  /**\n   * bypassPrefetchedCheck will bypass the check to see if the `href` has\n   * already been fetched.\n   */\n  bypassPrefetchedCheck?: boolean\n}\n\nfunction prefetch(\n  router: NextRouter,\n  href: string,\n  as: string,\n  options: PrefetchOptions\n): void {\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    return\n  }\n\n  // We should only dedupe requests when experimental.optimisticClientCache is\n  // disabled.\n  if (!options.bypassPrefetchedCheck) {\n    const locale =\n      // Let the link's locale prop override the default router locale.\n      typeof options.locale !== 'undefined'\n        ? options.locale\n        : // Otherwise fallback to the router's locale.\n          'locale' in router\n          ? router.locale\n          : undefined\n\n    const prefetchedKey = href + '%' + as + '%' + locale\n\n    // If we've already fetched the key, then don't prefetch it again!\n    if (prefetched.has(prefetchedKey)) {\n      return\n    }\n\n    // Mark this URL as prefetched.\n    prefetched.add(prefetchedKey)\n  }\n\n  // Prefetch the JSON page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  router.prefetch(href, as, options).catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n}\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter | AppRouterInstance,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean,\n  locale?: string | false,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    // If the router is an NextRouter instance it will have `beforePopState`\n    const routerScroll = scroll ?? true\n    if ('beforePopState' in router) {\n      router[replace ? 'replace' : 'push'](href, as, {\n        shallow,\n        locale,\n        scroll: routerScroll,\n      })\n    } else {\n      router[replace ? 'replace' : 'push'](as || href, {\n        scroll: routerScroll,\n      })\n    }\n  }\n\n  navigate()\n}\n\ntype LinkPropsReal = React.PropsWithChildren<\n  Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, keyof LinkProps> &\n    LinkProps\n>\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */\nconst Link = React.forwardRef<HTMLAnchorElement, LinkPropsReal>(\n  function LinkComponent(props, forwardedRef) {\n    let children: React.ReactNode\n\n    const {\n      href: hrefProp,\n      as: asProp,\n      children: childrenProp,\n      prefetch: prefetchProp = null,\n      passHref,\n      replace,\n      shallow,\n      scroll,\n      locale,\n      onClick,\n      onNavigate,\n      onMouseEnter: onMouseEnterProp,\n      onTouchStart: onTouchStartProp,\n      legacyBehavior = false,\n      ...restProps\n    } = props\n\n    children = childrenProp\n\n    if (\n      legacyBehavior &&\n      (typeof children === 'string' || typeof children === 'number')\n    ) {\n      children = <a>{children}</a>\n    }\n\n    const router = React.useContext(RouterContext)\n\n    const prefetchEnabled = prefetchProp !== false\n\n    if (process.env.NODE_ENV !== 'production') {\n      function createPropError(args: {\n        key: string\n        expected: string\n        actual: string\n      }) {\n        return new Error(\n          `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n            (typeof window !== 'undefined'\n              ? // TODO: Remove this addendum if Owner Stacks are available\n                \"\\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n\n      // TypeScript trick for type-guarding:\n      const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n        href: true,\n      } as const\n      const requiredProps: LinkPropsRequired[] = Object.keys(\n        requiredPropsGuard\n      ) as LinkPropsRequired[]\n      requiredProps.forEach((key: LinkPropsRequired) => {\n        if (key === 'href') {\n          if (\n            props[key] == null ||\n            (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n          ) {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: props[key] === null ? 'null' : typeof props[key],\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n\n      // TypeScript trick for type-guarding:\n      const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n        as: true,\n        replace: true,\n        scroll: true,\n        shallow: true,\n        passHref: true,\n        prefetch: true,\n        locale: true,\n        onClick: true,\n        onMouseEnter: true,\n        onTouchStart: true,\n        legacyBehavior: true,\n        onNavigate: true,\n      } as const\n      const optionalProps: LinkPropsOptional[] = Object.keys(\n        optionalPropsGuard\n      ) as LinkPropsOptional[]\n      optionalProps.forEach((key: LinkPropsOptional) => {\n        const valType = typeof props[key]\n\n        if (key === 'as') {\n          if (props[key] && valType !== 'string' && valType !== 'object') {\n            throw createPropError({\n              key,\n              expected: '`string` or `object`',\n              actual: valType,\n            })\n          }\n        } else if (key === 'locale') {\n          if (props[key] && valType !== 'string') {\n            throw createPropError({\n              key,\n              expected: '`string`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'onClick' ||\n          key === 'onMouseEnter' ||\n          key === 'onTouchStart' ||\n          key === 'onNavigate'\n        ) {\n          if (props[key] && valType !== 'function') {\n            throw createPropError({\n              key,\n              expected: '`function`',\n              actual: valType,\n            })\n          }\n        } else if (\n          key === 'replace' ||\n          key === 'scroll' ||\n          key === 'shallow' ||\n          key === 'passHref' ||\n          key === 'prefetch' ||\n          key === 'legacyBehavior'\n        ) {\n          if (props[key] != null && valType !== 'boolean') {\n            throw createPropError({\n              key,\n              expected: '`boolean`',\n              actual: valType,\n            })\n          }\n        } else {\n          // TypeScript trick for type-guarding:\n          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n          const _: never = key\n        }\n      })\n    }\n\n    const { href, as } = React.useMemo(() => {\n      if (!router) {\n        const resolvedHref = formatStringOrUrl(hrefProp)\n        return {\n          href: resolvedHref,\n          as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n        }\n      }\n\n      const [resolvedHref, resolvedAs] = resolveHref(router, hrefProp, true)\n\n      return {\n        href: resolvedHref,\n        as: asProp ? resolveHref(router, asProp) : resolvedAs || resolvedHref,\n      }\n    }, [router, hrefProp, asProp])\n\n    const previousHref = React.useRef<string>(href)\n    const previousAs = React.useRef<string>(as)\n\n    // This will return the first child, if multiple are provided it will throw an error\n    let child: any\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        if (onClick) {\n          console.warn(\n            `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n          )\n        }\n        if (onMouseEnterProp) {\n          console.warn(\n            `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n          )\n        }\n        try {\n          child = React.Children.only(children)\n        } catch (err) {\n          if (!children) {\n            throw new Error(\n              `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n            )\n          }\n          throw new Error(\n            `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n              (typeof window !== 'undefined'\n                ? \" \\nOpen your browser's console to view the Component stack trace.\"\n                : '')\n          )\n        }\n      } else {\n        child = React.Children.only(children)\n      }\n    } else {\n      if (process.env.NODE_ENV === 'development') {\n        if ((children as any)?.type === 'a') {\n          throw new Error(\n            'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n          )\n        }\n      }\n    }\n\n    const childRef: any = legacyBehavior\n      ? child && typeof child === 'object' && child.ref\n      : forwardedRef\n\n    const [setIntersectionRef, isVisible, resetVisible] = useIntersection({\n      rootMargin: '200px',\n    })\n\n    const setIntersectionWithResetRef = React.useCallback(\n      (el: Element | null) => {\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n          resetVisible()\n          previousAs.current = as\n          previousHref.current = href\n        }\n\n        setIntersectionRef(el)\n      },\n      [as, href, resetVisible, setIntersectionRef]\n    )\n\n    const setRef = useMergedRef(setIntersectionWithResetRef, childRef)\n\n    // Prefetch the URL if we haven't already and it's visible.\n    React.useEffect(() => {\n      // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n      if (process.env.NODE_ENV !== 'production') {\n        return\n      }\n\n      if (!router) {\n        return\n      }\n\n      // If we don't need to prefetch the URL, don't do prefetch.\n      if (!isVisible || !prefetchEnabled) {\n        return\n      }\n\n      // Prefetch the URL.\n      prefetch(router, href, as, { locale })\n    }, [as, href, isVisible, locale, prefetchEnabled, router?.locale, router])\n\n    const childProps: {\n      onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n      onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n      onClick: React.MouseEventHandler<HTMLAnchorElement>\n      href?: string\n      ref?: any\n    } = {\n      ref: setRef,\n      onClick(e) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (!e) {\n            throw new Error(\n              `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n            )\n          }\n        }\n\n        if (!legacyBehavior && typeof onClick === 'function') {\n          onClick(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onClick === 'function'\n        ) {\n          child.props.onClick(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        if (e.defaultPrevented) {\n          return\n        }\n\n        linkClicked(\n          e,\n          router,\n          href,\n          as,\n          replace,\n          shallow,\n          scroll,\n          locale,\n          onNavigate\n        )\n      },\n      onMouseEnter(e) {\n        if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n          onMouseEnterProp(e)\n        }\n\n        if (\n          legacyBehavior &&\n          child.props &&\n          typeof child.props.onMouseEnter === 'function'\n        ) {\n          child.props.onMouseEnter(e)\n        }\n\n        if (!router) {\n          return\n        }\n\n        prefetch(router, href, as, {\n          locale,\n          priority: true,\n          // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n          bypassPrefetchedCheck: true,\n        })\n      },\n      onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n        ? undefined\n        : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n              onTouchStartProp(e)\n            }\n\n            if (\n              legacyBehavior &&\n              child.props &&\n              typeof child.props.onTouchStart === 'function'\n            ) {\n              child.props.onTouchStart(e)\n            }\n\n            if (!router) {\n              return\n            }\n\n            prefetch(router, href, as, {\n              locale,\n              priority: true,\n              // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n              bypassPrefetchedCheck: true,\n            })\n          },\n    }\n\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if (isAbsoluteUrl(as)) {\n      childProps.href = as\n    } else if (\n      !legacyBehavior ||\n      passHref ||\n      (child.type === 'a' && !('href' in child.props))\n    ) {\n      const curLocale = typeof locale !== 'undefined' ? locale : router?.locale\n\n      // we only render domain locales if we are currently on a domain locale\n      // so that locale links are still visitable in development/preview envs\n      const localeDomain =\n        router?.isLocaleDomain &&\n        getDomainLocale(as, curLocale, router?.locales, router?.domainLocales)\n\n      childProps.href =\n        localeDomain ||\n        addBasePath(addLocale(as, curLocale, router?.defaultLocale))\n    }\n\n    if (legacyBehavior) {\n      if (process.env.NODE_ENV === 'development') {\n        errorOnce(\n          '`legacyBehavior` is deprecated and will be removed in a future ' +\n            'release. A codemod is available to upgrade your components:\\n\\n' +\n            'npx @next/codemod@latest new-link .\\n\\n' +\n            'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n        )\n      }\n      return React.cloneElement(child, childProps)\n    }\n\n    return (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n)\n\nconst LinkStatusContext = createContext<{\n  pending: boolean\n}>({\n  // We do not support link status in the Pages Router, so we always return false\n  pending: false,\n})\n\nexport const useLinkStatus = () => {\n  // This behaviour is like React's useFormStatus. When the component is not under\n  // a <form> tag, it will get the default value, instead of throwing an error.\n  return useContext(LinkStatusContext)\n}\n\nexport default Link\n"], "names": ["useLinkStatus", "prefetched", "Set", "prefetch", "router", "href", "as", "options", "window", "isLocalURL", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "preventDefault", "location", "navigate", "isDefaultPrevented", "routerScroll", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "React", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "useContext", "RouterContext", "prefetchEnabled", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "useMemo", "resolvedHref", "resolvedAs", "resolveHref", "previousHref", "useRef", "previousAs", "child", "console", "warn", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "useIntersection", "rootMargin", "setIntersectionWithResetRef", "useCallback", "el", "current", "setRef", "useMergedRef", "useEffect", "childProps", "defaultPrevented", "priority", "__NEXT_LINK_NO_TOUCH_START", "isAbsoluteUrl", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "getDomainLocale", "locales", "domainLocales", "addBasePath", "addLocale", "defaultLocale", "errorOnce", "cloneElement", "LinkStatusContext", "createContext", "pending"], "mappings": "AAAA;;;;;;;;;;;;;;;;IA8rBA,OAAmB;eAAnB;;IANaA,aAAa;eAAbA;;;;;iEAjrBoC;6BAErB;4BACD;2BACD;uBACI;2BACJ;4CACI;iCAEE;iCACA;6BACJ;8BACC;2BACH;AA0G1B,MAAMC,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAkB,EAClBC,IAAY,EACZC,EAAU,EACVC,OAAwB;IAExB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,IAAI,CAACC,IAAAA,sBAAU,EAACJ,OAAO;QACrB;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQG,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAOJ,QAAQI,MAAM,KAAK,cACtBJ,QAAQI,MAAM,GAEd,YAAYP,SACVA,OAAOO,MAAM,GACbC;QAER,MAAMC,gBAAgBR,OAAO,MAAMC,KAAK,MAAMK;QAE9C,kEAAkE;QAClE,IAAIV,WAAWa,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/BZ,WAAWc,GAAG,CAACF;IACjB;IAEA,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDT,OAAOD,QAAQ,CAACE,MAAMC,IAAIC,SAASS,KAAK,CAAC,CAACC;QACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnB9B,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACV6B,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB1B,MAAuB,EACvB2B,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGL,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMgB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACE,AAACD,oBAAoBnB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACkB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACjC,IAAAA,sBAAU,EAACJ,OAAO;QACrB,IAAI8B,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BD,EAAES,cAAc;YAChBC,SAAST,OAAO,CAAC9B;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEA6B,EAAES,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIP,YAAY;YACd,IAAIQ,qBAAqB;YAEzBR,WAAW;gBACTK,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEA,wEAAwE;QACxE,MAAMC,eAAeV,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBjC,QAAQ;YAC9BA,MAAM,CAAC+B,UAAU,YAAY,OAAO,CAAC9B,MAAMC,IAAI;gBAC7C8B;gBACAzB;gBACA0B,QAAQU;YACV;QACF,OAAO;YACL3C,MAAM,CAAC+B,UAAU,YAAY,OAAO,CAAC7B,MAAMD,MAAM;gBAC/CgC,QAAQU;YACV;QACF;IACF;IAEAF;AACF;AAOA,SAASG,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,IAAAA,oBAAS,EAACD;AACnB;AAEA;;;;;;;CAOC,GACD,MAAME,qBAAOC,cAAK,CAACC,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJpD,MAAMqD,QAAQ,EACdpD,IAAIqD,MAAM,EACVF,UAAUG,YAAY,EACtBzD,UAAU0D,eAAe,IAAI,EAC7BC,QAAQ,EACR3B,OAAO,EACPC,OAAO,EACPC,MAAM,EACN1B,MAAM,EACNoD,OAAO,EACPzB,UAAU,EACV0B,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,qBAACa;sBAAGb;;IACjB;IAEA,MAAMrD,SAASgD,cAAK,CAACmB,UAAU,CAACC,yCAAa;IAE7C,MAAMC,kBAAkBZ,iBAAiB;IAEzC,IAAI3C,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAASsD,gBAAgBC,IAIxB;YACC,OAAO,qBAMN,CANM,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOvE,WAAW,cAEf,qEACA,EAAC,IALF,qBAAA;uBAAA;4BAAA;8BAAA;YAMP;QACF;QAEA,sCAAsC;QACtC,MAAMwE,qBAAsD;YAC1D3E,MAAM;QACR;QACA,MAAM4E,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEtB,KAAK,CAACsB,IAAI,IAAI,QACb,OAAOtB,KAAK,CAACsB,IAAI,KAAK,YAAY,OAAOtB,KAAK,CAACsB,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQxB,KAAK,CAACsB,IAAI,KAAK,OAAO,SAAS,OAAOtB,KAAK,CAACsB,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DhF,IAAI;YACJ6B,SAAS;YACTE,QAAQ;YACRD,SAAS;YACT0B,UAAU;YACV3D,UAAU;YACVQ,QAAQ;YACRoD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChB9B,YAAY;QACd;QACA,MAAMiD,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOjC,KAAK,CAACsB,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAItB,KAAK,CAACsB,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAItB,KAAK,CAACsB,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAItB,KAAK,CAACsB,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAItB,KAAK,CAACsB,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;IACF;IAEA,MAAM,EAAExE,IAAI,EAAEC,EAAE,EAAE,GAAG8C,cAAK,CAACqC,OAAO,CAAC;QACjC,IAAI,CAACrF,QAAQ;YACX,MAAMsF,eAAe1C,kBAAkBU;YACvC,OAAO;gBACLrD,MAAMqF;gBACNpF,IAAIqD,SAASX,kBAAkBW,UAAU+B;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGC,IAAAA,wBAAW,EAACxF,QAAQsD,UAAU;QAEjE,OAAO;YACLrD,MAAMqF;YACNpF,IAAIqD,SAASiC,IAAAA,wBAAW,EAACxF,QAAQuD,UAAUgC,cAAcD;QAC3D;IACF,GAAG;QAACtF;QAAQsD;QAAUC;KAAO;IAE7B,MAAMkC,eAAezC,cAAK,CAAC0C,MAAM,CAASzF;IAC1C,MAAM0F,aAAa3C,cAAK,CAAC0C,MAAM,CAASxF;IAExC,oFAAoF;IACpF,IAAI0F;IACJ,IAAI5B,gBAAgB;QAClB,IAAIlD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI2C,SAAS;gBACXkC,QAAQC,IAAI,CACV,AAAC,oDAAoDxC,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpBgC,QAAQC,IAAI,CACV,AAAC,yDAAyDxC,WAAS;YAEvE;YACA,IAAI;gBACFsC,QAAQ5C,cAAK,CAAC+C,QAAQ,CAACC,IAAI,CAAC3C;YAC9B,EAAE,OAAOxC,KAAK;gBACZ,IAAI,CAACwC,UAAU;oBACb,MAAM,qBAEL,CAFK,IAAImB,MACR,AAAC,uDAAuDlB,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,qBAKL,CALK,IAAIkB,MACR,AAAC,6DAA6DlB,WAAS,8FACpE,CAAA,OAAOlD,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;YACLwF,QAAQ5C,cAAK,CAAC+C,QAAQ,CAACC,IAAI,CAAC3C;QAC9B;IACF,OAAO;QACL,IAAIvC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACqC,4BAAD,AAACA,SAAkB4C,IAAI,MAAK,KAAK;gBACnC,MAAM,qBAEL,CAFK,IAAIzB,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAM0B,WAAgBlC,iBAClB4B,SAAS,OAAOA,UAAU,YAAYA,MAAMO,GAAG,GAC/C/C;IAEJ,MAAM,CAACgD,oBAAoBC,WAAWC,aAAa,GAAGC,IAAAA,gCAAe,EAAC;QACpEC,YAAY;IACd;IAEA,MAAMC,8BAA8BzD,cAAK,CAAC0D,WAAW,CACnD,CAACC;QACC,4EAA4E;QAC5E,IAAIhB,WAAWiB,OAAO,KAAK1G,MAAMuF,aAAamB,OAAO,KAAK3G,MAAM;YAC9DqG;YACAX,WAAWiB,OAAO,GAAG1G;YACrBuF,aAAamB,OAAO,GAAG3G;QACzB;QAEAmG,mBAAmBO;IACrB,GACA;QAACzG;QAAID;QAAMqG;QAAcF;KAAmB;IAG9C,MAAMS,SAASC,IAAAA,0BAAY,EAACL,6BAA6BP;IAEzD,2DAA2D;IAC3DlD,cAAK,CAAC+D,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAIjG,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAAChB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACqG,aAAa,CAAChC,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpBtE,SAASC,QAAQC,MAAMC,IAAI;YAAEK;QAAO;IACtC,GAAG;QAACL;QAAID;QAAMoG;QAAW9F;QAAQ8D;QAAiBrE,0BAAAA,OAAQO,MAAM;QAAEP;KAAO;IAEzE,MAAMgH,aAMF;QACFb,KAAKU;QACLlD,SAAQ7B,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,qBAEL,CAFK,IAAI0C,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACR,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ7B;YACV;YAEA,IACEkC,kBACA4B,MAAMzC,KAAK,IACX,OAAOyC,MAAMzC,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACAiC,MAAMzC,KAAK,CAACQ,OAAO,CAAC7B;YACtB;YAEA,IAAI,CAAC9B,QAAQ;gBACX;YACF;YAEA,IAAI8B,EAAEmF,gBAAgB,EAAE;gBACtB;YACF;YAEApF,YACEC,GACA9B,QACAC,MACAC,IACA6B,SACAC,SACAC,QACA1B,QACA2B;QAEJ;QACA0B,cAAa9B,CAAC;YACZ,IAAI,CAACkC,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB/B;YACnB;YAEA,IACEkC,kBACA4B,MAAMzC,KAAK,IACX,OAAOyC,MAAMzC,KAAK,CAACS,YAAY,KAAK,YACpC;gBACAgC,MAAMzC,KAAK,CAACS,YAAY,CAAC9B;YAC3B;YAEA,IAAI,CAAC9B,QAAQ;gBACX;YACF;YAEAD,SAASC,QAAQC,MAAMC,IAAI;gBACzBK;gBACA2G,UAAU;gBACV,gGAAgG;gBAChG5G,uBAAuB;YACzB;QACF;QACAwD,cAAchD,QAAQC,GAAG,CAACoG,0BAA0B,GAChD3G,YACA,SAASsD,aAAahC,CAAC;YACrB,IAAI,CAACkC,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBjC;YACnB;YAEA,IACEkC,kBACA4B,MAAMzC,KAAK,IACX,OAAOyC,MAAMzC,KAAK,CAACW,YAAY,KAAK,YACpC;gBACA8B,MAAMzC,KAAK,CAACW,YAAY,CAAChC;YAC3B;YAEA,IAAI,CAAC9B,QAAQ;gBACX;YACF;YAEAD,SAASC,QAAQC,MAAMC,IAAI;gBACzBK;gBACA2G,UAAU;gBACV,gGAAgG;gBAChG5G,uBAAuB;YACzB;QACF;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAI8G,IAAAA,oBAAa,EAAClH,KAAK;QACrB8G,WAAW/G,IAAI,GAAGC;IACpB,OAAO,IACL,CAAC8D,kBACDN,YACCkC,MAAMK,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUL,MAAMzC,KAAK,AAAD,GAC7C;QACA,MAAMkE,YAAY,OAAO9G,WAAW,cAAcA,SAASP,0BAAAA,OAAQO,MAAM;QAEzE,uEAAuE;QACvE,uEAAuE;QACvE,MAAM+G,eACJtH,CAAAA,0BAAAA,OAAQuH,cAAc,KACtBC,IAAAA,gCAAe,EAACtH,IAAImH,WAAWrH,0BAAAA,OAAQyH,OAAO,EAAEzH,0BAAAA,OAAQ0H,aAAa;QAEvEV,WAAW/G,IAAI,GACbqH,gBACAK,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAAC1H,IAAImH,WAAWrH,0BAAAA,OAAQ6H,aAAa;IAC9D;IAEA,IAAI7D,gBAAgB;QAClB,IAAIlD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C8G,IAAAA,oBAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACA,qBAAO9E,cAAK,CAAC+E,YAAY,CAACnC,OAAOoB;IACnC;IAEA,qBACE,qBAAC9C;QAAG,GAAGD,SAAS;QAAG,GAAG+C,UAAU;kBAC7B3D;;AAGP;AAGF,MAAM2E,kCAAoBC,IAAAA,oBAAa,EAEpC;IACD,+EAA+E;IAC/EC,SAAS;AACX;AAEO,MAAMtI,gBAAgB;IAC3B,gFAAgF;IAChF,6EAA6E;IAC7E,OAAOuE,IAAAA,iBAAU,EAAC6D;AACpB;MAEA,WAAejF"}