{"version": 3, "sources": ["../../src/client/form-shared.tsx"], "sourcesContent": ["import type { HTMLProps } from 'react'\n\nexport const DISALLOWED_FORM_PROPS = ['method', 'encType', 'target'] as const\n\ntype HTMLFormProps = HTMLProps<HTMLFormElement>\ntype DisallowedFormProps = (typeof DISALLOWED_FORM_PROPS)[number]\n\ntype InternalFormProps = {\n  /**\n   * `action` can be either a `string` or a function.\n   * - If `action` is a string, it will be interpreted as a path or URL to navigate to when the form is submitted.\n   *   The path will be prefetched when the form becomes visible.\n   * - If `action` is a function, it will be called when the form is submitted. See the [React docs](https://react.dev/reference/react-dom/components/form#props) for more.\n   */\n  action: NonNullable<HTMLFormProps['action']>\n  /**\n   * Controls how the route specified by `action` is prefetched.\n   * Any `<Form />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`. Prefetching is only enabled in production.\n   *\n   * Options:\n   * - `null` (default): For statically generated pages, this will prefetch the full React Server Component data. For dynamic pages, this will prefetch up to the nearest route segment with a [`loading.js`](https://nextjs.org/docs/app/api-reference/file-conventions/loading) file. If there is no loading file, it will not fetch the full tree to avoid fetching too much data.\n   * - `false`: This will not prefetch any data.\n   *\n   * In pages dir, prefetching is not supported, and passing this prop will emit a warning.\n   *\n   * @defaultValue `null`\n   */\n  prefetch?: false | null\n  /**\n   * Whether submitting the form should replace the current `history` state instead of adding a new url into the stack.\n   * Only valid if `action` is a string.\n   *\n   * @defaultValue `false`\n   */\n  replace?: boolean\n  /**\n   * Override the default scroll behavior when navigating.\n   * Only valid if `action` is a string.\n   *\n   * @defaultValue `true`\n   */\n  scroll?: boolean\n} & Omit<HTMLFormProps, 'action' | DisallowedFormProps>\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type FormProps<RouteInferType = any> = InternalFormProps\n\nexport function createFormSubmitDestinationUrl(\n  action: string,\n  formElement: HTMLFormElement\n) {\n  let targetUrl: URL\n  try {\n    // NOTE: It might be more correct to resolve URLs relative to `document.baseURI`,\n    // but we already do it relative to `location.href` elsewhere:\n    //  (see e.g. https://github.com/vercel/next.js/blob/bb0e6722f87ceb2d43015f5b8a413d0072f2badf/packages/next/src/client/components/app-router.tsx#L146)\n    // so it's better to stay consistent.\n    const base = window.location.href\n    targetUrl = new URL(action, base)\n  } catch (err) {\n    throw new Error(`Cannot parse form action \"${action}\" as a URL`, {\n      cause: err,\n    })\n  }\n  if (targetUrl.searchParams.size) {\n    // url-encoded HTML forms *overwrite* any search params in the `action` url:\n    //\n    //  \"Let `query` be the result of running the application/x-www-form-urlencoded serializer [...]\"\n    //  \"Set parsed action's query component to `query`.\"\n    //   https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#submit-mutate-action\n    //\n    // We need to match that.\n    // (note that all other parts of the URL, like `hash`, are preserved)\n    targetUrl.search = ''\n  }\n\n  const formData = new FormData(formElement)\n\n  for (let [name, value] of formData) {\n    if (typeof value !== 'string') {\n      // For file inputs, the native browser behavior is to use the filename as the value instead:\n      //\n      //   \"If entry's value is a File object, then let value be entry's value's name. Otherwise, let value be entry's value.\"\n      //   https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n      //\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(\n          `<Form> only supports file inputs if \\`action\\` is a function. File inputs cannot be used if \\`action\\` is a string, ` +\n            `because files cannot be encoded as search params.`\n        )\n      }\n      value = value.name\n    }\n\n    targetUrl.searchParams.append(name, value)\n  }\n  return targetUrl\n}\n\nexport function checkFormActionUrl(\n  action: string,\n  source: 'action' | 'formAction'\n) {\n  const aPropName = source === 'action' ? `an \\`action\\`` : `a \\`formAction\\``\n\n  let testUrl: URL\n  try {\n    testUrl = new URL(action, 'http://n')\n  } catch (err) {\n    console.error(\n      `<Form> received ${aPropName} that cannot be parsed as a URL: \"${action}\".`\n    )\n    return\n  }\n\n  // url-encoded HTML forms ignore any queryparams in the `action` url. We need to match that.\n  if (testUrl.searchParams.size) {\n    console.warn(\n      `<Form> received ${aPropName} that contains search params: \"${action}\". This is not supported, and they will be ignored. ` +\n        `If you need to pass in additional search params, use an \\`<input type=\"hidden\" />\\` instead.`\n    )\n  }\n}\n\nexport const isSupportedFormEncType = (value: string) =>\n  value === 'application/x-www-form-urlencoded'\nexport const isSupportedFormMethod = (value: string) => value === 'get'\nexport const isSupportedFormTarget = (value: string) => value === '_self'\n\nexport function hasUnsupportedSubmitterAttributes(\n  submitter: HTMLElement\n): boolean {\n  // A submitter can override `encType` for the form.\n  const formEncType = submitter.getAttribute('formEncType')\n  if (formEncType !== null && !isSupportedFormEncType(formEncType)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.error(\n        `<Form>'s \\`encType\\` was set to an unsupported value via \\`formEncType=\"${formEncType}\"\\`. ` +\n          `This will disable <Form>'s navigation functionality. If you need this, use a native <form> element instead.`\n      )\n    }\n    return true\n  }\n\n  // A submitter can override `method` for the form.\n  const formMethod = submitter.getAttribute('formMethod')\n  if (formMethod !== null && !isSupportedFormMethod(formMethod)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.error(\n        `<Form>'s \\`method\\` was set to an unsupported value via \\`formMethod=\"${formMethod}\"\\`. ` +\n          `This will disable <Form>'s navigation functionality. If you need this, use a native <form> element instead.`\n      )\n    }\n    return true\n  }\n\n  // A submitter can override `target` for the form.\n  const formTarget = submitter.getAttribute('formTarget')\n  if (formTarget !== null && !isSupportedFormTarget(formTarget)) {\n    if (process.env.NODE_ENV === 'development') {\n      console.error(\n        `<Form>'s \\`target\\` was set to an unsupported value via \\`formTarget=\"${formTarget}\"\\`. ` +\n          `This will disable <Form>'s navigation functionality. If you need this, use a native <form> element instead.`\n      )\n    }\n    return true\n  }\n\n  return false\n}\n\nexport function hasReactClientActionAttributes(submitter: HTMLElement) {\n  // CSR: https://github.com/facebook/react/blob/942eb80381b96f8410eab1bef1c539bed1ab0eb1/packages/react-dom-bindings/src/client/ReactDOMComponent.js#L482-L487\n  // SSR: https://github.com/facebook/react/blob/942eb80381b96f8410eab1bef1c539bed1ab0eb1/packages/react-dom-bindings/src/client/ReactDOMComponent.js#L2401\n  const action = submitter.getAttribute('formAction')\n  return action && /\\s*javascript:/i.test(action)\n}\n"], "names": ["DISALLOWED_FORM_PROPS", "checkFormActionUrl", "createFormSubmitDestinationUrl", "hasReactClientActionAttributes", "hasUnsupportedSubmitterAttributes", "isSupportedFormEncType", "isSupportedFormMethod", "isSupportedFormTarget", "action", "formElement", "targetUrl", "base", "window", "location", "href", "URL", "err", "Error", "cause", "searchParams", "size", "search", "formData", "FormData", "name", "value", "process", "env", "NODE_ENV", "console", "warn", "append", "source", "aPropName", "testUrl", "error", "submitter", "formEncType", "getAttribute", "formMethod", "formTarget", "test"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAEaA,qBAAqB;eAArBA;;IAoGGC,kBAAkB;eAAlBA;;IApDAC,8BAA8B;eAA9BA;;IA4HAC,8BAA8B;eAA9BA;;IA1CAC,iCAAiC;eAAjCA;;IALHC,sBAAsB;eAAtBA;;IAEAC,qBAAqB;eAArBA;;IACAC,qBAAqB;eAArBA;;;AAhIN,MAAMP,wBAAwB;IAAC;IAAU;IAAW;CAAS;AAgD7D,SAASE,+BACdM,MAAc,EACdC,WAA4B;IAE5B,IAAIC;IACJ,IAAI;QACF,iFAAiF;QACjF,8DAA8D;QAC9D,sJAAsJ;QACtJ,qCAAqC;QACrC,MAAMC,OAAOC,OAAOC,QAAQ,CAACC,IAAI;QACjCJ,YAAY,IAAIK,IAAIP,QAAQG;IAC9B,EAAE,OAAOK,KAAK;QACZ,MAAM,qBAEJ,CAFI,IAAIC,MAAM,AAAC,+BAA4BT,SAAO,cAAa;YAC/DU,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IACA,IAAIN,UAAUS,YAAY,CAACC,IAAI,EAAE;QAC/B,4EAA4E;QAC5E,EAAE;QACF,iGAAiG;QACjG,qDAAqD;QACrD,iGAAiG;QACjG,EAAE;QACF,yBAAyB;QACzB,qEAAqE;QACrEV,UAAUW,MAAM,GAAG;IACrB;IAEA,MAAMC,WAAW,IAAIC,SAASd;IAE9B,KAAK,IAAI,CAACe,MAAMC,MAAM,IAAIH,SAAU;QAClC,IAAI,OAAOG,UAAU,UAAU;YAC7B,4FAA4F;YAC5F,EAAE;YACF,wHAAwH;YACxH,mIAAmI;YACnI,EAAE;YACF,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;gBAC1CC,QAAQC,IAAI,CACV,AAAC,qHACE;YAEP;YACAL,QAAQA,MAAMD,IAAI;QACpB;QAEAd,UAAUS,YAAY,CAACY,MAAM,CAACP,MAAMC;IACtC;IACA,OAAOf;AACT;AAEO,SAAST,mBACdO,MAAc,EACdwB,MAA+B;IAE/B,MAAMC,YAAYD,WAAW,WAAY,gBAAkB;IAE3D,IAAIE;IACJ,IAAI;QACFA,UAAU,IAAInB,IAAIP,QAAQ;IAC5B,EAAE,OAAOQ,KAAK;QACZa,QAAQM,KAAK,CACX,AAAC,qBAAkBF,YAAU,uCAAoCzB,SAAO;QAE1E;IACF;IAEA,4FAA4F;IAC5F,IAAI0B,QAAQf,YAAY,CAACC,IAAI,EAAE;QAC7BS,QAAQC,IAAI,CACV,AAAC,qBAAkBG,YAAU,oCAAiCzB,SAAO,yDAClE;IAEP;AACF;AAEO,MAAMH,yBAAyB,CAACoB,QACrCA,UAAU;AACL,MAAMnB,wBAAwB,CAACmB,QAAkBA,UAAU;AAC3D,MAAMlB,wBAAwB,CAACkB,QAAkBA,UAAU;AAE3D,SAASrB,kCACdgC,SAAsB;IAEtB,mDAAmD;IACnD,MAAMC,cAAcD,UAAUE,YAAY,CAAC;IAC3C,IAAID,gBAAgB,QAAQ,CAAChC,uBAAuBgC,cAAc;QAChE,IAAIX,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CC,QAAQM,KAAK,CACX,AAAC,2EAA0EE,cAAY,SACpF;QAEP;QACA,OAAO;IACT;IAEA,kDAAkD;IAClD,MAAME,aAAaH,UAAUE,YAAY,CAAC;IAC1C,IAAIC,eAAe,QAAQ,CAACjC,sBAAsBiC,aAAa;QAC7D,IAAIb,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CC,QAAQM,KAAK,CACX,AAAC,yEAAwEI,aAAW,SACjF;QAEP;QACA,OAAO;IACT;IAEA,kDAAkD;IAClD,MAAMC,aAAaJ,UAAUE,YAAY,CAAC;IAC1C,IAAIE,eAAe,QAAQ,CAACjC,sBAAsBiC,aAAa;QAC7D,IAAId,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CC,QAAQM,KAAK,CACX,AAAC,yEAAwEK,aAAW,SACjF;QAEP;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASrC,+BAA+BiC,SAAsB;IACnE,6JAA6J;IAC7J,yJAAyJ;IACzJ,MAAM5B,SAAS4B,UAAUE,YAAY,CAAC;IACtC,OAAO9B,UAAU,kBAAkBiC,IAAI,CAACjC;AAC1C"}