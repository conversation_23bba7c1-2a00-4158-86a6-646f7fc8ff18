'use client';

import { useState } from 'react';
import { wctAirdropAPI, WCTAirdropResult } from '@/lib/solana-api';

export default function BulkAddressChecker() {
  const [addresses, setAddresses] = useState('');
  const [results, setResults] = useState<WCTAirdropResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [progress, setProgress] = useState({ current: 0, total: 0 });

  const parseAddresses = (input: string): string[] => {
    return input
      .split(/[\n,;]/)
      .map(addr => addr.trim())
      .filter(addr => addr.length > 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!addresses.trim()) {
      alert('Please enter wallet addresses');
      return;
    }

    const addressList = parseAddresses(addresses);
    
    if (addressList.length === 0) {
      alert('No valid addresses found');
      return;
    }

    if (addressList.length > 100) {
      alert('Maximum 100 addresses allowed at once');
      return;
    }

    // Validate addresses
    const invalidAddresses = addressList.filter(addr => !wctAirdropAPI.isValidSolanaAddress(addr));
    if (invalidAddresses.length > 0) {
      alert(`Invalid addresses found: ${invalidAddresses.slice(0, 3).join(', ')}${invalidAddresses.length > 3 ? '...' : ''}`);
      return;
    }

    setLoading(true);
    setResults([]);
    setProgress({ current: 0, total: addressList.length });

    try {
      // Process addresses in batches to avoid overwhelming the API
      const batchSize = 10;
      const allResults: WCTAirdropResult[] = [];

      for (let i = 0; i < addressList.length; i += batchSize) {
        const batch = addressList.slice(i, i + batchSize);
        const batchResults = await wctAirdropAPI.checkMultipleWCTEligibility(batch);
        allResults.push(...batchResults);
        
        setProgress({ current: allResults.length, total: addressList.length });
        setResults([...allResults]); // Update results progressively
        
        // Small delay between batches to be respectful to the API
        if (i + batchSize < addressList.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      setResults(allResults);
    } catch (error) {
      console.error('Error checking WCT eligibility:', error);
      alert('Error occurred while checking WCT eligibility');
    } finally {
      setLoading(false);
      setProgress({ current: 0, total: 0 });
    }
  };

  const handleClear = () => {
    setAddresses('');
    setResults([]);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const content = event.target?.result as string;
      setAddresses(content);
    };
    reader.readAsText(file);
  };

  const exportResults = () => {
    if (results.length === 0) return;

    const csvContent = [
      'Address,WCT Airdrop Status,Eligible Amount (WCT),Error',
      ...results.map(result =>
        `"${result.address}",${result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'},${result.eligibleAmount.toLocaleString()},"${result.error || ''}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `wct-airdrop-results-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const eligibleCount = results.filter(r => r.isEligible).length;
  const errorCount = results.filter(r => r.error).length;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">WCT Airdrop Checker - Bulk</h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="addresses" className="block text-sm font-medium text-gray-700 mb-2">
            Wallet Addresses (one per line, or comma/semicolon separated)
          </label>
          <textarea
            id="addresses"
            value={addresses}
            onChange={(e) => setAddresses(e.target.value)}
            placeholder="Enter multiple Solana wallet addresses..."
            rows={8}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={loading}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Or upload a text file
          </label>
          <input
            type="file"
            accept=".txt,.csv"
            onChange={handleFileUpload}
            disabled={loading}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
        </div>
        
        <div className="flex gap-3">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? `Checking... (${progress.current}/${progress.total})` : 'Check WCT Eligibility'}
          </button>
          
          <button
            type="button"
            onClick={handleClear}
            disabled={loading}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Clear
          </button>
        </div>
      </form>

      {loading && (
        <div className="mt-4">
          <div className="bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress.total > 0 ? (progress.current / progress.total) * 100 : 0}%` }}
            />
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Processing {progress.current} of {progress.total} addresses...
          </p>
        </div>
      )}

      {results.length > 0 && (
        <div className="mt-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-lg">Results ({results.length} addresses)</h3>
            <button
              onClick={exportResults}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
            >
              Export CSV
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="bg-blue-50 p-3 rounded-md">
              <div className="text-2xl font-bold text-blue-600">{results.length}</div>
              <div className="text-sm text-blue-600">Total Checked</div>
            </div>
            <div className="bg-green-50 p-3 rounded-md">
              <div className="text-2xl font-bold text-green-600">{eligibleCount}</div>
              <div className="text-sm text-green-600">Eligible</div>
            </div>
            <div className="bg-red-50 p-3 rounded-md">
              <div className="text-2xl font-bold text-red-600">{errorCount}</div>
              <div className="text-sm text-red-600">Errors</div>
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto border rounded-md">
            <table className="w-full text-sm">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th className="px-4 py-2 text-left">Address</th>
                  <th className="px-4 py-2 text-left">WCT Status</th>
                  <th className="px-4 py-2 text-left">Eligible Amount</th>
                  <th className="px-4 py-2 text-left">Status</th>
                </tr>
              </thead>
              <tbody>
                {results.map((result, index) => (
                  <tr key={index} className="border-t">
                    <td className="px-4 py-2 font-mono text-xs break-all">{result.address}</td>
                    <td className="px-4 py-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        result.isEligible
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'}
                      </span>
                    </td>
                    <td className="px-4 py-2">
                      {result.isEligible ? (
                        <span className="font-semibold text-green-600">
                          {result.eligibleAmount.toLocaleString()} WCT
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    <td className="px-4 py-2">
                      {result.error ? (
                        <span className="text-red-600 text-xs">{result.error}</span>
                      ) : (
                        <span className="text-green-600 text-xs">✓ Success</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
