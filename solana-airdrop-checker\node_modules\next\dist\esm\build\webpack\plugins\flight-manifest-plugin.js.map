{"version": 3, "sources": ["../../../../src/build/webpack/plugins/flight-manifest-plugin.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nimport path from 'path'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  APP_CLIENT_INTERNALS,\n  BARREL_OPTIMIZATION_PREFIX,\n  CLIENT_REFERENCE_MANIFEST,\n  SYSTEM_ENTRYPOINTS,\n} from '../../../shared/lib/constants'\nimport { relative } from 'path'\nimport { getProxiedPluginState } from '../../build-context'\n\nimport { WEBPACK_LAYERS } from '../../../lib/constants'\nimport { normalizePagePath } from '../../../shared/lib/page-path/normalize-page-path'\nimport { CLIENT_STATIC_FILES_RUNTIME_MAIN_APP } from '../../../shared/lib/constants'\nimport { getDeploymentIdQueryOrEmptyString } from '../../deployment-id'\nimport {\n  formatBarrelOptimizedResource,\n  getModuleReferencesInOrder,\n} from '../utils'\nimport type { ChunkGroup } from 'webpack'\nimport { encodeURIPath } from '../../../shared/lib/encode-uri-path'\nimport type { ModuleInfo } from './flight-client-entry-plugin'\n\ninterface Options {\n  dev: boolean\n  appDir: string\n  experimentalInlineCss: boolean\n}\n\n/**\n * Webpack module id\n */\n// TODO-APP ensure `null` is included as it is used.\ntype ModuleId = string | number /*| null*/\n\n// double indexed chunkId, filename\nexport type ManifestChunks = Array<string>\n\nconst pluginState = getProxiedPluginState({\n  ssrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n  edgeSsrModules: {} as { [ssrModuleId: string]: ModuleInfo },\n\n  rscModules: {} as { [rscModuleId: string]: ModuleInfo },\n  edgeRscModules: {} as { [rscModuleId: string]: ModuleInfo },\n})\n\nexport interface ManifestNode {\n  [moduleExport: string]: {\n    /**\n     * Webpack module id\n     */\n    id: ModuleId\n    /**\n     * Export name\n     */\n    name: string\n    /**\n     * Chunks for the module. JS and CSS.\n     */\n    chunks: ManifestChunks\n\n    /**\n     * If chunk contains async module\n     */\n    async?: boolean\n  }\n}\n\nexport interface ClientReferenceManifestForRsc {\n  clientModules: ManifestNode\n  rscModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n  edgeRscModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n}\n\nexport type CssResource = InlinedCssFile | UninlinedCssFile\n\ninterface InlinedCssFile {\n  path: string\n  inlined: true\n  content: string\n}\n\ninterface UninlinedCssFile {\n  path: string\n  inlined: false\n}\n\nexport interface ClientReferenceManifest extends ClientReferenceManifestForRsc {\n  readonly moduleLoading: {\n    prefix: string\n    crossOrigin?: 'use-credentials' | ''\n  }\n  ssrModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n  edgeSSRModuleMapping: {\n    [moduleId: string]: ManifestNode\n  }\n  entryCSSFiles: {\n    [entry: string]: CssResource[]\n  }\n  entryJSFiles?: {\n    [entry: string]: string[]\n  }\n}\n\nfunction getAppPathRequiredChunks(\n  chunkGroup: webpack.ChunkGroup,\n  excludedFiles: Set<string>\n) {\n  const deploymentIdChunkQuery = getDeploymentIdQueryOrEmptyString()\n\n  const chunks: Array<string> = []\n  chunkGroup.chunks.forEach((chunk) => {\n    if (SYSTEM_ENTRYPOINTS.has(chunk.name || '')) {\n      return null\n    }\n\n    // Get the actual chunk file names from the chunk file list.\n    // It's possible that the chunk is generated via `import()`, in\n    // that case the chunk file name will be '[name].[contenthash]'\n    // instead of '[name]-[chunkhash]'.\n    if (chunk.id != null) {\n      const chunkId = '' + chunk.id\n      chunk.files.forEach((file) => {\n        // It's possible that a chunk also emits CSS files, that will\n        // be handled separatedly.\n        if (!file.endsWith('.js')) return null\n        if (file.endsWith('.hot-update.js')) return null\n        if (excludedFiles.has(file)) return null\n\n        // We encode the file as a URI because our server (and many other services such as S3)\n        // expect to receive reserved characters such as `[` and `]` as encoded. This was\n        // previously done for dynamic chunks by patching the webpack runtime but we want\n        // these filenames to be managed by React's Flight runtime instead and so we need\n        // to implement any special handling of the file name here.\n        return chunks.push(\n          chunkId,\n          encodeURIPath(file) + deploymentIdChunkQuery\n        )\n      })\n    }\n  })\n  return chunks\n}\n\n// Normalize the entry names to their \"group names\" so a page can easily track\n// all the manifest items it needs from parent groups by looking up the group\n// segments:\n// - app/foo/loading -> app/foo\n// - app/foo/page -> app/foo\n// - app/(group)/@named/foo/page -> app/foo\n// - app/(.)foo/(..)bar/loading -> app/bar\n// - app/[...catchAll]/page -> app\n// - app/foo/@slot/[...catchAll]/page -> app/foo\nfunction entryNameToGroupName(entryName: string) {\n  let groupName = entryName\n    .slice(0, entryName.lastIndexOf('/'))\n    // Remove slots\n    .replace(/\\/@[^/]+/g, '')\n    // Remove the group with lookahead to make sure it's not interception route\n    .replace(/\\/\\([^/]+\\)(?=(\\/|$))/g, '')\n    // Remove catch-all routes since they should be part of the parent group that the catch-all would apply to.\n    // This is necessary to support parallel routes since multiple page components can be rendered on the same page.\n    // In order to do that, we need to ensure that the manifests are merged together by putting them in the same group.\n    .replace(/\\/\\[?\\[\\.\\.\\.[^\\]]*]]?/g, '')\n\n  // Interception routes\n  groupName = groupName\n    .replace(/^.+\\/\\(\\.\\.\\.\\)/g, 'app/')\n    .replace(/\\/\\(\\.\\)/g, '/')\n\n  // Interception routes (recursive)\n  while (/\\/[^/]+\\/\\(\\.\\.\\)/.test(groupName)) {\n    groupName = groupName.replace(/\\/[^/]+\\/\\(\\.\\.\\)/g, '/')\n  }\n\n  return groupName\n}\n\nfunction mergeManifest(\n  manifest: ClientReferenceManifest,\n  manifestToMerge: ClientReferenceManifest\n) {\n  Object.assign(manifest.clientModules, manifestToMerge.clientModules)\n  Object.assign(manifest.ssrModuleMapping, manifestToMerge.ssrModuleMapping)\n  Object.assign(\n    manifest.edgeSSRModuleMapping,\n    manifestToMerge.edgeSSRModuleMapping\n  )\n  Object.assign(manifest.entryCSSFiles, manifestToMerge.entryCSSFiles)\n  Object.assign(manifest.rscModuleMapping, manifestToMerge.rscModuleMapping)\n  Object.assign(\n    manifest.edgeRscModuleMapping,\n    manifestToMerge.edgeRscModuleMapping\n  )\n}\n\nconst PLUGIN_NAME = 'ClientReferenceManifestPlugin'\n\nexport class ClientReferenceManifestPlugin {\n  dev: Options['dev'] = false\n  appDir: Options['appDir']\n  appDirBase: string\n  experimentalInlineCss: Options['experimentalInlineCss']\n\n  constructor(options: Options) {\n    this.dev = options.dev\n    this.appDir = options.appDir\n    this.appDirBase = path.dirname(this.appDir) + path.sep\n    this.experimentalInlineCss = options.experimentalInlineCss\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.processAssets.tap(\n        {\n          name: PLUGIN_NAME,\n          // Have to be in the optimize stage to run after updating the CSS\n          // asset hash via extract mini css plugin.\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_OPTIMIZE_HASH,\n        },\n        () => this.createAsset(compilation, compiler.context)\n      )\n    })\n  }\n\n  createAsset(compilation: webpack.Compilation, context: string) {\n    const manifestsPerGroup = new Map<string, ClientReferenceManifest[]>()\n    const manifestEntryFiles: string[] = []\n\n    const configuredCrossOriginLoading =\n      compilation.outputOptions.crossOriginLoading\n    const crossOriginMode =\n      typeof configuredCrossOriginLoading === 'string'\n        ? configuredCrossOriginLoading === 'use-credentials'\n          ? configuredCrossOriginLoading\n          : '' // === 'anonymous'\n        : undefined\n\n    if (typeof compilation.outputOptions.publicPath !== 'string') {\n      throw new Error(\n        'Expected webpack publicPath to be a string when using App Router. To customize where static assets are loaded from, use the `assetPrefix` option in next.config.js. If you are customizing your webpack config please make sure you are not modifying or removing the publicPath configuration option'\n      )\n    }\n    const prefix = compilation.outputOptions.publicPath || ''\n\n    // We want to omit any files that will always be loaded on any App Router page\n    // because they will already be loaded by the main entrypoint.\n    const rootMainFiles: Set<string> = new Set()\n    compilation.entrypoints\n      .get(CLIENT_STATIC_FILES_RUNTIME_MAIN_APP)\n      ?.getFiles()\n      .forEach((file) => {\n        if (/(?<!\\.hot-update)\\.(js|css)($|\\?)/.test(file)) {\n          rootMainFiles.add(file.replace(/\\\\/g, '/'))\n        }\n      })\n\n    for (let [entryName, entrypoint] of compilation.entrypoints) {\n      if (\n        entryName === CLIENT_STATIC_FILES_RUNTIME_MAIN_APP ||\n        entryName === APP_CLIENT_INTERNALS\n      ) {\n        entryName = ''\n      } else if (!/^app[\\\\/]/.test(entryName)) {\n        continue\n      }\n\n      const manifest: ClientReferenceManifest = {\n        moduleLoading: {\n          prefix,\n          crossOrigin: crossOriginMode,\n        },\n        ssrModuleMapping: {},\n        edgeSSRModuleMapping: {},\n        clientModules: {},\n        entryCSSFiles: {},\n        rscModuleMapping: {},\n        edgeRscModuleMapping: {},\n      }\n\n      // Absolute path without the extension\n      const chunkEntryName = (this.appDirBase + entryName).replace(\n        /[\\\\/]/g,\n        path.sep\n      )\n\n      manifest.entryCSSFiles[chunkEntryName] = entrypoint\n        .getFiles()\n        .filter((f) => !f.startsWith('static/css/pages/') && f.endsWith('.css'))\n        .map((file) => {\n          const source = compilation.getAsset(file)!.source.source()\n          if (\n            this.experimentalInlineCss &&\n            // Inline CSS currently does not work properly with HMR, so we only\n            // inline CSS in production.\n            !this.dev\n          ) {\n            return {\n              inlined: true,\n              path: file,\n              content: typeof source === 'string' ? source : source.toString(),\n            }\n          }\n          return {\n            inlined: false,\n            path: file,\n          }\n        })\n\n      const requiredChunks = getAppPathRequiredChunks(entrypoint, rootMainFiles)\n      const recordModule = (modId: ModuleId, mod: webpack.NormalModule) => {\n        let resource =\n          mod.type === 'css/mini-extract'\n            ? mod.identifier().slice(mod.identifier().lastIndexOf('!') + 1)\n            : mod.resource\n\n        if (!resource) {\n          return\n        }\n\n        const moduleReferences = manifest.clientModules\n        const moduleIdMapping = manifest.ssrModuleMapping\n        const edgeModuleIdMapping = manifest.edgeSSRModuleMapping\n\n        const rscIdMapping = manifest.rscModuleMapping\n        const edgeRscIdMapping = manifest.edgeRscModuleMapping\n\n        // Note that this isn't that reliable as webpack is still possible to assign\n        // additional queries to make sure there's no conflict even using the `named`\n        // module ID strategy.\n        let ssrNamedModuleId = relative(\n          context,\n          mod.resourceResolveData?.path || resource\n        )\n\n        const rscNamedModuleId = relative(\n          context,\n          mod.resourceResolveData?.path || resource\n        )\n\n        if (!ssrNamedModuleId.startsWith('.'))\n          ssrNamedModuleId = `./${ssrNamedModuleId.replace(/\\\\/g, '/')}`\n\n        // The client compiler will always use the CJS Next.js build, so here we\n        // also add the mapping for the ESM build (Edge runtime) to consume.\n        const esmResource = /[\\\\/]next[\\\\/]dist[\\\\/]/.test(resource)\n          ? resource.replace(\n              /[\\\\/]next[\\\\/]dist[\\\\/]/,\n              '/next/dist/esm/'.replace(/\\//g, path.sep)\n            )\n          : null\n\n        // An extra query param is added to the resource key when it's optimized\n        // through the Barrel Loader. That's because the same file might be created\n        // as multiple modules (depending on what you import from it).\n        // See also: webpack/loaders/next-flight-loader/index.ts.\n        if (mod.matchResource?.startsWith(BARREL_OPTIMIZATION_PREFIX)) {\n          ssrNamedModuleId = formatBarrelOptimizedResource(\n            ssrNamedModuleId,\n            mod.matchResource\n          )\n          resource = formatBarrelOptimizedResource(resource, mod.matchResource)\n        }\n\n        function addClientReference() {\n          const isAsync = Boolean(\n            compilation.moduleGraph.isAsync(mod) ||\n              pluginState.ssrModules[ssrNamedModuleId]?.async ||\n              pluginState.edgeSsrModules[ssrNamedModuleId]?.async\n          )\n\n          const exportName = resource\n          manifest.clientModules[exportName] = {\n            id: modId,\n            name: '*',\n            chunks: requiredChunks,\n            async: isAsync,\n          }\n          if (esmResource) {\n            const edgeExportName = esmResource\n            manifest.clientModules[edgeExportName] =\n              manifest.clientModules[exportName]\n          }\n        }\n\n        function addSSRIdMapping() {\n          const exportName = resource\n          const moduleInfo = pluginState.ssrModules[ssrNamedModuleId]\n\n          if (moduleInfo) {\n            moduleIdMapping[modId] = moduleIdMapping[modId] || {}\n            moduleIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: moduleInfo.moduleId,\n              async: moduleInfo.async,\n            }\n          }\n\n          const edgeModuleInfo = pluginState.edgeSsrModules[ssrNamedModuleId]\n\n          if (edgeModuleInfo) {\n            edgeModuleIdMapping[modId] = edgeModuleIdMapping[modId] || {}\n            edgeModuleIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: edgeModuleInfo.moduleId,\n              async: edgeModuleInfo.async,\n            }\n          }\n        }\n\n        function addRSCIdMapping() {\n          const exportName = resource\n          const moduleInfo = pluginState.rscModules[rscNamedModuleId]\n\n          if (moduleInfo) {\n            rscIdMapping[modId] = rscIdMapping[modId] || {}\n            rscIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: moduleInfo.moduleId,\n              async: moduleInfo.async,\n            }\n          }\n\n          const edgeModuleInfo = pluginState.ssrModules[rscNamedModuleId]\n\n          if (edgeModuleInfo) {\n            edgeRscIdMapping[modId] = edgeRscIdMapping[modId] || {}\n            edgeRscIdMapping[modId]['*'] = {\n              ...manifest.clientModules[exportName],\n              // During SSR, we don't have external chunks to load on the server\n              // side with our architecture of Webpack / Turbopack. We can keep\n              // this field empty to save some bytes.\n              chunks: [],\n              id: edgeModuleInfo.moduleId,\n              async: edgeModuleInfo.async,\n            }\n          }\n        }\n\n        addClientReference()\n        addSSRIdMapping()\n        addRSCIdMapping()\n\n        manifest.clientModules = moduleReferences\n        manifest.ssrModuleMapping = moduleIdMapping\n        manifest.edgeSSRModuleMapping = edgeModuleIdMapping\n        manifest.rscModuleMapping = rscIdMapping\n        manifest.edgeRscModuleMapping = edgeRscIdMapping\n      }\n\n      const checkedChunkGroups = new Set()\n      const checkedChunks = new Set()\n\n      function recordChunkGroup(chunkGroup: ChunkGroup) {\n        // Ensure recursion is stopped if we've already checked this chunk group.\n        if (checkedChunkGroups.has(chunkGroup)) return\n        checkedChunkGroups.add(chunkGroup)\n        // Only apply following logic to client module requests from client entry,\n        // or if the module is marked as client module. That's because other\n        // client modules don't need to be in the manifest at all as they're\n        // never be referenced by the server/client boundary.\n        // This saves a lot of bytes in the manifest.\n        chunkGroup.chunks.forEach((chunk: webpack.Chunk) => {\n          // Ensure recursion is stopped if we've already checked this chunk.\n          if (checkedChunks.has(chunk)) return\n          checkedChunks.add(chunk)\n          const entryMods =\n            compilation.chunkGraph.getChunkEntryModulesIterable(chunk)\n          for (const mod of entryMods) {\n            if (mod.layer !== WEBPACK_LAYERS.appPagesBrowser) continue\n\n            const request = (mod as webpack.NormalModule).request\n\n            if (\n              !request ||\n              !request.includes('next-flight-client-entry-loader.js?')\n            ) {\n              continue\n            }\n\n            const connections = getModuleReferencesInOrder(\n              mod,\n              compilation.moduleGraph\n            )\n\n            for (const connection of connections) {\n              const dependency = connection.dependency\n              if (!dependency) continue\n\n              const clientEntryMod = compilation.moduleGraph.getResolvedModule(\n                dependency\n              ) as webpack.NormalModule\n              const modId = compilation.chunkGraph.getModuleId(\n                clientEntryMod\n              ) as string | number | null\n\n              if (modId !== null) {\n                recordModule(modId, clientEntryMod)\n              } else {\n                // If this is a concatenation, register each child to the parent ID.\n                if (\n                  connection.module?.constructor.name === 'ConcatenatedModule'\n                ) {\n                  const concatenatedMod = connection.module\n                  const concatenatedModId =\n                    compilation.chunkGraph.getModuleId(concatenatedMod)\n                  if (concatenatedModId) {\n                    recordModule(concatenatedModId, clientEntryMod)\n                  }\n                }\n              }\n            }\n          }\n        })\n\n        // Walk through all children chunk groups too.\n        for (const child of chunkGroup.childrenIterable) {\n          recordChunkGroup(child)\n        }\n      }\n\n      recordChunkGroup(entrypoint)\n\n      // A page's entry name can have extensions. For example, these are both valid:\n      // - app/foo/page\n      // - app/foo/page.page\n      if (/\\/page(\\.[^/]+)?$/.test(entryName)) {\n        manifestEntryFiles.push(entryName.replace(/\\/page(\\.[^/]+)?$/, '/page'))\n      }\n\n      // We also need to create manifests for route handler entrypoints to\n      // enable `'use cache'`.\n      if (/\\/route$/.test(entryName)) {\n        manifestEntryFiles.push(entryName)\n      }\n\n      const groupName = entryNameToGroupName(entryName)\n      if (!manifestsPerGroup.has(groupName)) {\n        manifestsPerGroup.set(groupName, [])\n      }\n      manifestsPerGroup.get(groupName)!.push(manifest)\n    }\n\n    // Generate per-page manifests.\n    for (const pageName of manifestEntryFiles) {\n      const mergedManifest: ClientReferenceManifest = {\n        moduleLoading: {\n          prefix,\n          crossOrigin: crossOriginMode,\n        },\n        ssrModuleMapping: {},\n        edgeSSRModuleMapping: {},\n        clientModules: {},\n        entryCSSFiles: {},\n        rscModuleMapping: {},\n        edgeRscModuleMapping: {},\n      }\n\n      const segments = [...entryNameToGroupName(pageName).split('/'), 'page']\n      let group = ''\n      for (const segment of segments) {\n        for (const manifest of manifestsPerGroup.get(group) || []) {\n          mergeManifest(mergedManifest, manifest)\n        }\n        group += (group ? '/' : '') + segment\n      }\n\n      const json = JSON.stringify(mergedManifest)\n\n      const pagePath = pageName.replace(/%5F/g, '_')\n      const pageBundlePath = normalizePagePath(pagePath.slice('app'.length))\n      compilation.emitAsset(\n        'server/app' + pageBundlePath + '_' + CLIENT_REFERENCE_MANIFEST + '.js',\n        new sources.RawSource(\n          `globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST[${JSON.stringify(\n            pagePath.slice('app'.length)\n          )}]=${json}`\n        ) as unknown as webpack.sources.RawSource\n      )\n    }\n  }\n}\n"], "names": ["path", "webpack", "sources", "APP_CLIENT_INTERNALS", "BARREL_OPTIMIZATION_PREFIX", "CLIENT_REFERENCE_MANIFEST", "SYSTEM_ENTRYPOINTS", "relative", "getProxiedPluginState", "WEBPACK_LAYERS", "normalizePagePath", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "getDeploymentIdQueryOrEmptyString", "formatBarrelOptimizedResource", "getModuleReferencesInOrder", "encodeURIPath", "pluginState", "ssrModules", "edgeSsrModules", "rscModules", "edgeRscModules", "getAppPathRequiredChunks", "chunkGroup", "excludedFiles", "deploymentIdChunkQuery", "chunks", "for<PERSON>ach", "chunk", "has", "name", "id", "chunkId", "files", "file", "endsWith", "push", "entryNameToGroupName", "entryName", "groupName", "slice", "lastIndexOf", "replace", "test", "mergeManifest", "manifest", "manifestToMerge", "Object", "assign", "clientModules", "ssrModuleMapping", "edgeSSRModuleMapping", "entryCSSFiles", "rscModuleMapping", "edgeRscModuleMapping", "PLUGIN_NAME", "ClientReferenceManifestPlugin", "constructor", "options", "dev", "appDir", "appDirBase", "dirname", "sep", "experimentalInlineCss", "apply", "compiler", "hooks", "compilation", "tap", "processAssets", "stage", "Compilation", "PROCESS_ASSETS_STAGE_OPTIMIZE_HASH", "createAsset", "context", "manifestsPerGroup", "Map", "manifestEntryFiles", "configuredCrossOriginLoading", "outputOptions", "crossOriginLoading", "crossOriginMode", "undefined", "publicPath", "Error", "prefix", "rootMainFiles", "Set", "entrypoints", "get", "getFiles", "add", "entrypoint", "moduleLoading", "crossOrigin", "chunkEntryName", "filter", "f", "startsWith", "map", "source", "getAsset", "inlined", "content", "toString", "requiredChunks", "recordModule", "modId", "mod", "resource", "type", "identifier", "moduleReferences", "moduleIdMapping", "edgeModuleIdMapping", "rscIdMapping", "edgeRscIdMapping", "ssrNamedModuleId", "resourceResolveData", "rscNamedModuleId", "esmResource", "matchResource", "addClientReference", "isAsync", "Boolean", "moduleGraph", "async", "exportName", "edgeExportName", "addSSRIdMapping", "moduleInfo", "moduleId", "edgeModuleInfo", "addRSCIdMapping", "checkedChunkGroups", "checkedChunks", "recordChunkGroup", "entryMods", "chunkGraph", "getChunkEntryModulesIterable", "layer", "appPagesBrowser", "request", "includes", "connections", "connection", "dependency", "clientEntryMod", "getResolvedModule", "getModuleId", "module", "concatenatedMod", "concatenatedModId", "child", "childrenIterable", "set", "pageName", "mergedManifest", "segments", "split", "group", "segment", "json", "JSON", "stringify", "pagePath", "pageBundlePath", "length", "emitAsset", "RawSource"], "mappings": "AAAA;;;;;CAKC,GAED,OAAOA,UAAU,OAAM;AACvB,SAASC,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AACrE,SACEC,oBAAoB,EACpBC,0BAA0B,EAC1BC,yBAAyB,EACzBC,kBAAkB,QACb,gCAA+B;AACtC,SAASC,QAAQ,QAAQ,OAAM;AAC/B,SAASC,qBAAqB,QAAQ,sBAAqB;AAE3D,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,iBAAiB,QAAQ,oDAAmD;AACrF,SAASC,oCAAoC,QAAQ,gCAA+B;AACpF,SAASC,iCAAiC,QAAQ,sBAAqB;AACvE,SACEC,6BAA6B,EAC7BC,0BAA0B,QACrB,WAAU;AAEjB,SAASC,aAAa,QAAQ,sCAAqC;AAkBnE,MAAMC,cAAcR,sBAAsB;IACxCS,YAAY,CAAC;IACbC,gBAAgB,CAAC;IAEjBC,YAAY,CAAC;IACbC,gBAAgB,CAAC;AACnB;AAkEA,SAASC,yBACPC,UAA8B,EAC9BC,aAA0B;IAE1B,MAAMC,yBAAyBZ;IAE/B,MAAMa,SAAwB,EAAE;IAChCH,WAAWG,MAAM,CAACC,OAAO,CAAC,CAACC;QACzB,IAAIrB,mBAAmBsB,GAAG,CAACD,MAAME,IAAI,IAAI,KAAK;YAC5C,OAAO;QACT;QAEA,4DAA4D;QAC5D,+DAA+D;QAC/D,+DAA+D;QAC/D,mCAAmC;QACnC,IAAIF,MAAMG,EAAE,IAAI,MAAM;YACpB,MAAMC,UAAU,KAAKJ,MAAMG,EAAE;YAC7BH,MAAMK,KAAK,CAACN,OAAO,CAAC,CAACO;gBACnB,6DAA6D;gBAC7D,0BAA0B;gBAC1B,IAAI,CAACA,KAAKC,QAAQ,CAAC,QAAQ,OAAO;gBAClC,IAAID,KAAKC,QAAQ,CAAC,mBAAmB,OAAO;gBAC5C,IAAIX,cAAcK,GAAG,CAACK,OAAO,OAAO;gBAEpC,sFAAsF;gBACtF,iFAAiF;gBACjF,iFAAiF;gBACjF,iFAAiF;gBACjF,2DAA2D;gBAC3D,OAAOR,OAAOU,IAAI,CAChBJ,SACAhB,cAAckB,QAAQT;YAE1B;QACF;IACF;IACA,OAAOC;AACT;AAEA,8EAA8E;AAC9E,6EAA6E;AAC7E,YAAY;AACZ,+BAA+B;AAC/B,4BAA4B;AAC5B,2CAA2C;AAC3C,0CAA0C;AAC1C,kCAAkC;AAClC,gDAAgD;AAChD,SAASW,qBAAqBC,SAAiB;IAC7C,IAAIC,YAAYD,UACbE,KAAK,CAAC,GAAGF,UAAUG,WAAW,CAAC,KAChC,eAAe;KACdC,OAAO,CAAC,aAAa,GACtB,2EAA2E;KAC1EA,OAAO,CAAC,0BAA0B,GACnC,2GAA2G;IAC3G,gHAAgH;IAChH,mHAAmH;KAClHA,OAAO,CAAC,2BAA2B;IAEtC,sBAAsB;IACtBH,YAAYA,UACTG,OAAO,CAAC,oBAAoB,QAC5BA,OAAO,CAAC,aAAa;IAExB,kCAAkC;IAClC,MAAO,oBAAoBC,IAAI,CAACJ,WAAY;QAC1CA,YAAYA,UAAUG,OAAO,CAAC,sBAAsB;IACtD;IAEA,OAAOH;AACT;AAEA,SAASK,cACPC,QAAiC,EACjCC,eAAwC;IAExCC,OAAOC,MAAM,CAACH,SAASI,aAAa,EAAEH,gBAAgBG,aAAa;IACnEF,OAAOC,MAAM,CAACH,SAASK,gBAAgB,EAAEJ,gBAAgBI,gBAAgB;IACzEH,OAAOC,MAAM,CACXH,SAASM,oBAAoB,EAC7BL,gBAAgBK,oBAAoB;IAEtCJ,OAAOC,MAAM,CAACH,SAASO,aAAa,EAAEN,gBAAgBM,aAAa;IACnEL,OAAOC,MAAM,CAACH,SAASQ,gBAAgB,EAAEP,gBAAgBO,gBAAgB;IACzEN,OAAOC,MAAM,CACXH,SAASS,oBAAoB,EAC7BR,gBAAgBQ,oBAAoB;AAExC;AAEA,MAAMC,cAAc;AAEpB,OAAO,MAAMC;IAMXC,YAAYC,OAAgB,CAAE;aAL9BC,MAAsB;QAMpB,IAAI,CAACA,GAAG,GAAGD,QAAQC,GAAG;QACtB,IAAI,CAACC,MAAM,GAAGF,QAAQE,MAAM;QAC5B,IAAI,CAACC,UAAU,GAAG5D,KAAK6D,OAAO,CAAC,IAAI,CAACF,MAAM,IAAI3D,KAAK8D,GAAG;QACtD,IAAI,CAACC,qBAAqB,GAAGN,QAAQM,qBAAqB;IAC5D;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASC,KAAK,CAACC,WAAW,CAACC,GAAG,CAACd,aAAa,CAACa;YAC3CA,YAAYD,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEvC,MAAMyB;gBACN,iEAAiE;gBACjE,0CAA0C;gBAC1CgB,OAAOrE,QAAQsE,WAAW,CAACC,kCAAkC;YAC/D,GACA,IAAM,IAAI,CAACC,WAAW,CAACN,aAAaF,SAASS,OAAO;QAExD;IACF;IAEAD,YAAYN,WAAgC,EAAEO,OAAe,EAAE;YAuB7DP;QAtBA,MAAMQ,oBAAoB,IAAIC;QAC9B,MAAMC,qBAA+B,EAAE;QAEvC,MAAMC,+BACJX,YAAYY,aAAa,CAACC,kBAAkB;QAC9C,MAAMC,kBACJ,OAAOH,iCAAiC,WACpCA,iCAAiC,oBAC/BA,+BACA,GAAG,kBAAkB;WACvBI;QAEN,IAAI,OAAOf,YAAYY,aAAa,CAACI,UAAU,KAAK,UAAU;YAC5D,MAAM,qBAEL,CAFK,IAAIC,MACR,0SADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMC,SAASlB,YAAYY,aAAa,CAACI,UAAU,IAAI;QAEvD,8EAA8E;QAC9E,8DAA8D;QAC9D,MAAMG,gBAA6B,IAAIC;SACvCpB,+BAAAA,YAAYqB,WAAW,CACpBC,GAAG,CAAC9E,0DADPwD,6BAEIuB,QAAQ,GACThE,OAAO,CAAC,CAACO;YACR,IAAI,oCAAoCS,IAAI,CAACT,OAAO;gBAClDqD,cAAcK,GAAG,CAAC1D,KAAKQ,OAAO,CAAC,OAAO;YACxC;QACF;QAEF,KAAK,IAAI,CAACJ,WAAWuD,WAAW,IAAIzB,YAAYqB,WAAW,CAAE;YAC3D,IACEnD,cAAc1B,wCACd0B,cAAclC,sBACd;gBACAkC,YAAY;YACd,OAAO,IAAI,CAAC,YAAYK,IAAI,CAACL,YAAY;gBACvC;YACF;YAEA,MAAMO,WAAoC;gBACxCiD,eAAe;oBACbR;oBACAS,aAAab;gBACf;gBACAhC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;gBAChBC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;YACzB;YAEA,sCAAsC;YACtC,MAAM0C,iBAAiB,AAAC,CAAA,IAAI,CAACnC,UAAU,GAAGvB,SAAQ,EAAGI,OAAO,CAC1D,UACAzC,KAAK8D,GAAG;YAGVlB,SAASO,aAAa,CAAC4C,eAAe,GAAGH,WACtCF,QAAQ,GACRM,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEC,UAAU,CAAC,wBAAwBD,EAAE/D,QAAQ,CAAC,SAC/DiE,GAAG,CAAC,CAAClE;gBACJ,MAAMmE,SAASjC,YAAYkC,QAAQ,CAACpE,MAAOmE,MAAM,CAACA,MAAM;gBACxD,IACE,IAAI,CAACrC,qBAAqB,IAC1B,mEAAmE;gBACnE,4BAA4B;gBAC5B,CAAC,IAAI,CAACL,GAAG,EACT;oBACA,OAAO;wBACL4C,SAAS;wBACTtG,MAAMiC;wBACNsE,SAAS,OAAOH,WAAW,WAAWA,SAASA,OAAOI,QAAQ;oBAChE;gBACF;gBACA,OAAO;oBACLF,SAAS;oBACTtG,MAAMiC;gBACR;YACF;YAEF,MAAMwE,iBAAiBpF,yBAAyBuE,YAAYN;YAC5D,MAAMoB,eAAe,CAACC,OAAiBC;oBAsBnCA,0BAKAA,2BAmBEA;gBA7CJ,IAAIC,WACFD,IAAIE,IAAI,KAAK,qBACTF,IAAIG,UAAU,GAAGxE,KAAK,CAACqE,IAAIG,UAAU,GAAGvE,WAAW,CAAC,OAAO,KAC3DoE,IAAIC,QAAQ;gBAElB,IAAI,CAACA,UAAU;oBACb;gBACF;gBAEA,MAAMG,mBAAmBpE,SAASI,aAAa;gBAC/C,MAAMiE,kBAAkBrE,SAASK,gBAAgB;gBACjD,MAAMiE,sBAAsBtE,SAASM,oBAAoB;gBAEzD,MAAMiE,eAAevE,SAASQ,gBAAgB;gBAC9C,MAAMgE,mBAAmBxE,SAASS,oBAAoB;gBAEtD,4EAA4E;gBAC5E,6EAA6E;gBAC7E,sBAAsB;gBACtB,IAAIgE,mBAAmB9G,SACrBmE,SACAkC,EAAAA,2BAAAA,IAAIU,mBAAmB,qBAAvBV,yBAAyB5G,IAAI,KAAI6G;gBAGnC,MAAMU,mBAAmBhH,SACvBmE,SACAkC,EAAAA,4BAAAA,IAAIU,mBAAmB,qBAAvBV,0BAAyB5G,IAAI,KAAI6G;gBAGnC,IAAI,CAACQ,iBAAiBnB,UAAU,CAAC,MAC/BmB,mBAAmB,CAAC,EAAE,EAAEA,iBAAiB5E,OAAO,CAAC,OAAO,MAAM;gBAEhE,wEAAwE;gBACxE,oEAAoE;gBACpE,MAAM+E,cAAc,0BAA0B9E,IAAI,CAACmE,YAC/CA,SAASpE,OAAO,CACd,2BACA,kBAAkBA,OAAO,CAAC,OAAOzC,KAAK8D,GAAG,KAE3C;gBAEJ,wEAAwE;gBACxE,2EAA2E;gBAC3E,8DAA8D;gBAC9D,yDAAyD;gBACzD,KAAI8C,qBAAAA,IAAIa,aAAa,qBAAjBb,mBAAmBV,UAAU,CAAC9F,6BAA6B;oBAC7DiH,mBAAmBxG,8BACjBwG,kBACAT,IAAIa,aAAa;oBAEnBZ,WAAWhG,8BAA8BgG,UAAUD,IAAIa,aAAa;gBACtE;gBAEA,SAASC;wBAGH1G,0CACAA;oBAHJ,MAAM2G,UAAUC,QACdzD,YAAY0D,WAAW,CAACF,OAAO,CAACf,UAC9B5F,2CAAAA,YAAYC,UAAU,CAACoG,iBAAiB,qBAAxCrG,yCAA0C8G,KAAK,OAC/C9G,+CAAAA,YAAYE,cAAc,CAACmG,iBAAiB,qBAA5CrG,6CAA8C8G,KAAK;oBAGvD,MAAMC,aAAalB;oBACnBjE,SAASI,aAAa,CAAC+E,WAAW,GAAG;wBACnCjG,IAAI6E;wBACJ9E,MAAM;wBACNJ,QAAQgF;wBACRqB,OAAOH;oBACT;oBACA,IAAIH,aAAa;wBACf,MAAMQ,iBAAiBR;wBACvB5E,SAASI,aAAa,CAACgF,eAAe,GACpCpF,SAASI,aAAa,CAAC+E,WAAW;oBACtC;gBACF;gBAEA,SAASE;oBACP,MAAMF,aAAalB;oBACnB,MAAMqB,aAAalH,YAAYC,UAAU,CAACoG,iBAAiB;oBAE3D,IAAIa,YAAY;wBACdjB,eAAe,CAACN,MAAM,GAAGM,eAAe,CAACN,MAAM,IAAI,CAAC;wBACpDM,eAAe,CAACN,MAAM,CAAC,IAAI,GAAG;4BAC5B,GAAG/D,SAASI,aAAa,CAAC+E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCtG,QAAQ,EAAE;4BACVK,IAAIoG,WAAWC,QAAQ;4BACvBL,OAAOI,WAAWJ,KAAK;wBACzB;oBACF;oBAEA,MAAMM,iBAAiBpH,YAAYE,cAAc,CAACmG,iBAAiB;oBAEnE,IAAIe,gBAAgB;wBAClBlB,mBAAmB,CAACP,MAAM,GAAGO,mBAAmB,CAACP,MAAM,IAAI,CAAC;wBAC5DO,mBAAmB,CAACP,MAAM,CAAC,IAAI,GAAG;4BAChC,GAAG/D,SAASI,aAAa,CAAC+E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCtG,QAAQ,EAAE;4BACVK,IAAIsG,eAAeD,QAAQ;4BAC3BL,OAAOM,eAAeN,KAAK;wBAC7B;oBACF;gBACF;gBAEA,SAASO;oBACP,MAAMN,aAAalB;oBACnB,MAAMqB,aAAalH,YAAYG,UAAU,CAACoG,iBAAiB;oBAE3D,IAAIW,YAAY;wBACdf,YAAY,CAACR,MAAM,GAAGQ,YAAY,CAACR,MAAM,IAAI,CAAC;wBAC9CQ,YAAY,CAACR,MAAM,CAAC,IAAI,GAAG;4BACzB,GAAG/D,SAASI,aAAa,CAAC+E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCtG,QAAQ,EAAE;4BACVK,IAAIoG,WAAWC,QAAQ;4BACvBL,OAAOI,WAAWJ,KAAK;wBACzB;oBACF;oBAEA,MAAMM,iBAAiBpH,YAAYC,UAAU,CAACsG,iBAAiB;oBAE/D,IAAIa,gBAAgB;wBAClBhB,gBAAgB,CAACT,MAAM,GAAGS,gBAAgB,CAACT,MAAM,IAAI,CAAC;wBACtDS,gBAAgB,CAACT,MAAM,CAAC,IAAI,GAAG;4BAC7B,GAAG/D,SAASI,aAAa,CAAC+E,WAAW;4BACrC,kEAAkE;4BAClE,iEAAiE;4BACjE,uCAAuC;4BACvCtG,QAAQ,EAAE;4BACVK,IAAIsG,eAAeD,QAAQ;4BAC3BL,OAAOM,eAAeN,KAAK;wBAC7B;oBACF;gBACF;gBAEAJ;gBACAO;gBACAI;gBAEAzF,SAASI,aAAa,GAAGgE;gBACzBpE,SAASK,gBAAgB,GAAGgE;gBAC5BrE,SAASM,oBAAoB,GAAGgE;gBAChCtE,SAASQ,gBAAgB,GAAG+D;gBAC5BvE,SAASS,oBAAoB,GAAG+D;YAClC;YAEA,MAAMkB,qBAAqB,IAAI/C;YAC/B,MAAMgD,gBAAgB,IAAIhD;YAE1B,SAASiD,iBAAiBlH,UAAsB;gBAC9C,yEAAyE;gBACzE,IAAIgH,mBAAmB1G,GAAG,CAACN,aAAa;gBACxCgH,mBAAmB3C,GAAG,CAACrE;gBACvB,0EAA0E;gBAC1E,oEAAoE;gBACpE,oEAAoE;gBACpE,qDAAqD;gBACrD,6CAA6C;gBAC7CA,WAAWG,MAAM,CAACC,OAAO,CAAC,CAACC;oBACzB,mEAAmE;oBACnE,IAAI4G,cAAc3G,GAAG,CAACD,QAAQ;oBAC9B4G,cAAc5C,GAAG,CAAChE;oBAClB,MAAM8G,YACJtE,YAAYuE,UAAU,CAACC,4BAA4B,CAAChH;oBACtD,KAAK,MAAMiF,OAAO6B,UAAW;wBAC3B,IAAI7B,IAAIgC,KAAK,KAAKnI,eAAeoI,eAAe,EAAE;wBAElD,MAAMC,UAAU,AAAClC,IAA6BkC,OAAO;wBAErD,IACE,CAACA,WACD,CAACA,QAAQC,QAAQ,CAAC,wCAClB;4BACA;wBACF;wBAEA,MAAMC,cAAclI,2BAClB8F,KACAzC,YAAY0D,WAAW;wBAGzB,KAAK,MAAMoB,cAAcD,YAAa;4BACpC,MAAME,aAAaD,WAAWC,UAAU;4BACxC,IAAI,CAACA,YAAY;4BAEjB,MAAMC,iBAAiBhF,YAAY0D,WAAW,CAACuB,iBAAiB,CAC9DF;4BAEF,MAAMvC,QAAQxC,YAAYuE,UAAU,CAACW,WAAW,CAC9CF;4BAGF,IAAIxC,UAAU,MAAM;gCAClBD,aAAaC,OAAOwC;4BACtB,OAAO;oCAGHF;gCAFF,oEAAoE;gCACpE,IACEA,EAAAA,qBAAAA,WAAWK,MAAM,qBAAjBL,mBAAmBzF,WAAW,CAAC3B,IAAI,MAAK,sBACxC;oCACA,MAAM0H,kBAAkBN,WAAWK,MAAM;oCACzC,MAAME,oBACJrF,YAAYuE,UAAU,CAACW,WAAW,CAACE;oCACrC,IAAIC,mBAAmB;wCACrB9C,aAAa8C,mBAAmBL;oCAClC;gCACF;4BACF;wBACF;oBACF;gBACF;gBAEA,8CAA8C;gBAC9C,KAAK,MAAMM,SAASnI,WAAWoI,gBAAgB,CAAE;oBAC/ClB,iBAAiBiB;gBACnB;YACF;YAEAjB,iBAAiB5C;YAEjB,8EAA8E;YAC9E,iBAAiB;YACjB,sBAAsB;YACtB,IAAI,oBAAoBlD,IAAI,CAACL,YAAY;gBACvCwC,mBAAmB1C,IAAI,CAACE,UAAUI,OAAO,CAAC,qBAAqB;YACjE;YAEA,oEAAoE;YACpE,wBAAwB;YACxB,IAAI,WAAWC,IAAI,CAACL,YAAY;gBAC9BwC,mBAAmB1C,IAAI,CAACE;YAC1B;YAEA,MAAMC,YAAYF,qBAAqBC;YACvC,IAAI,CAACsC,kBAAkB/C,GAAG,CAACU,YAAY;gBACrCqC,kBAAkBgF,GAAG,CAACrH,WAAW,EAAE;YACrC;YACAqC,kBAAkBc,GAAG,CAACnD,WAAYH,IAAI,CAACS;QACzC;QAEA,+BAA+B;QAC/B,KAAK,MAAMgH,YAAY/E,mBAAoB;YACzC,MAAMgF,iBAA0C;gBAC9ChE,eAAe;oBACbR;oBACAS,aAAab;gBACf;gBACAhC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;gBACvBF,eAAe,CAAC;gBAChBG,eAAe,CAAC;gBAChBC,kBAAkB,CAAC;gBACnBC,sBAAsB,CAAC;YACzB;YAEA,MAAMyG,WAAW;mBAAI1H,qBAAqBwH,UAAUG,KAAK,CAAC;gBAAM;aAAO;YACvE,IAAIC,QAAQ;YACZ,KAAK,MAAMC,WAAWH,SAAU;gBAC9B,KAAK,MAAMlH,YAAY+B,kBAAkBc,GAAG,CAACuE,UAAU,EAAE,CAAE;oBACzDrH,cAAckH,gBAAgBjH;gBAChC;gBACAoH,SAAS,AAACA,CAAAA,QAAQ,MAAM,EAAC,IAAKC;YAChC;YAEA,MAAMC,OAAOC,KAAKC,SAAS,CAACP;YAE5B,MAAMQ,WAAWT,SAASnH,OAAO,CAAC,QAAQ;YAC1C,MAAM6H,iBAAiB5J,kBAAkB2J,SAAS9H,KAAK,CAAC,MAAMgI,MAAM;YACpEpG,YAAYqG,SAAS,CACnB,eAAeF,iBAAiB,MAAMjK,4BAA4B,OAClE,IAAIH,QAAQuK,SAAS,CACnB,CAAC,oFAAoF,EAAEN,KAAKC,SAAS,CACnGC,SAAS9H,KAAK,CAAC,MAAMgI,MAAM,GAC3B,EAAE,EAAEL,MAAM;QAGlB;IACF;AACF"}