"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    generateId() {\n        return crypto.randomUUID();\n    }\n    async checkWCTEligibility(address) {\n        try {\n            var _data_result;\n            const requestBody = {\n                method: \"getBalance\",\n                jsonrpc: \"2.0\",\n                params: [\n                    address,\n                    {\n                        commitment: \"confirmed\"\n                    }\n                ],\n                id: this.generateId()\n            };\n            const response = await fetch(this.RPC_URL, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Accept': '*/*',\n                    'solana-client': 'js/1.0.0-maintenance'\n                },\n                body: JSON.stringify(requestBody)\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            if (data.error) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: data.error.message\n                };\n            }\n            const wctAmount = ((_data_result = data.result) === null || _data_result === void 0 ? void 0 : _data_result.value) || 0;\n            return {\n                address,\n                balance: wctAmount,\n                isEligible: wctAmount > 0,\n                eligibleAmount: wctAmount,\n                error: undefined\n            };\n        } catch (error) {\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n    constructor(){\n        this.RPC_URL = 'https://rpc.walletconnect.org/v1/?chainId=solana%**********************************&projectId=b366e97e24223af7a0c0ad4003303698';\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/solana-api.ts\n"));

/***/ })

});