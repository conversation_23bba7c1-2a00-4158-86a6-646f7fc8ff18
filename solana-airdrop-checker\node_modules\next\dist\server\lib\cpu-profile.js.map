{"version": 3, "sources": ["../../../src/server/lib/cpu-profile.ts"], "sourcesContent": ["const privateCpuProfileName = process.env.__NEXT_PRIVATE_CPU_PROFILE\nconst isCpuProfileEnabled = process.env.NEXT_CPU_PROF || privateCpuProfileName\n\nif (isCpuProfileEnabled) {\n  const { Session } = require('inspector') as typeof import('inspector')\n  const fs = require('fs')\n\n  const session = new Session()\n  session.connect()\n\n  session.post('Profiler.enable')\n  session.post('Profiler.start')\n\n  function saveProfile() {\n    session.post('Profiler.stop', (error, param) => {\n      if (error) {\n        console.error('Cannot generate CPU profiling:', error)\n        return\n      }\n\n      // Write profile to disk\n      const filename = `${\n        privateCpuProfileName || 'CPU.main'\n      }.${Date.now()}.cpuprofile`\n      fs.writeFileSync(`./${filename}`, JSON.stringify(param.profile))\n      process.exit(0)\n    })\n  }\n  process.on('SIGINT', saveProfile)\n  process.on('SIGTERM', saveProfile)\n  process.on('exit', saveProfile)\n}\n"], "names": ["privateCpuProfileName", "process", "env", "__NEXT_PRIVATE_CPU_PROFILE", "isCpuProfileEnabled", "NEXT_CPU_PROF", "Session", "require", "fs", "session", "connect", "post", "saveProfile", "error", "param", "console", "filename", "Date", "now", "writeFileSync", "JSON", "stringify", "profile", "exit", "on"], "mappings": ";AAAA,MAAMA,wBAAwBC,QAAQC,GAAG,CAACC,0BAA0B;AACpE,MAAMC,sBAAsBH,QAAQC,GAAG,CAACG,aAAa,IAAIL;AAEzD,IAAII,qBAAqB;IACvB,MAAM,EAAEE,OAAO,EAAE,GAAGC,QAAQ;IAC5B,MAAMC,KAAKD,QAAQ;IAEnB,MAAME,UAAU,IAAIH;IACpBG,QAAQC,OAAO;IAEfD,QAAQE,IAAI,CAAC;IACbF,QAAQE,IAAI,CAAC;IAEb,SAASC;QACPH,QAAQE,IAAI,CAAC,iBAAiB,CAACE,OAAOC;YACpC,IAAID,OAAO;gBACTE,QAAQF,KAAK,CAAC,kCAAkCA;gBAChD;YACF;YAEA,wBAAwB;YACxB,MAAMG,WAAW,GACfhB,yBAAyB,WAC1B,CAAC,EAAEiB,KAAKC,GAAG,GAAG,WAAW,CAAC;YAC3BV,GAAGW,aAAa,CAAC,CAAC,EAAE,EAAEH,UAAU,EAAEI,KAAKC,SAAS,CAACP,MAAMQ,OAAO;YAC9DrB,QAAQsB,IAAI,CAAC;QACf;IACF;IACAtB,QAAQuB,EAAE,CAAC,UAAUZ;IACrBX,QAAQuB,EAAE,CAAC,WAAWZ;IACtBX,QAAQuB,EAAE,CAAC,QAAQZ;AACrB"}