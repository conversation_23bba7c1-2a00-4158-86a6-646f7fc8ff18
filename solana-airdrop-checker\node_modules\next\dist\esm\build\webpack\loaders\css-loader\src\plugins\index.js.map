{"version": 3, "sources": ["../../../../../../../src/build/webpack/loaders/css-loader/src/plugins/index.ts"], "sourcesContent": ["import importParser from './postcss-import-parser'\nimport icssParser from './postcss-icss-parser'\nimport urlParser from './postcss-url-parser'\n\nexport { importParser, icssParser, urlParser }\n"], "names": ["importParser", "icss<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,OAAOA,kBAAkB,0BAAyB;AAClD,OAAOC,gBAAgB,wBAAuB;AAC9C,OAAOC,eAAe,uBAAsB;AAE5C,SAASF,YAAY,EAAEC,UAAU,EAAEC,SAAS,GAAE"}