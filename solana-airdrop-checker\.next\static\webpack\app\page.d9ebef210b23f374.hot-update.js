"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    async checkWCTEligibility(address) {\n        try {\n            const response = await fetch(\"\".concat(this.AIRDROP_API_URL, \"/\").concat(address), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            // Check if recipient not found (not eligible)\n            if (data.error && data.error.includes('not found')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: undefined\n                };\n            }\n            // If there's any other error\n            if (data.error) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: data.error\n                };\n            }\n            // Extract eligible amount from response (hex format)\n            let eligibleAmount = 0;\n            if (data.amount) {\n                // Convert hex amount to decimal and then to WCT tokens\n                const hexAmount = data.amount.startsWith('0x') ? data.amount : '0x' + data.amount;\n                const decimalAmount = parseInt(hexAmount, 16);\n                // Convert from smallest unit to WCT tokens (assuming 18 decimals like most tokens)\n                eligibleAmount = decimalAmount / Math.pow(10, 18);\n            }\n            return {\n                address,\n                balance: eligibleAmount,\n                isEligible: eligibleAmount > 0,\n                eligibleAmount,\n                error: undefined\n            };\n        } catch (error) {\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n    constructor(){\n        this.AIRDROP_API_URL = 'https://api.walletconnect.network/airdrop/solana';\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/solana-api.ts\n"));

/***/ })

});