/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjaGVjZWtyJTIwd2N0JTIwc29sYW5hJTIwb2clNUMlNUNzb2xhbmEtYWlyZHJvcC1jaGVja2VyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcY2hlY2VrciB3Y3Qgc29sYW5hIG9nXFxcXHNvbGFuYS1haXJkcm9wLWNoZWNrZXJcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxjaGVjZWtyIHdjdCBzb2xhbmEgb2dcXHNvbGFuYS1haXJkcm9wLWNoZWNrZXJcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcY2hlY2VrciB3Y3Qgc29sYW5hIG9nXFxzb2xhbmEtYWlyZHJvcC1jaGVja2VyXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIkQ6XFxjaGVjZWtyIHdjdCBzb2xhbmEgb2dcXHNvbGFuYS1haXJkcm9wLWNoZWNrZXJcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgR2Vpc3QsIEdlaXN0X01vbm8gfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBnZWlzdFNhbnMgPSBHZWlzdCh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1zYW5zXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmNvbnN0IGdlaXN0TW9ubyA9IEdlaXN0X01vbm8oe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3QtbW9ub1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogXCJDcmVhdGUgTmV4dCBBcHBcIixcbiAgZGVzY3JpcHRpb246IFwiR2VuZXJhdGVkIGJ5IGNyZWF0ZSBuZXh0IGFwcFwiLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5XG4gICAgICAgIGNsYXNzTmFtZT17YCR7Z2Vpc3RTYW5zLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gYW50aWFsaWFzZWRgfVxuICAgICAgPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImdlaXN0U2FucyIsImdlaXN0TW9ubyIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\checekr wct solana og\\solana-airdrop-checker\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNjaGVjZWtyJTIwd2N0JTIwc29sYW5hJTIwb2clNUMlNUNzb2xhbmEtYWlyZHJvcC1jaGVja2VyJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdKQUEwRyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcY2hlY2VrciB3Y3Qgc29sYW5hIG9nXFxcXHNvbGFuYS1haXJkcm9wLWNoZWNrZXJcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cchecekr%20wct%20solana%20og%5C%5Csolana-airdrop-checker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SingleAddressChecker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SingleAddressChecker */ \"(ssr)/./src/components/SingleAddressChecker.tsx\");\n/* harmony import */ var _components_BulkAddressChecker__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/BulkAddressChecker */ \"(ssr)/./src/components/BulkAddressChecker.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('single');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"WalletConnect Token (WCT) Airdrop Checker\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Check WCT airdrop eligibility and amounts\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Powered by WalletConnect RPC\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('single'),\n                                    className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'single' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                                    children: \"Single Address\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab('bulk'),\n                                    className: `py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'bulk' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n                                    children: \"Bulk Checker\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            activeTab === 'single' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SingleAddressChecker__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 38\n                            }, this),\n                            activeTab === 'bulk' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BulkAddressChecker__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 36\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 bg-blue-50 rounded-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-blue-900 mb-3\",\n                                children: \"How it works\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid md:grid-cols-2 gap-6 text-sm text-blue-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Single Address Checker\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 66,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Enter a single Solana wallet address\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Get instant WCT airdrop eligibility status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• See eligible WCT token amount\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Perfect for quick checks\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 71,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Bulk Checker\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Check up to 100 addresses at once\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Upload text file or paste addresses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 78,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• See all eligible amounts in one table\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Export results to CSV\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Progress tracking for large batches\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 81,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Built for checking WalletConnect Token (WCT) airdrop eligibility\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2\",\n                                children: \"Using WalletConnect RPC API for reliable data\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/BulkAddressChecker.tsx":
/*!***********************************************!*\
  !*** ./src/components/BulkAddressChecker.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BulkAddressChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/solana-api */ \"(ssr)/./src/lib/solana-api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction BulkAddressChecker() {\n    const [addresses, setAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        current: 0,\n        total: 0\n    });\n    const parseAddresses = (input)=>{\n        return input.split(/[\\n,;]/).map((addr)=>addr.trim()).filter((addr)=>addr.length > 0);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!addresses.trim()) {\n            alert('Please enter wallet addresses');\n            return;\n        }\n        const addressList = parseAddresses(addresses);\n        if (addressList.length === 0) {\n            alert('No valid addresses found');\n            return;\n        }\n        if (addressList.length > 100) {\n            alert('Maximum 100 addresses allowed at once');\n            return;\n        }\n        // Validate addresses\n        const invalidAddresses = addressList.filter((addr)=>!_lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.isValidSolanaAddress(addr));\n        if (invalidAddresses.length > 0) {\n            alert(`Invalid addresses found: ${invalidAddresses.slice(0, 3).join(', ')}${invalidAddresses.length > 3 ? '...' : ''}`);\n            return;\n        }\n        setLoading(true);\n        setResults([]);\n        setProgress({\n            current: 0,\n            total: addressList.length\n        });\n        try {\n            // Process addresses in batches to avoid overwhelming the API\n            const batchSize = 10;\n            const allResults = [];\n            for(let i = 0; i < addressList.length; i += batchSize){\n                const batch = addressList.slice(i, i + batchSize);\n                const batchResults = await _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.checkMultipleWCTEligibility(batch);\n                allResults.push(...batchResults);\n                setProgress({\n                    current: allResults.length,\n                    total: addressList.length\n                });\n                setResults([\n                    ...allResults\n                ]); // Update results progressively\n                // Small delay between batches to be respectful to the API\n                if (i + batchSize < addressList.length) {\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                }\n            }\n            setResults(allResults);\n        } catch (error) {\n            console.error('Error checking WCT eligibility:', error);\n            alert('Error occurred while checking WCT eligibility');\n        } finally{\n            setLoading(false);\n            setProgress({\n                current: 0,\n                total: 0\n            });\n        }\n    };\n    const handleClear = ()=>{\n        setAddresses('');\n        setResults([]);\n    };\n    const handleFileUpload = (e)=>{\n        const file = e.target.files?.[0];\n        if (!file) return;\n        const reader = new FileReader();\n        reader.onload = (event)=>{\n            const content = event.target?.result;\n            setAddresses(content);\n        };\n        reader.readAsText(file);\n    };\n    const exportResults = ()=>{\n        if (results.length === 0) return;\n        const csvContent = [\n            'Address,WCT Airdrop Status,Eligible Amount (WCT),Error',\n            ...results.map((result)=>`\"${result.address}\",${result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'},${result.eligibleAmount.toLocaleString()},\"${result.error || ''}\"`)\n        ].join('\\n');\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `wct-airdrop-results-${new Date().toISOString().split('T')[0]}.csv`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n    };\n    const eligibleCount = results.filter((r)=>r.isEligible).length;\n    const errorCount = results.filter((r)=>r.error).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"WCT Airdrop Checker - Bulk\"\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"addresses\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Wallet Addresses (one per line, or comma/semicolon separated)\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                id: \"addresses\",\n                                value: addresses,\n                                onChange: (e)=>setAddresses(e.target.value),\n                                placeholder: \"Enter multiple Solana wallet addresses...\",\n                                rows: 8,\n                                className: \"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                disabled: loading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Or upload a text file\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"file\",\n                                accept: \".txt,.csv\",\n                                onChange: handleFileUpload,\n                                disabled: loading,\n                                className: \"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: loading ? `Checking... (${progress.current}/${progress.total})` : 'Check WCT Eligibility'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleClear,\n                                disabled: loading,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-200 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${progress.total > 0 ? progress.current / progress.total * 100 : 0}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 mt-1\",\n                        children: [\n                            \"Processing \",\n                            progress.current,\n                            \" of \",\n                            progress.total,\n                            \" addresses...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, this),\n            results.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-lg\",\n                                children: [\n                                    \"Results (\",\n                                    results.length,\n                                    \" addresses)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: exportResults,\n                                className: \"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\",\n                                children: \"Export CSV\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 p-3 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: results.length\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-blue-600\",\n                                        children: \"Total Checked\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 p-3 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: eligibleCount\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-green-600\",\n                                        children: \"Eligible\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 p-3 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-red-600\",\n                                        children: errorCount\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-red-600\",\n                                        children: \"Errors\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto border rounded-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 sticky top-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"Address\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"WCT Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"Eligible Amount\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-4 py-2 text-left\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-t\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2 font-mono text-xs break-all\",\n                                                    children: result.address\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-2 py-1 rounded text-xs font-medium ${result.isEligible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                                                        children: result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: result.isEligible ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-semibold text-green-600\",\n                                                        children: [\n                                                            result.eligibleAmount.toLocaleString(),\n                                                            \" WCT\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-400\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-4 py-2\",\n                                                    children: result.error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-600 text-xs\",\n                                                        children: result.error\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-600 text-xs\",\n                                                        children: \"✓ Success\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n                lineNumber: 188,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\BulkAddressChecker.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9CdWxrQWRkcmVzc0NoZWNrZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFFaUM7QUFDa0M7QUFFcEQsU0FBU0U7SUFDdEIsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdKLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ0ssU0FBU0MsV0FBVyxHQUFHTiwrQ0FBUUEsQ0FBcUIsRUFBRTtJQUM3RCxNQUFNLENBQUNPLFNBQVNDLFdBQVcsR0FBR1IsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDUyxVQUFVQyxZQUFZLEdBQUdWLCtDQUFRQSxDQUFDO1FBQUVXLFNBQVM7UUFBR0MsT0FBTztJQUFFO0lBRWhFLE1BQU1DLGlCQUFpQixDQUFDQztRQUN0QixPQUFPQSxNQUNKQyxLQUFLLENBQUMsVUFDTkMsR0FBRyxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxJQUFJLElBQ3JCQyxNQUFNLENBQUNGLENBQUFBLE9BQVFBLEtBQUtHLE1BQU0sR0FBRztJQUNsQztJQUVBLE1BQU1DLGVBQWUsT0FBT0M7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDcEIsVUFBVWUsSUFBSSxJQUFJO1lBQ3JCTSxNQUFNO1lBQ047UUFDRjtRQUVBLE1BQU1DLGNBQWNaLGVBQWVWO1FBRW5DLElBQUlzQixZQUFZTCxNQUFNLEtBQUssR0FBRztZQUM1QkksTUFBTTtZQUNOO1FBQ0Y7UUFFQSxJQUFJQyxZQUFZTCxNQUFNLEdBQUcsS0FBSztZQUM1QkksTUFBTTtZQUNOO1FBQ0Y7UUFFQSxxQkFBcUI7UUFDckIsTUFBTUUsbUJBQW1CRCxZQUFZTixNQUFNLENBQUNGLENBQUFBLE9BQVEsQ0FBQ2hCLDBEQUFhQSxDQUFDMEIsb0JBQW9CLENBQUNWO1FBQ3hGLElBQUlTLGlCQUFpQk4sTUFBTSxHQUFHLEdBQUc7WUFDL0JJLE1BQU0sQ0FBQyx5QkFBeUIsRUFBRUUsaUJBQWlCRSxLQUFLLENBQUMsR0FBRyxHQUFHQyxJQUFJLENBQUMsUUFBUUgsaUJBQWlCTixNQUFNLEdBQUcsSUFBSSxRQUFRLElBQUk7WUFDdEg7UUFDRjtRQUVBWixXQUFXO1FBQ1hGLFdBQVcsRUFBRTtRQUNiSSxZQUFZO1lBQUVDLFNBQVM7WUFBR0MsT0FBT2EsWUFBWUwsTUFBTTtRQUFDO1FBRXBELElBQUk7WUFDRiw2REFBNkQ7WUFDN0QsTUFBTVUsWUFBWTtZQUNsQixNQUFNQyxhQUFpQyxFQUFFO1lBRXpDLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJUCxZQUFZTCxNQUFNLEVBQUVZLEtBQUtGLFVBQVc7Z0JBQ3RELE1BQU1HLFFBQVFSLFlBQVlHLEtBQUssQ0FBQ0ksR0FBR0EsSUFBSUY7Z0JBQ3ZDLE1BQU1JLGVBQWUsTUFBTWpDLDBEQUFhQSxDQUFDa0MsMkJBQTJCLENBQUNGO2dCQUNyRUYsV0FBV0ssSUFBSSxJQUFJRjtnQkFFbkJ4QixZQUFZO29CQUFFQyxTQUFTb0IsV0FBV1gsTUFBTTtvQkFBRVIsT0FBT2EsWUFBWUwsTUFBTTtnQkFBQztnQkFDcEVkLFdBQVc7dUJBQUl5QjtpQkFBVyxHQUFHLCtCQUErQjtnQkFFNUQsMERBQTBEO2dCQUMxRCxJQUFJQyxJQUFJRixZQUFZTCxZQUFZTCxNQUFNLEVBQUU7b0JBQ3RDLE1BQU0sSUFBSWlCLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7Z0JBQ25EO1lBQ0Y7WUFFQWhDLFdBQVd5QjtRQUNiLEVBQUUsT0FBT1MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTtZQUNqRGhCLE1BQU07UUFDUixTQUFVO1lBQ1JoQixXQUFXO1lBQ1hFLFlBQVk7Z0JBQUVDLFNBQVM7Z0JBQUdDLE9BQU87WUFBRTtRQUNyQztJQUNGO0lBRUEsTUFBTThCLGNBQWM7UUFDbEJ0QyxhQUFhO1FBQ2JFLFdBQVcsRUFBRTtJQUNmO0lBRUEsTUFBTXFDLG1CQUFtQixDQUFDckI7UUFDeEIsTUFBTXNCLE9BQU90QixFQUFFdUIsTUFBTSxDQUFDQyxLQUFLLEVBQUUsQ0FBQyxFQUFFO1FBQ2hDLElBQUksQ0FBQ0YsTUFBTTtRQUVYLE1BQU1HLFNBQVMsSUFBSUM7UUFDbkJELE9BQU9FLE1BQU0sR0FBRyxDQUFDQztZQUNmLE1BQU1DLFVBQVVELE1BQU1MLE1BQU0sRUFBRU87WUFDOUJoRCxhQUFhK0M7UUFDZjtRQUNBSixPQUFPTSxVQUFVLENBQUNUO0lBQ3BCO0lBRUEsTUFBTVUsZ0JBQWdCO1FBQ3BCLElBQUlqRCxRQUFRZSxNQUFNLEtBQUssR0FBRztRQUUxQixNQUFNbUMsYUFBYTtZQUNqQjtlQUNHbEQsUUFBUVcsR0FBRyxDQUFDb0MsQ0FBQUEsU0FDYixDQUFDLENBQUMsRUFBRUEsT0FBT0ksT0FBTyxDQUFDLEVBQUUsRUFBRUosT0FBT0ssVUFBVSxHQUFHLGFBQWEsZUFBZSxDQUFDLEVBQUVMLE9BQU9NLGNBQWMsQ0FBQ0MsY0FBYyxHQUFHLEVBQUUsRUFBRVAsT0FBT1osS0FBSyxJQUFJLEdBQUcsQ0FBQyxDQUFDO1NBRTdJLENBQUNYLElBQUksQ0FBQztRQUVQLE1BQU0rQixPQUFPLElBQUlDLEtBQUs7WUFBQ047U0FBVyxFQUFFO1lBQUVPLE1BQU07UUFBVztRQUN2RCxNQUFNQyxNQUFNQyxJQUFJQyxlQUFlLENBQUNMO1FBQ2hDLE1BQU1NLElBQUlDLFNBQVNDLGFBQWEsQ0FBQztRQUNqQ0YsRUFBRUcsSUFBSSxHQUFHTjtRQUNURyxFQUFFSSxRQUFRLEdBQUcsQ0FBQyxvQkFBb0IsRUFBRSxJQUFJQyxPQUFPQyxXQUFXLEdBQUd6RCxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7UUFDaEZvRCxTQUFTTSxJQUFJLENBQUNDLFdBQVcsQ0FBQ1I7UUFDMUJBLEVBQUVTLEtBQUs7UUFDUFIsU0FBU00sSUFBSSxDQUFDRyxXQUFXLENBQUNWO1FBQzFCRixJQUFJYSxlQUFlLENBQUNkO0lBQ3RCO0lBRUEsTUFBTWUsZ0JBQWdCekUsUUFBUWMsTUFBTSxDQUFDNEQsQ0FBQUEsSUFBS0EsRUFBRXRCLFVBQVUsRUFBRXJDLE1BQU07SUFDOUQsTUFBTTRELGFBQWEzRSxRQUFRYyxNQUFNLENBQUM0RCxDQUFBQSxJQUFLQSxFQUFFdkMsS0FBSyxFQUFFcEIsTUFBTTtJQUV0RCxxQkFDRSw4REFBQzZEO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBd0M7Ozs7OzswQkFFdEQsOERBQUNFO2dCQUFLQyxVQUFVaEU7Z0JBQWM2RCxXQUFVOztrQ0FDdEMsOERBQUNEOzswQ0FDQyw4REFBQ0s7Z0NBQU1DLFNBQVE7Z0NBQVlMLFdBQVU7MENBQStDOzs7Ozs7MENBR3BGLDhEQUFDTTtnQ0FDQ0MsSUFBRztnQ0FDSEMsT0FBT3ZGO2dDQUNQd0YsVUFBVSxDQUFDckUsSUFBTWxCLGFBQWFrQixFQUFFdUIsTUFBTSxDQUFDNkMsS0FBSztnQ0FDNUNFLGFBQVk7Z0NBQ1pDLE1BQU07Z0NBQ05YLFdBQVU7Z0NBQ1ZZLFVBQVV2Rjs7Ozs7Ozs7Ozs7O2tDQUlkLDhEQUFDMEU7OzBDQUNDLDhEQUFDSztnQ0FBTUosV0FBVTswQ0FBK0M7Ozs7OzswQ0FHaEUsOERBQUNwRTtnQ0FDQ2dELE1BQUs7Z0NBQ0xpQyxRQUFPO2dDQUNQSixVQUFVaEQ7Z0NBQ1ZtRCxVQUFVdkY7Z0NBQ1YyRSxXQUFVOzs7Ozs7Ozs7Ozs7a0NBSWQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2M7Z0NBQ0NsQyxNQUFLO2dDQUNMZ0MsVUFBVXZGO2dDQUNWMkUsV0FBVTswQ0FFVDNFLFVBQVUsQ0FBQyxhQUFhLEVBQUVFLFNBQVNFLE9BQU8sQ0FBQyxDQUFDLEVBQUVGLFNBQVNHLEtBQUssQ0FBQyxDQUFDLENBQUMsR0FBRzs7Ozs7OzBDQUdyRSw4REFBQ29GO2dDQUNDbEMsTUFBSztnQ0FDTG1DLFNBQVN2RDtnQ0FDVG9ELFVBQVV2RjtnQ0FDVjJFLFdBQVU7MENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1KM0UseUJBQ0MsOERBQUMwRTtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFDQ0MsV0FBVTs0QkFDVmdCLE9BQU87Z0NBQUVDLE9BQU8sR0FBRzFGLFNBQVNHLEtBQUssR0FBRyxJQUFJLFNBQVVELE9BQU8sR0FBR0YsU0FBU0csS0FBSyxHQUFJLE1BQU0sRUFBRSxDQUFDLENBQUM7NEJBQUM7Ozs7Ozs7Ozs7O2tDQUc3Riw4REFBQ3dGO3dCQUFFbEIsV0FBVTs7NEJBQTZCOzRCQUM1QnpFLFNBQVNFLE9BQU87NEJBQUM7NEJBQUtGLFNBQVNHLEtBQUs7NEJBQUM7Ozs7Ozs7Ozs7Ozs7WUFLdERQLFFBQVFlLE1BQU0sR0FBRyxtQkFDaEIsOERBQUM2RDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ21CO2dDQUFHbkIsV0FBVTs7b0NBQXdCO29DQUFVN0UsUUFBUWUsTUFBTTtvQ0FBQzs7Ozs7OzswQ0FDL0QsOERBQUM0RTtnQ0FDQ0MsU0FBUzNDO2dDQUNUNEIsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7O2tDQUtILDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQW9DN0UsUUFBUWUsTUFBTTs7Ozs7O2tEQUNqRSw4REFBQzZEO3dDQUFJQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQUV6Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFBcUNKOzs7Ozs7a0RBQ3BELDhEQUFDRzt3Q0FBSUMsV0FBVTtrREFBeUI7Ozs7Ozs7Ozs7OzswQ0FFMUMsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQW1DRjs7Ozs7O2tEQUNsRCw4REFBQ0M7d0NBQUlDLFdBQVU7a0RBQXVCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSTFDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ29COzRCQUFNcEIsV0FBVTs7OENBQ2YsOERBQUNxQjtvQ0FBTXJCLFdBQVU7OENBQ2YsNEVBQUNzQjs7MERBQ0MsOERBQUNDO2dEQUFHdkIsV0FBVTswREFBc0I7Ozs7OzswREFDcEMsOERBQUN1QjtnREFBR3ZCLFdBQVU7MERBQXNCOzs7Ozs7MERBQ3BDLDhEQUFDdUI7Z0RBQUd2QixXQUFVOzBEQUFzQjs7Ozs7OzBEQUNwQyw4REFBQ3VCO2dEQUFHdkIsV0FBVTswREFBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUd4Qyw4REFBQ3dCOzhDQUNFckcsUUFBUVcsR0FBRyxDQUFDLENBQUNvQyxRQUFRdUQsc0JBQ3BCLDhEQUFDSDs0Q0FBZXRCLFdBQVU7OzhEQUN4Qiw4REFBQzBCO29EQUFHMUIsV0FBVTs4REFBeUM5QixPQUFPSSxPQUFPOzs7Ozs7OERBQ3JFLDhEQUFDb0Q7b0RBQUcxQixXQUFVOzhEQUNaLDRFQUFDMkI7d0RBQUszQixXQUFXLENBQUMsc0NBQXNDLEVBQ3REOUIsT0FBT0ssVUFBVSxHQUNiLGdDQUNBLDJCQUNKO2tFQUNDTCxPQUFPSyxVQUFVLEdBQUcsYUFBYTs7Ozs7Ozs7Ozs7OERBR3RDLDhEQUFDbUQ7b0RBQUcxQixXQUFVOzhEQUNYOUIsT0FBT0ssVUFBVSxpQkFDaEIsOERBQUNvRDt3REFBSzNCLFdBQVU7OzREQUNiOUIsT0FBT00sY0FBYyxDQUFDQyxjQUFjOzREQUFHOzs7Ozs7NkVBRzFDLDhEQUFDa0Q7d0RBQUszQixXQUFVO2tFQUFnQjs7Ozs7Ozs7Ozs7OERBR3BDLDhEQUFDMEI7b0RBQUcxQixXQUFVOzhEQUNYOUIsT0FBT1osS0FBSyxpQkFDWCw4REFBQ3FFO3dEQUFLM0IsV0FBVTtrRUFBd0I5QixPQUFPWixLQUFLOzs7Ozs2RUFFcEQsOERBQUNxRTt3REFBSzNCLFdBQVU7a0VBQXlCOzs7Ozs7Ozs7Ozs7MkNBeEJ0Q3lCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFvQzNCIiwic291cmNlcyI6WyJEOlxcY2hlY2VrciB3Y3Qgc29sYW5hIG9nXFxzb2xhbmEtYWlyZHJvcC1jaGVja2VyXFxzcmNcXGNvbXBvbmVudHNcXEJ1bGtBZGRyZXNzQ2hlY2tlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHdjdEFpcmRyb3BBUEksIFdDVEFpcmRyb3BSZXN1bHQgfSBmcm9tICdAL2xpYi9zb2xhbmEtYXBpJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQnVsa0FkZHJlc3NDaGVja2VyKCkge1xuICBjb25zdCBbYWRkcmVzc2VzLCBzZXRBZGRyZXNzZXNdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbcmVzdWx0cywgc2V0UmVzdWx0c10gPSB1c2VTdGF0ZTxXQ1RBaXJkcm9wUmVzdWx0W10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcHJvZ3Jlc3MsIHNldFByb2dyZXNzXSA9IHVzZVN0YXRlKHsgY3VycmVudDogMCwgdG90YWw6IDAgfSk7XG5cbiAgY29uc3QgcGFyc2VBZGRyZXNzZXMgPSAoaW5wdXQ6IHN0cmluZyk6IHN0cmluZ1tdID0+IHtcbiAgICByZXR1cm4gaW5wdXRcbiAgICAgIC5zcGxpdCgvW1xcbiw7XS8pXG4gICAgICAubWFwKGFkZHIgPT4gYWRkci50cmltKCkpXG4gICAgICAuZmlsdGVyKGFkZHIgPT4gYWRkci5sZW5ndGggPiAwKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIFxuICAgIGlmICghYWRkcmVzc2VzLnRyaW0oKSkge1xuICAgICAgYWxlcnQoJ1BsZWFzZSBlbnRlciB3YWxsZXQgYWRkcmVzc2VzJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgY29uc3QgYWRkcmVzc0xpc3QgPSBwYXJzZUFkZHJlc3NlcyhhZGRyZXNzZXMpO1xuICAgIFxuICAgIGlmIChhZGRyZXNzTGlzdC5sZW5ndGggPT09IDApIHtcbiAgICAgIGFsZXJ0KCdObyB2YWxpZCBhZGRyZXNzZXMgZm91bmQnKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoYWRkcmVzc0xpc3QubGVuZ3RoID4gMTAwKSB7XG4gICAgICBhbGVydCgnTWF4aW11bSAxMDAgYWRkcmVzc2VzIGFsbG93ZWQgYXQgb25jZScpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGFkZHJlc3Nlc1xuICAgIGNvbnN0IGludmFsaWRBZGRyZXNzZXMgPSBhZGRyZXNzTGlzdC5maWx0ZXIoYWRkciA9PiAhd2N0QWlyZHJvcEFQSS5pc1ZhbGlkU29sYW5hQWRkcmVzcyhhZGRyKSk7XG4gICAgaWYgKGludmFsaWRBZGRyZXNzZXMubGVuZ3RoID4gMCkge1xuICAgICAgYWxlcnQoYEludmFsaWQgYWRkcmVzc2VzIGZvdW5kOiAke2ludmFsaWRBZGRyZXNzZXMuc2xpY2UoMCwgMykuam9pbignLCAnKX0ke2ludmFsaWRBZGRyZXNzZXMubGVuZ3RoID4gMyA/ICcuLi4nIDogJyd9YCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRSZXN1bHRzKFtdKTtcbiAgICBzZXRQcm9ncmVzcyh7IGN1cnJlbnQ6IDAsIHRvdGFsOiBhZGRyZXNzTGlzdC5sZW5ndGggfSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gUHJvY2VzcyBhZGRyZXNzZXMgaW4gYmF0Y2hlcyB0byBhdm9pZCBvdmVyd2hlbG1pbmcgdGhlIEFQSVxuICAgICAgY29uc3QgYmF0Y2hTaXplID0gMTA7XG4gICAgICBjb25zdCBhbGxSZXN1bHRzOiBXQ1RBaXJkcm9wUmVzdWx0W10gPSBbXTtcblxuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBhZGRyZXNzTGlzdC5sZW5ndGg7IGkgKz0gYmF0Y2hTaXplKSB7XG4gICAgICAgIGNvbnN0IGJhdGNoID0gYWRkcmVzc0xpc3Quc2xpY2UoaSwgaSArIGJhdGNoU2l6ZSk7XG4gICAgICAgIGNvbnN0IGJhdGNoUmVzdWx0cyA9IGF3YWl0IHdjdEFpcmRyb3BBUEkuY2hlY2tNdWx0aXBsZVdDVEVsaWdpYmlsaXR5KGJhdGNoKTtcbiAgICAgICAgYWxsUmVzdWx0cy5wdXNoKC4uLmJhdGNoUmVzdWx0cyk7XG4gICAgICAgIFxuICAgICAgICBzZXRQcm9ncmVzcyh7IGN1cnJlbnQ6IGFsbFJlc3VsdHMubGVuZ3RoLCB0b3RhbDogYWRkcmVzc0xpc3QubGVuZ3RoIH0pO1xuICAgICAgICBzZXRSZXN1bHRzKFsuLi5hbGxSZXN1bHRzXSk7IC8vIFVwZGF0ZSByZXN1bHRzIHByb2dyZXNzaXZlbHlcbiAgICAgICAgXG4gICAgICAgIC8vIFNtYWxsIGRlbGF5IGJldHdlZW4gYmF0Y2hlcyB0byBiZSByZXNwZWN0ZnVsIHRvIHRoZSBBUElcbiAgICAgICAgaWYgKGkgKyBiYXRjaFNpemUgPCBhZGRyZXNzTGlzdC5sZW5ndGgpIHtcbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgc2V0UmVzdWx0cyhhbGxSZXN1bHRzKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2hlY2tpbmcgV0NUIGVsaWdpYmlsaXR5OicsIGVycm9yKTtcbiAgICAgIGFsZXJ0KCdFcnJvciBvY2N1cnJlZCB3aGlsZSBjaGVja2luZyBXQ1QgZWxpZ2liaWxpdHknKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICBzZXRQcm9ncmVzcyh7IGN1cnJlbnQ6IDAsIHRvdGFsOiAwIH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVDbGVhciA9ICgpID0+IHtcbiAgICBzZXRBZGRyZXNzZXMoJycpO1xuICAgIHNldFJlc3VsdHMoW10pO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUZpbGVVcGxvYWQgPSAoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcbiAgICBjb25zdCBmaWxlID0gZS50YXJnZXQuZmlsZXM/LlswXTtcbiAgICBpZiAoIWZpbGUpIHJldHVybjtcblxuICAgIGNvbnN0IHJlYWRlciA9IG5ldyBGaWxlUmVhZGVyKCk7XG4gICAgcmVhZGVyLm9ubG9hZCA9IChldmVudCkgPT4ge1xuICAgICAgY29uc3QgY29udGVudCA9IGV2ZW50LnRhcmdldD8ucmVzdWx0IGFzIHN0cmluZztcbiAgICAgIHNldEFkZHJlc3Nlcyhjb250ZW50KTtcbiAgICB9O1xuICAgIHJlYWRlci5yZWFkQXNUZXh0KGZpbGUpO1xuICB9O1xuXG4gIGNvbnN0IGV4cG9ydFJlc3VsdHMgPSAoKSA9PiB7XG4gICAgaWYgKHJlc3VsdHMubGVuZ3RoID09PSAwKSByZXR1cm47XG5cbiAgICBjb25zdCBjc3ZDb250ZW50ID0gW1xuICAgICAgJ0FkZHJlc3MsV0NUIEFpcmRyb3AgU3RhdHVzLEVsaWdpYmxlIEFtb3VudCAoV0NUKSxFcnJvcicsXG4gICAgICAuLi5yZXN1bHRzLm1hcChyZXN1bHQgPT5cbiAgICAgICAgYFwiJHtyZXN1bHQuYWRkcmVzc31cIiwke3Jlc3VsdC5pc0VsaWdpYmxlID8gJ0VMSUdJQkxFJyA6ICdOT1QgRUxJR0lCTEUnfSwke3Jlc3VsdC5lbGlnaWJsZUFtb3VudC50b0xvY2FsZVN0cmluZygpfSxcIiR7cmVzdWx0LmVycm9yIHx8ICcnfVwiYFxuICAgICAgKVxuICAgIF0uam9pbignXFxuJyk7XG5cbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NzdkNvbnRlbnRdLCB7IHR5cGU6ICd0ZXh0L2NzdicgfSk7XG4gICAgY29uc3QgdXJsID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTtcbiAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgIGEuaHJlZiA9IHVybDtcbiAgICBhLmRvd25sb2FkID0gYHdjdC1haXJkcm9wLXJlc3VsdHMtJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0uY3N2YDtcbiAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGEpO1xuICAgIGEuY2xpY2soKTtcbiAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGEpO1xuICAgIFVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbiAgfTtcblxuICBjb25zdCBlbGlnaWJsZUNvdW50ID0gcmVzdWx0cy5maWx0ZXIociA9PiByLmlzRWxpZ2libGUpLmxlbmd0aDtcbiAgY29uc3QgZXJyb3JDb3VudCA9IHJlc3VsdHMuZmlsdGVyKHIgPT4gci5lcnJvcikubGVuZ3RoO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBwLTZcIj5cbiAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTgwMCBtYi02XCI+V0NUIEFpcmRyb3AgQ2hlY2tlciAtIEJ1bGs8L2gyPlxuICAgICAgXG4gICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImFkZHJlc3Nlc1wiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICBXYWxsZXQgQWRkcmVzc2VzIChvbmUgcGVyIGxpbmUsIG9yIGNvbW1hL3NlbWljb2xvbiBzZXBhcmF0ZWQpXG4gICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgIGlkPVwiYWRkcmVzc2VzXCJcbiAgICAgICAgICAgIHZhbHVlPXthZGRyZXNzZXN9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEFkZHJlc3NlcyhlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIG11bHRpcGxlIFNvbGFuYSB3YWxsZXQgYWRkcmVzc2VzLi4uXCJcbiAgICAgICAgICAgIHJvd3M9ezh9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdj5cbiAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgIE9yIHVwbG9hZCBhIHRleHQgZmlsZVxuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgICBhY2NlcHQ9XCIudHh0LC5jc3ZcIlxuICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbGVVcGxvYWR9XG4gICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJsb2NrIHctZnVsbCB0ZXh0LXNtIHRleHQtZ3JheS01MDAgZmlsZTptci00IGZpbGU6cHktMiBmaWxlOnB4LTQgZmlsZTpyb3VuZGVkLW1kIGZpbGU6Ym9yZGVyLTAgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gZmlsZTpiZy1ibHVlLTUwIGZpbGU6dGV4dC1ibHVlLTcwMCBob3ZlcjpmaWxlOmJnLWJsdWUtMTAwXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtM1wiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxuICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctYmx1ZS02MDAgdGV4dC13aGl0ZSBweS0yIHB4LTQgcm91bmRlZC1tZCBob3ZlcjpiZy1ibHVlLTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2xvYWRpbmcgPyBgQ2hlY2tpbmcuLi4gKCR7cHJvZ3Jlc3MuY3VycmVudH0vJHtwcm9ncmVzcy50b3RhbH0pYCA6ICdDaGVjayBXQ1QgRWxpZ2liaWxpdHknfVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xlYXJ9XG4gICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHRleHQtZ3JheS03MDAgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmF5LTUwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICBDbGVhclxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9ybT5cblxuICAgICAge2xvYWRpbmcgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktMjAwIHJvdW5kZWQtZnVsbCBoLTJcIj5cbiAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGgtMiByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6IGAke3Byb2dyZXNzLnRvdGFsID4gMCA/IChwcm9ncmVzcy5jdXJyZW50IC8gcHJvZ3Jlc3MudG90YWwpICogMTAwIDogMH0lYCB9fVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbXQtMVwiPlxuICAgICAgICAgICAgUHJvY2Vzc2luZyB7cHJvZ3Jlc3MuY3VycmVudH0gb2Yge3Byb2dyZXNzLnRvdGFsfSBhZGRyZXNzZXMuLi5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAge3Jlc3VsdHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTRcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtbGdcIj5SZXN1bHRzICh7cmVzdWx0cy5sZW5ndGh9IGFkZHJlc3Nlcyk8L2gzPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtleHBvcnRSZXN1bHRzfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSBweC00IHB5LTIgcm91bmRlZC1tZCBob3ZlcjpiZy1ncmVlbi03MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBFeHBvcnQgQ1NWXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMyBnYXAtNCBtYi00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgcC0zIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ibHVlLTYwMFwiPntyZXN1bHRzLmxlbmd0aH08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS02MDBcIj5Ub3RhbCBDaGVja2VkPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JlZW4tNTAgcC0zIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmVlbi02MDBcIj57ZWxpZ2libGVDb3VudH08L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JlZW4tNjAwXCI+RWxpZ2libGU8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1yZWQtNTAgcC0zIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1yZWQtNjAwXCI+e2Vycm9yQ291bnR9PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXJlZC02MDBcIj5FcnJvcnM8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtaC05NiBvdmVyZmxvdy15LWF1dG8gYm9yZGVyIHJvdW5kZWQtbWRcIj5cbiAgICAgICAgICAgIDx0YWJsZSBjbGFzc05hbWU9XCJ3LWZ1bGwgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBzdGlja3kgdG9wLTBcIj5cbiAgICAgICAgICAgICAgICA8dHI+XG4gICAgICAgICAgICAgICAgICA8dGggY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtbGVmdFwiPkFkZHJlc3M8L3RoPlxuICAgICAgICAgICAgICAgICAgPHRoIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWxlZnRcIj5XQ1QgU3RhdHVzPC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1sZWZ0XCI+RWxpZ2libGUgQW1vdW50PC90aD5cbiAgICAgICAgICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC00IHB5LTIgdGV4dC1sZWZ0XCI+U3RhdHVzPC90aD5cbiAgICAgICAgICAgICAgICA8L3RyPlxuICAgICAgICAgICAgICA8L3RoZWFkPlxuICAgICAgICAgICAgICA8dGJvZHk+XG4gICAgICAgICAgICAgICAge3Jlc3VsdHMubWFwKChyZXN1bHQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8dHIga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiYm9yZGVyLXRcIj5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMiBmb250LW1vbm8gdGV4dC14cyBicmVhay1hbGxcIj57cmVzdWx0LmFkZHJlc3N9PC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gJHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlc3VsdC5pc0VsaWdpYmxlXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3Jlc3VsdC5pc0VsaWdpYmxlID8gJ0VMSUdJQkxFJyA6ICdOT1QgRUxJR0lCTEUnfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQuaXNFbGlnaWJsZSA/IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Jlc3VsdC5lbGlnaWJsZUFtb3VudC50b0xvY2FsZVN0cmluZygpfSBXQ1RcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPi08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTQgcHktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtyZXN1bHQuZXJyb3IgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC02MDAgdGV4dC14c1wiPntyZXN1bHQuZXJyb3J9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyZWVuLTYwMCB0ZXh0LXhzXCI+4pyTIFN1Y2Nlc3M8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC90ZD5cbiAgICAgICAgICAgICAgICAgIDwvdHI+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICAgICA8L3RhYmxlPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ3Y3RBaXJkcm9wQVBJIiwiQnVsa0FkZHJlc3NDaGVja2VyIiwiYWRkcmVzc2VzIiwic2V0QWRkcmVzc2VzIiwicmVzdWx0cyIsInNldFJlc3VsdHMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInByb2dyZXNzIiwic2V0UHJvZ3Jlc3MiLCJjdXJyZW50IiwidG90YWwiLCJwYXJzZUFkZHJlc3NlcyIsImlucHV0Iiwic3BsaXQiLCJtYXAiLCJhZGRyIiwidHJpbSIsImZpbHRlciIsImxlbmd0aCIsImhhbmRsZVN1Ym1pdCIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImFsZXJ0IiwiYWRkcmVzc0xpc3QiLCJpbnZhbGlkQWRkcmVzc2VzIiwiaXNWYWxpZFNvbGFuYUFkZHJlc3MiLCJzbGljZSIsImpvaW4iLCJiYXRjaFNpemUiLCJhbGxSZXN1bHRzIiwiaSIsImJhdGNoIiwiYmF0Y2hSZXN1bHRzIiwiY2hlY2tNdWx0aXBsZVdDVEVsaWdpYmlsaXR5IiwicHVzaCIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImVycm9yIiwiY29uc29sZSIsImhhbmRsZUNsZWFyIiwiaGFuZGxlRmlsZVVwbG9hZCIsImZpbGUiLCJ0YXJnZXQiLCJmaWxlcyIsInJlYWRlciIsIkZpbGVSZWFkZXIiLCJvbmxvYWQiLCJldmVudCIsImNvbnRlbnQiLCJyZXN1bHQiLCJyZWFkQXNUZXh0IiwiZXhwb3J0UmVzdWx0cyIsImNzdkNvbnRlbnQiLCJhZGRyZXNzIiwiaXNFbGlnaWJsZSIsImVsaWdpYmxlQW1vdW50IiwidG9Mb2NhbGVTdHJpbmciLCJibG9iIiwiQmxvYiIsInR5cGUiLCJ1cmwiLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsInJldm9rZU9iamVjdFVSTCIsImVsaWdpYmxlQ291bnQiLCJyIiwiZXJyb3JDb3VudCIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwiZm9ybSIsIm9uU3VibWl0IiwibGFiZWwiLCJodG1sRm9yIiwidGV4dGFyZWEiLCJpZCIsInZhbHVlIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsInJvd3MiLCJkaXNhYmxlZCIsImFjY2VwdCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzdHlsZSIsIndpZHRoIiwicCIsImgzIiwidGFibGUiLCJ0aGVhZCIsInRyIiwidGgiLCJ0Ym9keSIsImluZGV4IiwidGQiLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/BulkAddressChecker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SingleAddressChecker.tsx":
/*!*************************************************!*\
  !*** ./src/components/SingleAddressChecker.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SingleAddressChecker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/solana-api */ \"(ssr)/./src/lib/solana-api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SingleAddressChecker() {\n    const [address, setAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!address.trim()) {\n            alert('Please enter a Solana address');\n            return;\n        }\n        if (!_lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.isValidSolanaAddress(address.trim())) {\n            alert('Please enter a valid Solana address');\n            return;\n        }\n        setLoading(true);\n        setResult(null);\n        try {\n            const result = await _lib_solana_api__WEBPACK_IMPORTED_MODULE_2__.wctAirdropAPI.checkWCTEligibility(address.trim());\n            setResult(result);\n        } catch (error) {\n            console.error('Error checking WCT eligibility:', error);\n            setResult({\n                address: address.trim(),\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: 'Failed to check WCT eligibility'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleClear = ()=>{\n        setAddress('');\n        setResult(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold text-gray-800 mb-6\",\n                children: \"WCT Airdrop Checker - Single Address\"\n            }, void 0, false, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"address\",\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Solana Wallet Address\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"address\",\n                                value: address,\n                                onChange: (e)=>setAddress(e.target.value),\n                                placeholder: \"Enter Solana wallet address (e.g., 54iFFejXJWnkydLqExz3tk8aTkaY1mYtdVtmhaxv44rw)\",\n                                className: \"w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                disabled: loading\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: loading ? 'Checking...' : 'Check WCT Eligibility'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleClear,\n                                disabled: loading,\n                                className: \"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-4 border rounded-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-lg mb-3\",\n                        children: \"Result\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Address:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-mono text-sm break-all\",\n                                        children: result.address\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"WCT Airdrop Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `ml-2 px-2 py-1 rounded text-sm font-medium ${result.isEligible ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                                        children: result.isEligible ? 'ELIGIBLE' : 'NOT ELIGIBLE'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            result.isEligible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Eligible Amount:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 font-semibold text-green-600\",\n                                        children: [\n                                            result.eligibleAmount.toLocaleString(),\n                                            \" WCT\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this),\n                            result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-red-600\",\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 text-red-600\",\n                                        children: result.error\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\checekr wct solana og\\\\solana-airdrop-checker\\\\src\\\\components\\\\SingleAddressChecker.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SingleAddressChecker.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    async checkWCTEligibility(address) {\n        try {\n            const response = await fetch(`${this.AIRDROP_API_URL}/${address}`, {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            // Check if recipient not found (not eligible)\n            if (data.error && data.error.includes('not found')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: undefined\n                };\n            }\n            // If there's any other error\n            if (data.error) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: data.error\n                };\n            }\n            // Extract eligible amount from response\n            const eligibleAmount = data.amount || data.value || data.eligibleAmount || data.tokens || 0;\n            return {\n                address,\n                balance: eligibleAmount,\n                isEligible: eligibleAmount > 0,\n                eligibleAmount,\n                error: undefined\n            };\n        } catch (error) {\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n    constructor(){\n        this.AIRDROP_API_URL = 'https://api.walletconnect.network/airdrop/solana';\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/solana-api.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cchecekr%20wct%20solana%20og%5Csolana-airdrop-checker&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();