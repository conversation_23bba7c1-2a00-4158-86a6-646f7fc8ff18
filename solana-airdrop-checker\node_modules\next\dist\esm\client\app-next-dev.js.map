{"version": 3, "sources": ["../../src/client/app-next-dev.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport './app-webpack'\n\nimport { appBootstrap } from './app-bootstrap'\nimport { initializeDevBuildIndicatorForAppRouter } from './dev/dev-build-indicator/initialize-for-app-router'\n\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  hydrate(instrumentationHooks)\n  initializeDevBuildIndicatorForAppRouter()\n})\n"], "names": ["appBootstrap", "initializeDevBuildIndicatorForAppRouter", "<PERSON><PERSON><PERSON><PERSON>", "require", "hydrate"], "mappings": "AAAA,8BAA8B;AAE9B,OAAO,gBAAe;AAEtB,SAASA,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,uCAAuC,QAAQ,sDAAqD;AAE7G,MAAMC,uBAAuBC,QAAQ;AAErCH,aAAa;IACX,MAAM,EAAEI,OAAO,EAAE,GAAGD,QAAQ;IAC5BC,QAAQF;IACRD;AACF"}