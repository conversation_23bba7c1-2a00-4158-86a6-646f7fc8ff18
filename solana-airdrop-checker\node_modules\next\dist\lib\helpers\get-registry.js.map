{"version": 3, "sources": ["../../../src/lib/helpers/get-registry.ts"], "sourcesContent": ["import { execSync } from 'child_process'\nimport { getPkgManager } from './get-pkg-manager'\nimport { getFormattedNodeOptionsWithoutInspect } from '../../server/lib/utils'\n\n/**\n * Returns the package registry using the user's package manager.\n * The URL will have a trailing slash.\n * @default https://registry.npmjs.org/\n */\nexport function getRegistry(baseDir: string = process.cwd()) {\n  const pkgManager = getPkgManager(baseDir)\n  // Since `npm config` command fails in npm workspace to prevent workspace config conflicts,\n  // add `--no-workspaces` flag to run under the context of the root project only.\n  // Safe for non-workspace projects as it's equivalent to default `--workspaces=false`.\n  // x-ref: https://github.com/vercel/next.js/issues/47121#issuecomment-1499044345\n  // x-ref: https://github.com/npm/statusboard/issues/371#issue-920669998\n  const resolvedFlags = pkgManager === 'npm' ? '--no-workspaces' : ''\n  let registry = `https://registry.npmjs.org/`\n\n  try {\n    const output = execSync(\n      `${pkgManager} config get registry ${resolvedFlags}`,\n      {\n        env: {\n          ...process.env,\n          NODE_OPTIONS: getFormattedNodeOptionsWithoutInspect(),\n        },\n      }\n    )\n      .toString()\n      .trim()\n\n    if (output.startsWith('http')) {\n      registry = output.endsWith('/') ? output : `${output}/`\n    }\n  } catch (err) {\n    throw new Error(`Failed to get registry from \"${pkgManager}\".`, {\n      cause: err,\n    })\n  }\n\n  return registry\n}\n"], "names": ["getRegistry", "baseDir", "process", "cwd", "pkgManager", "getPkgManager", "resolvedFlags", "registry", "output", "execSync", "env", "NODE_OPTIONS", "getFormattedNodeOptionsWithoutInspect", "toString", "trim", "startsWith", "endsWith", "err", "Error", "cause"], "mappings": ";;;;+BASgBA;;;eAAAA;;;+BATS;+BACK;uBACwB;AAO/C,SAASA,YAAYC,UAAkBC,QAAQC,GAAG,EAAE;IACzD,MAAMC,aAAaC,IAAAA,4BAAa,EAACJ;IACjC,2FAA2F;IAC3F,gFAAgF;IAChF,sFAAsF;IACtF,gFAAgF;IAChF,uEAAuE;IACvE,MAAMK,gBAAgBF,eAAe,QAAQ,oBAAoB;IACjE,IAAIG,WAAW,CAAC,2BAA2B,CAAC;IAE5C,IAAI;QACF,MAAMC,SAASC,IAAAA,uBAAQ,EACrB,GAAGL,WAAW,qBAAqB,EAAEE,eAAe,EACpD;YACEI,KAAK;gBACH,GAAGR,QAAQQ,GAAG;gBACdC,cAAcC,IAAAA,4CAAqC;YACrD;QACF,GAECC,QAAQ,GACRC,IAAI;QAEP,IAAIN,OAAOO,UAAU,CAAC,SAAS;YAC7BR,WAAWC,OAAOQ,QAAQ,CAAC,OAAOR,SAAS,GAAGA,OAAO,CAAC,CAAC;QACzD;IACF,EAAE,OAAOS,KAAK;QACZ,MAAM,qBAEJ,CAFI,IAAIC,MAAM,CAAC,6BAA6B,EAAEd,WAAW,EAAE,CAAC,EAAE;YAC9De,OAAOF;QACT,IAFM,qBAAA;mBAAA;wBAAA;0BAAA;QAEL;IACH;IAEA,OAAOV;AACT"}