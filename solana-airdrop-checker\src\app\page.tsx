'use client';

import { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>cker from '@/components/SingleAddressChecker';
import BulkAddressChecker from '@/components/BulkAddressChecker';

export default function Home() {
  const [activeTab, setActiveTab] = useState<'single' | 'bulk'>('single');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">WalletConnect Token (WCT) Airdrop Checker</h1>
              <p className="text-gray-600 mt-1">Check WCT airdrop eligibility and amounts</p>
            </div>
            <div className="text-sm text-gray-500">
              Powered by WalletConnect RPC
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tab Navigation */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('single')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'single'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Single Address
            </button>
            <button
              onClick={() => setActiveTab('bulk')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'bulk'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Bulk Checker
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-8">
          {activeTab === 'single' && <SingleAddressChecker />}
          {activeTab === 'bulk' && <BulkAddressChecker />}
        </div>

        {/* Info Section */}
        <div className="mt-12 bg-blue-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">How it works</h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm text-blue-800">
            <div>
              <h4 className="font-medium mb-2">Single Address Checker</h4>
              <ul className="space-y-1">
                <li>• Enter a single Solana wallet address</li>
                <li>• Get instant WCT airdrop eligibility status</li>
                <li>• See eligible WCT token amount</li>
                <li>• Perfect for quick checks</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Bulk Checker</h4>
              <ul className="space-y-1">
                <li>• Check up to 100 addresses at once</li>
                <li>• Upload text file or paste addresses</li>
                <li>• See all eligible amounts in one table</li>
                <li>• Export results to CSV</li>
                <li>• Progress tracking for large batches</li>
              </ul>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500 text-sm">
            <p>Built for checking WalletConnect Token (WCT) airdrop eligibility</p>
            <p className="mt-2">Using WalletConnect RPC API for reliable data</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
