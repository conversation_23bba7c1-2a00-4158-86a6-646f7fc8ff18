{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/font/get-dev-overlay-font-middleware.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport path from 'path'\nimport * as fs from 'fs/promises'\nimport { constants } from 'fs'\nimport * as Log from '../../../../build/output/log'\nimport { middlewareResponse } from '../server/middleware-response'\n\nconst FONT_PREFIX = '/__nextjs_font/'\n\nconst VALID_FONTS = [\n  'geist-latin-ext.woff2',\n  'geist-mono-latin-ext.woff2',\n  'geist-latin.woff2',\n  'geist-mono-latin.woff2',\n]\n\nconst FONT_HEADERS = {\n  'Content-Type': 'font/woff2',\n  'Cache-Control': 'public, max-age=31536000, immutable',\n} as const\n\nexport function getDevOverlayFontMiddleware() {\n  return async function devOverlayFontMiddleware(\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    try {\n      const { pathname } = new URL(`http://n${req.url}`)\n\n      if (!pathname.startsWith(FONT_PREFIX)) {\n        return next()\n      }\n\n      const fontFile = pathname.replace(FONT_PREFIX, '')\n      if (!VALID_FONTS.includes(fontFile)) {\n        return middlewareResponse.notFound(res)\n      }\n\n      const fontPath = path.resolve(__dirname, fontFile)\n      const fileExists = await checkFileExists(fontPath)\n\n      if (!fileExists) {\n        return middlewareResponse.notFound(res)\n      }\n\n      const fontData = await fs.readFile(fontPath)\n      Object.entries(FONT_HEADERS).forEach(([key, value]) => {\n        res.setHeader(key, value)\n      })\n      res.end(fontData)\n    } catch (err) {\n      Log.error(\n        'Failed to serve font:',\n        err instanceof Error ? err.message : err\n      )\n      return middlewareResponse.internalServerError(res)\n    }\n  }\n}\n\nasync function checkFileExists(filePath: string): Promise<boolean> {\n  try {\n    await fs.access(filePath, constants.F_OK)\n    return true\n  } catch {\n    return false\n  }\n}\n"], "names": ["getDevOverlayFontMiddleware", "FONT_PREFIX", "VALID_FONTS", "FONT_HEADERS", "devOverlayFontMiddleware", "req", "res", "next", "pathname", "URL", "url", "startsWith", "fontFile", "replace", "includes", "middlewareResponse", "notFound", "fontPath", "path", "resolve", "__dirname", "fileExists", "checkFileExists", "fontData", "fs", "readFile", "Object", "entries", "for<PERSON>ach", "key", "value", "<PERSON><PERSON><PERSON><PERSON>", "end", "err", "Log", "error", "Error", "message", "internalServerError", "filePath", "access", "constants", "F_OK"], "mappings": ";;;;+BAqBgBA;;;eAAAA;;;;;+DAp<PERSON>;oEACG;oBACM;+DACL;oCACc;AAEnC,MAAMC,cAAc;AAEpB,MAAMC,cAAc;IAClB;IACA;IACA;IACA;CACD;AAED,MAAMC,eAAe;IACnB,gBAAgB;IAChB,iBAAiB;AACnB;AAEO,SAASH;IACd,OAAO,eAAeI,yBACpBC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,IAAI;YACF,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAI,AAAC,aAAUJ,IAAIK,GAAG;YAE/C,IAAI,CAACF,SAASG,UAAU,CAACV,cAAc;gBACrC,OAAOM;YACT;YAEA,MAAMK,WAAWJ,SAASK,OAAO,CAACZ,aAAa;YAC/C,IAAI,CAACC,YAAYY,QAAQ,CAACF,WAAW;gBACnC,OAAOG,sCAAkB,CAACC,QAAQ,CAACV;YACrC;YAEA,MAAMW,WAAWC,aAAI,CAACC,OAAO,CAACC,WAAWR;YACzC,MAAMS,aAAa,MAAMC,gBAAgBL;YAEzC,IAAI,CAACI,YAAY;gBACf,OAAON,sCAAkB,CAACC,QAAQ,CAACV;YACrC;YAEA,MAAMiB,WAAW,MAAMC,UAAGC,QAAQ,CAACR;YACnCS,OAAOC,OAAO,CAACxB,cAAcyB,OAAO,CAAC;oBAAC,CAACC,KAAKC,MAAM;gBAChDxB,IAAIyB,SAAS,CAACF,KAAKC;YACrB;YACAxB,IAAI0B,GAAG,CAACT;QACV,EAAE,OAAOU,KAAK;YACZC,KAAIC,KAAK,CACP,yBACAF,eAAeG,QAAQH,IAAII,OAAO,GAAGJ;YAEvC,OAAOlB,sCAAkB,CAACuB,mBAAmB,CAAChC;QAChD;IACF;AACF;AAEA,eAAegB,gBAAgBiB,QAAgB;IAC7C,IAAI;QACF,MAAMf,UAAGgB,MAAM,CAACD,UAAUE,aAAS,CAACC,IAAI;QACxC,OAAO;IACT,EAAE,UAAM;QACN,OAAO;IACT;AACF"}