{"version": 3, "sources": ["../../../src/server/app-render/has-loading-component-in-tree.tsx"], "sourcesContent": ["import type { LoaderTree } from '../lib/app-dir-module'\n\nexport function hasLoadingComponentInTree(tree: LoaderTree): boolean {\n  const [, parallelRoutes, { loading }] = tree\n\n  if (loading) {\n    return true\n  }\n\n  return Object.values(parallelRoutes).some((parallelRoute) =>\n    hasLoadingComponentInTree(parallelRoute)\n  ) as boolean\n}\n"], "names": ["hasLoadingComponentInTree", "tree", "parallelRoutes", "loading", "Object", "values", "some", "parallelRoute"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,0BAA0BC,IAAgB;IACxD,MAAM,GAAGC,gBAAgB,EAAEC,OAAO,EAAE,CAAC,GAAGF;IAExC,IAAIE,SAAS;QACX,OAAO;IACT;IAEA,OAAOC,OAAOC,MAAM,CAACH,gBAAgBI,IAAI,CAAC,CAACC,gBACzCP,0BAA0BO;AAE9B"}