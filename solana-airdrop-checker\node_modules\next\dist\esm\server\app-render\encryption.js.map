{"version": 3, "sources": ["../../../src/server/app-render/encryption.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport 'server-only'\n\n/* eslint-disable import/no-extraneous-dependencies */\nimport { renderToReadableStream } from 'react-server-dom-webpack/server.edge'\n/* eslint-disable import/no-extraneous-dependencies */\nimport { createFromReadableStream } from 'react-server-dom-webpack/client.edge'\n\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport {\n  arrayBufferToString,\n  decrypt,\n  encrypt,\n  getActionEncryptionKey,\n  getClientReferenceManifestForRsc,\n  getServerModuleMap,\n  stringToUint8Array,\n} from './encryption-utils'\nimport {\n  getPrerenderResumeDataCache,\n  getRenderResumeDataCache,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { createHangingInputAbortSignal } from './dynamic-rendering'\nimport React from 'react'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst textEncoder = new TextEncoder()\nconst textDecoder = new TextDecoder()\n\n/**\n * Decrypt the serialized string with the action id as the salt.\n */\nasync function decodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (typeof key === 'undefined') {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get the iv (16 bytes) and the payload from the arg.\n  const originalPayload = atob(arg)\n  const ivValue = originalPayload.slice(0, 16)\n  const payload = originalPayload.slice(16)\n\n  const decrypted = textDecoder.decode(\n    await decrypt(key, stringToUint8Array(ivValue), stringToUint8Array(payload))\n  )\n\n  if (!decrypted.startsWith(actionId)) {\n    throw new Error('Invalid Server Action payload: failed to decrypt.')\n  }\n\n  return decrypted.slice(actionId.length)\n}\n\n/**\n * Encrypt the serialized string with the action id as the salt. Add a prefix to\n * later ensure that the payload is correctly decrypted, similar to a checksum.\n */\nasync function encodeActionBoundArg(actionId: string, arg: string) {\n  const key = await getActionEncryptionKey()\n  if (key === undefined) {\n    throw new Error(\n      `Missing encryption key for Server Action. This is a bug in Next.js`\n    )\n  }\n\n  // Get 16 random bytes as iv.\n  const randomBytes = new Uint8Array(16)\n  workUnitAsyncStorage.exit(() => crypto.getRandomValues(randomBytes))\n  const ivValue = arrayBufferToString(randomBytes.buffer)\n\n  const encrypted = await encrypt(\n    key,\n    randomBytes,\n    textEncoder.encode(actionId + arg)\n  )\n\n  return btoa(ivValue + arrayBufferToString(encrypted))\n}\n\n// Encrypts the action's bound args into a string. For the same combination of\n// actionId and args the same cached promise is returned. This ensures reference\n// equality for returned objects from \"use cache\" functions when they're invoked\n// multiple times within one render pass using the same bound args.\nexport const encryptActionBoundArgs = React.cache(\n  async function encryptActionBoundArgs(actionId: string, ...args: any[]) {\n    const { clientModules } = getClientReferenceManifestForRsc()\n\n    // Create an error before any asynchronous calls, to capture the original\n    // call stack in case we need it when the serialization errors.\n    const error = new Error()\n    Error.captureStackTrace(error, encryptActionBoundArgs)\n\n    let didCatchError = false\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    const hangingInputAbortSignal =\n      workUnitStore?.type === 'prerender'\n        ? createHangingInputAbortSignal(workUnitStore)\n        : undefined\n\n    // Using Flight to serialize the args into a string.\n    const serialized = await streamToString(\n      renderToReadableStream(args, clientModules, {\n        signal: hangingInputAbortSignal,\n        onError(err) {\n          if (hangingInputAbortSignal?.aborted) {\n            return\n          }\n\n          // We're only reporting one error at a time, starting with the first.\n          if (didCatchError) {\n            return\n          }\n\n          didCatchError = true\n\n          // Use the original error message together with the previously created\n          // stack, because err.stack is a useless Flight Server call stack.\n          error.message = err instanceof Error ? err.message : String(err)\n        },\n      }),\n      // We pass the abort signal to `streamToString` so that no chunks are\n      // included that are emitted after the signal was already aborted. This\n      // ensures that we can encode hanging promises.\n      hangingInputAbortSignal\n    )\n\n    if (didCatchError) {\n      if (process.env.NODE_ENV === 'development') {\n        // Logging the error is needed for server functions that are passed to the\n        // client where the decryption is not done during rendering. Console\n        // replaying allows us to still show the error dev overlay in this case.\n        console.error(error)\n      }\n\n      throw error\n    }\n\n    if (!workUnitStore) {\n      return encodeActionBoundArg(actionId, serialized)\n    }\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n    const cacheKey = actionId + serialized\n\n    const cachedEncrypted =\n      prerenderResumeDataCache?.encryptedBoundArgs.get(cacheKey) ??\n      renderResumeDataCache?.encryptedBoundArgs.get(cacheKey)\n\n    if (cachedEncrypted) {\n      return cachedEncrypted\n    }\n\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    cacheSignal?.beginRead()\n\n    const encrypted = await encodeActionBoundArg(actionId, serialized)\n\n    cacheSignal?.endRead()\n    prerenderResumeDataCache?.encryptedBoundArgs.set(cacheKey, encrypted)\n\n    return encrypted\n  }\n)\n\n// Decrypts the action's bound args from the encrypted string.\nexport async function decryptActionBoundArgs(\n  actionId: string,\n  encryptedPromise: Promise<string>\n) {\n  const encrypted = await encryptedPromise\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  let decrypted: string | undefined\n\n  if (workUnitStore) {\n    const cacheSignal =\n      workUnitStore.type === 'prerender' ? workUnitStore.cacheSignal : undefined\n\n    const prerenderResumeDataCache = getPrerenderResumeDataCache(workUnitStore)\n    const renderResumeDataCache = getRenderResumeDataCache(workUnitStore)\n\n    decrypted =\n      prerenderResumeDataCache?.decryptedBoundArgs.get(encrypted) ??\n      renderResumeDataCache?.decryptedBoundArgs.get(encrypted)\n\n    if (!decrypted) {\n      cacheSignal?.beginRead()\n      decrypted = await decodeActionBoundArg(actionId, encrypted)\n      cacheSignal?.endRead()\n      prerenderResumeDataCache?.decryptedBoundArgs.set(encrypted, decrypted)\n    }\n  } else {\n    decrypted = await decodeActionBoundArg(actionId, encrypted)\n  }\n\n  const { edgeRscModuleMapping, rscModuleMapping } =\n    getClientReferenceManifestForRsc()\n\n  // Using Flight to deserialize the args from the string.\n  const deserialized = await createFromReadableStream(\n    new ReadableStream({\n      start(controller) {\n        controller.enqueue(textEncoder.encode(decrypted))\n\n        if (workUnitStore?.type === 'prerender') {\n          // Explicitly don't close the stream here (until prerendering is\n          // complete) so that hanging promises are not rejected.\n          if (workUnitStore.renderSignal.aborted) {\n            controller.close()\n          } else {\n            workUnitStore.renderSignal.addEventListener(\n              'abort',\n              () => controller.close(),\n              { once: true }\n            )\n          }\n        } else {\n          controller.close()\n        }\n      },\n    }),\n    {\n      serverConsumerManifest: {\n        // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n        // to be added to the current execution. Instead, we'll wait for any ClientReference\n        // to be emitted which themselves will handle the preloading.\n        moduleLoading: null,\n        moduleMap: isEdgeRuntime ? edgeRscModuleMapping : rscModuleMapping,\n        serverModuleMap: getServerModuleMap(),\n      },\n    }\n  )\n\n  return deserialized\n}\n"], "names": ["renderToReadableStream", "createFromReadableStream", "streamToString", "arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "stringToUint8Array", "getPrerenderResumeDataCache", "getRenderResumeDataCache", "workUnitAsyncStorage", "createHangingInputAbortSignal", "React", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "textEncoder", "TextEncoder", "textDecoder", "TextDecoder", "decodeActionBoundArg", "actionId", "arg", "key", "Error", "originalPayload", "atob", "ivValue", "slice", "payload", "decrypted", "decode", "startsWith", "length", "encodeActionBoundArg", "undefined", "randomBytes", "Uint8Array", "exit", "crypto", "getRandomValues", "buffer", "encrypted", "encode", "btoa", "encryptActionBoundArgs", "cache", "args", "clientModules", "error", "captureStackTrace", "didCatchError", "workUnitStore", "getStore", "hangingInputAbortSignal", "type", "serialized", "signal", "onError", "err", "aborted", "message", "String", "NODE_ENV", "console", "prerenderResumeDataCache", "renderResumeDataCache", "cache<PERSON>ey", "cachedEncrypted", "encryptedBoundArgs", "get", "cacheSignal", "beginRead", "endRead", "set", "decryptActionBoundArgs", "encryptedPromise", "decryptedBoundArgs", "edgeRscModuleMapping", "rscModuleMapping", "deserialized", "ReadableStream", "start", "controller", "enqueue", "renderSignal", "close", "addEventListener", "once", "serverConsumerManifest", "moduleLoading", "moduleMap", "serverModuleMap"], "mappings": "AAAA,oDAAoD,GACpD,OAAO,cAAa;AAEpB,oDAAoD,GACpD,SAASA,sBAAsB,QAAQ,uCAAsC;AAC7E,oDAAoD,GACpD,SAASC,wBAAwB,QAAQ,uCAAsC;AAE/E,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SACEC,mBAAmB,EACnBC,OAAO,EACPC,OAAO,EACPC,sBAAsB,EACtBC,gCAAgC,EAChCC,kBAAkB,EAClBC,kBAAkB,QACb,qBAAoB;AAC3B,SACEC,2BAA2B,EAC3BC,wBAAwB,EACxBC,oBAAoB,QACf,qCAAoC;AAC3C,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,OAAOC,WAAW,QAAO;AAEzB,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,cAAc,IAAIC;AACxB,MAAMC,cAAc,IAAIC;AAExB;;CAEC,GACD,eAAeC,qBAAqBC,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMpB;IAClB,IAAI,OAAOoB,QAAQ,aAAa;QAC9B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,sDAAsD;IACtD,MAAMC,kBAAkBC,KAAKJ;IAC7B,MAAMK,UAAUF,gBAAgBG,KAAK,CAAC,GAAG;IACzC,MAAMC,UAAUJ,gBAAgBG,KAAK,CAAC;IAEtC,MAAME,YAAYZ,YAAYa,MAAM,CAClC,MAAM9B,QAAQsB,KAAKjB,mBAAmBqB,UAAUrB,mBAAmBuB;IAGrE,IAAI,CAACC,UAAUE,UAAU,CAACX,WAAW;QACnC,MAAM,qBAA8D,CAA9D,IAAIG,MAAM,sDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA6D;IACrE;IAEA,OAAOM,UAAUF,KAAK,CAACP,SAASY,MAAM;AACxC;AAEA;;;CAGC,GACD,eAAeC,qBAAqBb,QAAgB,EAAEC,GAAW;IAC/D,MAAMC,MAAM,MAAMpB;IAClB,IAAIoB,QAAQY,WAAW;QACrB,MAAM,qBAEL,CAFK,IAAIX,MACR,CAAC,kEAAkE,CAAC,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,6BAA6B;IAC7B,MAAMY,cAAc,IAAIC,WAAW;IACnC5B,qBAAqB6B,IAAI,CAAC,IAAMC,OAAOC,eAAe,CAACJ;IACvD,MAAMT,UAAU3B,oBAAoBoC,YAAYK,MAAM;IAEtD,MAAMC,YAAY,MAAMxC,QACtBqB,KACAa,aACApB,YAAY2B,MAAM,CAACtB,WAAWC;IAGhC,OAAOsB,KAAKjB,UAAU3B,oBAAoB0C;AAC5C;AAEA,8EAA8E;AAC9E,gFAAgF;AAChF,gFAAgF;AAChF,mEAAmE;AACnE,OAAO,MAAMG,yBAAyBlC,MAAMmC,KAAK,CAC/C,eAAeD,uBAAuBxB,QAAgB,EAAE,GAAG0B,IAAW;IACpE,MAAM,EAAEC,aAAa,EAAE,GAAG5C;IAE1B,yEAAyE;IACzE,+DAA+D;IAC/D,MAAM6C,QAAQ,IAAIzB;IAClBA,MAAM0B,iBAAiB,CAACD,OAAOJ;IAE/B,IAAIM,gBAAgB;IAEpB,MAAMC,gBAAgB3C,qBAAqB4C,QAAQ;IAEnD,MAAMC,0BACJF,CAAAA,iCAAAA,cAAeG,IAAI,MAAK,cACpB7C,8BAA8B0C,iBAC9BjB;IAEN,oDAAoD;IACpD,MAAMqB,aAAa,MAAMzD,eACvBF,uBAAuBkD,MAAMC,eAAe;QAC1CS,QAAQH;QACRI,SAAQC,GAAG;YACT,IAAIL,2CAAAA,wBAAyBM,OAAO,EAAE;gBACpC;YACF;YAEA,qEAAqE;YACrE,IAAIT,eAAe;gBACjB;YACF;YAEAA,gBAAgB;YAEhB,sEAAsE;YACtE,kEAAkE;YAClEF,MAAMY,OAAO,GAAGF,eAAenC,QAAQmC,IAAIE,OAAO,GAAGC,OAAOH;QAC9D;IACF,IACA,qEAAqE;IACrE,uEAAuE;IACvE,+CAA+C;IAC/CL;IAGF,IAAIH,eAAe;QACjB,IAAItC,QAAQC,GAAG,CAACiD,QAAQ,KAAK,eAAe;YAC1C,0EAA0E;YAC1E,oEAAoE;YACpE,wEAAwE;YACxEC,QAAQf,KAAK,CAACA;QAChB;QAEA,MAAMA;IACR;IAEA,IAAI,CAACG,eAAe;QAClB,OAAOlB,qBAAqBb,UAAUmC;IACxC;IAEA,MAAMS,2BAA2B1D,4BAA4B6C;IAC7D,MAAMc,wBAAwB1D,yBAAyB4C;IACvD,MAAMe,WAAW9C,WAAWmC;IAE5B,MAAMY,kBACJH,CAAAA,4CAAAA,yBAA0BI,kBAAkB,CAACC,GAAG,CAACH,eACjDD,yCAAAA,sBAAuBG,kBAAkB,CAACC,GAAG,CAACH;IAEhD,IAAIC,iBAAiB;QACnB,OAAOA;IACT;IAEA,MAAMG,cACJnB,cAAcG,IAAI,KAAK,cAAcH,cAAcmB,WAAW,GAAGpC;IAEnEoC,+BAAAA,YAAaC,SAAS;IAEtB,MAAM9B,YAAY,MAAMR,qBAAqBb,UAAUmC;IAEvDe,+BAAAA,YAAaE,OAAO;IACpBR,4CAAAA,yBAA0BI,kBAAkB,CAACK,GAAG,CAACP,UAAUzB;IAE3D,OAAOA;AACT,GACD;AAED,8DAA8D;AAC9D,OAAO,eAAeiC,uBACpBtD,QAAgB,EAChBuD,gBAAiC;IAEjC,MAAMlC,YAAY,MAAMkC;IACxB,MAAMxB,gBAAgB3C,qBAAqB4C,QAAQ;IAEnD,IAAIvB;IAEJ,IAAIsB,eAAe;QACjB,MAAMmB,cACJnB,cAAcG,IAAI,KAAK,cAAcH,cAAcmB,WAAW,GAAGpC;QAEnE,MAAM8B,2BAA2B1D,4BAA4B6C;QAC7D,MAAMc,wBAAwB1D,yBAAyB4C;QAEvDtB,YACEmC,CAAAA,4CAAAA,yBAA0BY,kBAAkB,CAACP,GAAG,CAAC5B,gBACjDwB,yCAAAA,sBAAuBW,kBAAkB,CAACP,GAAG,CAAC5B;QAEhD,IAAI,CAACZ,WAAW;YACdyC,+BAAAA,YAAaC,SAAS;YACtB1C,YAAY,MAAMV,qBAAqBC,UAAUqB;YACjD6B,+BAAAA,YAAaE,OAAO;YACpBR,4CAAAA,yBAA0BY,kBAAkB,CAACH,GAAG,CAAChC,WAAWZ;QAC9D;IACF,OAAO;QACLA,YAAY,MAAMV,qBAAqBC,UAAUqB;IACnD;IAEA,MAAM,EAAEoC,oBAAoB,EAAEC,gBAAgB,EAAE,GAC9C3E;IAEF,wDAAwD;IACxD,MAAM4E,eAAe,MAAMlF,yBACzB,IAAImF,eAAe;QACjBC,OAAMC,UAAU;YACdA,WAAWC,OAAO,CAACpE,YAAY2B,MAAM,CAACb;YAEtC,IAAIsB,CAAAA,iCAAAA,cAAeG,IAAI,MAAK,aAAa;gBACvC,gEAAgE;gBAChE,uDAAuD;gBACvD,IAAIH,cAAciC,YAAY,CAACzB,OAAO,EAAE;oBACtCuB,WAAWG,KAAK;gBAClB,OAAO;oBACLlC,cAAciC,YAAY,CAACE,gBAAgB,CACzC,SACA,IAAMJ,WAAWG,KAAK,IACtB;wBAAEE,MAAM;oBAAK;gBAEjB;YACF,OAAO;gBACLL,WAAWG,KAAK;YAClB;QACF;IACF,IACA;QACEG,wBAAwB;YACtB,2FAA2F;YAC3F,oFAAoF;YACpF,6DAA6D;YAC7DC,eAAe;YACfC,WAAW/E,gBAAgBkE,uBAAuBC;YAClDa,iBAAiBvF;QACnB;IACF;IAGF,OAAO2E;AACT"}