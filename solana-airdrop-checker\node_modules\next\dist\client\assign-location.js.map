{"version": 3, "sources": ["../../src/client/assign-location.ts"], "sourcesContent": ["import { addBasePath } from './add-base-path'\n\n/**\n * Function to correctly assign location to URL\n *\n * The method will add basePath, and will also correctly add location (including if it is a relative path)\n * @param location Location that should be added to the url\n * @param url Base URL to which the location should be assigned\n */\nexport function assignLocation(location: string, url: URL): URL {\n  if (location.startsWith('.')) {\n    const urlBase = url.origin + url.pathname\n    return new URL(\n      // In order for a relative path to be added to the current url correctly, the current url must end with a slash\n      // new URL('./relative', 'https://example.com/subdir').href -> 'https://example.com/relative'\n      // new URL('./relative', 'https://example.com/subdir/').href -> 'https://example.com/subdir/relative'\n      (urlBase.endsWith('/') ? urlBase : urlBase + '/') + location\n    )\n  }\n\n  return new URL(addBasePath(location), url.href)\n}\n"], "names": ["assignLocation", "location", "url", "startsWith", "urlBase", "origin", "pathname", "URL", "endsWith", "addBasePath", "href"], "mappings": ";;;;+BASgBA;;;eAAAA;;;6BATY;AASrB,SAASA,eAAeC,QAAgB,EAAEC,GAAQ;IACvD,IAAID,SAASE,UAAU,CAAC,MAAM;QAC5B,MAAMC,UAAUF,IAAIG,MAAM,GAAGH,IAAII,QAAQ;QACzC,OAAO,IAAIC,IAIT,AAHA,+GAA+G;QAC/G,6FAA6F;QAC7F,qGAAqG;QACpGH,CAAAA,QAAQI,QAAQ,CAAC,OAAOJ,UAAUA,UAAU,GAAE,IAAKH;IAExD;IAEA,OAAO,IAAIM,IAAIE,IAAAA,wBAAW,EAACR,WAAWC,IAAIQ,IAAI;AAChD"}