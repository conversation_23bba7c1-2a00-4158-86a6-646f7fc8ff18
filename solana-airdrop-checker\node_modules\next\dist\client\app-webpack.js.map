{"version": 3, "sources": ["../../src/client/app-webpack.ts"], "sourcesContent": ["// Override chunk URL mapping in the webpack runtime\n// https://github.com/webpack/webpack/blob/2738eebc7880835d88c727d364ad37f3ec557593/lib/RuntimeGlobals.js#L204\n\nimport { getDeploymentIdQueryOrEmptyString } from '../build/deployment-id'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\n\ndeclare const __webpack_require__: any\n\n// If we have a deployment ID, we need to append it to the webpack chunk names\n// I am keeping the process check explicit so this can be statically optimized\nif (process.env.NEXT_DEPLOYMENT_ID) {\n  const suffix = getDeploymentIdQueryOrEmptyString()\n  // eslint-disable-next-line no-undef\n  const getChunkScriptFilename = __webpack_require__.u\n  // eslint-disable-next-line no-undef\n  __webpack_require__.u = (...args: any[]) =>\n    // We encode the chunk filename because our static server matches against and encoded\n    // filename path.\n    encodeURIPath(getChunkScriptFilename(...args)) + suffix\n\n  // eslint-disable-next-line no-undef\n  const getChunkCssFilename = __webpack_require__.k\n  // eslint-disable-next-line no-undef\n  __webpack_require__.k = (...args: any[]) =>\n    getChunkCssFilename(...args) + suffix\n\n  // eslint-disable-next-line no-undef\n  const getMiniCssFilename = __webpack_require__.miniCssF\n  // eslint-disable-next-line no-undef\n  __webpack_require__.miniCssF = (...args: any[]) =>\n    getMiniCssFilename(...args) + suffix\n} else {\n  // eslint-disable-next-line no-undef\n  const getChunkScriptFilename = __webpack_require__.u\n  // eslint-disable-next-line no-undef\n  __webpack_require__.u = (...args: any[]) =>\n    // We encode the chunk filename because our static server matches against and encoded\n    // filename path.\n    encodeURIPath(getChunkScriptFilename(...args))\n\n  // We don't need to override __webpack_require__.k because we don't modify\n  // the css chunk name when not using deployment id suffixes\n\n  // WE don't need to override __webpack_require__.miniCssF because we don't modify\n  // the mini css chunk name when not using deployment id suffixes\n}\n\nexport {}\n"], "names": ["process", "env", "NEXT_DEPLOYMENT_ID", "suffix", "getDeploymentIdQueryOrEmptyString", "getChunkScriptFilename", "__webpack_require__", "u", "args", "encodeURIPath", "getChunkCssFilename", "k", "getMiniCssFilename", "miniCssF"], "mappings": "AAAA,oDAAoD;AACpD,8GAA8G;;;;;8BAE5D;+BACpB;AAI9B,8EAA8E;AAC9E,8EAA8E;AAC9E,IAAIA,QAAQC,GAAG,CAACC,kBAAkB,EAAE;IAClC,MAAMC,SAASC,IAAAA,+CAAiC;IAChD,oCAAoC;IACpC,MAAMC,yBAAyBC,oBAAoBC,CAAC;IACpD,oCAAoC;IACpCD,oBAAoBC,CAAC,GAAG;yCAAIC;YAAAA;;eAC1B,qFAAqF;QACrF,iBAAiB;QACjBC,IAAAA,4BAAa,EAACJ,0BAA0BG,SAASL;;IAEnD,oCAAoC;IACpC,MAAMO,sBAAsBJ,oBAAoBK,CAAC;IACjD,oCAAoC;IACpCL,oBAAoBK,CAAC,GAAG;yCAAIH;YAAAA;;eAC1BE,uBAAuBF,QAAQL;;IAEjC,oCAAoC;IACpC,MAAMS,qBAAqBN,oBAAoBO,QAAQ;IACvD,oCAAoC;IACpCP,oBAAoBO,QAAQ,GAAG;yCAAIL;YAAAA;;eACjCI,sBAAsBJ,QAAQL;;AAClC,OAAO;IACL,oCAAoC;IACpC,MAAME,yBAAyBC,oBAAoBC,CAAC;IACpD,oCAAoC;IACpCD,oBAAoBC,CAAC,GAAG;yCAAIC;YAAAA;;eAC1B,qFAAqF;QACrF,iBAAiB;QACjBC,IAAAA,4BAAa,EAACJ,0BAA0BG;;AAE1C,0EAA0E;AAC1E,2DAA2D;AAE3D,iFAAiF;AACjF,gEAAgE;AAClE"}