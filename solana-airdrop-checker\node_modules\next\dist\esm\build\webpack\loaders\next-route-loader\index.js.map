{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-route-loader/index.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport type { MiddlewareConfig } from '../../../analysis/get-page-static-info'\n\nimport { stringify } from 'querystring'\nimport {\n  type ModuleBuildInfo,\n  getModuleBuildInfo,\n} from '../get-module-build-info'\nimport { RouteKind } from '../../../../server/route-kind'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\nimport { decodeFromBase64, encodeToBase64 } from '../utils'\nimport { isInstrumentationHookFile } from '../../../utils'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { MappedPages } from '../../../build-context'\n\ntype RouteLoaderOptionsPagesAPIInput = {\n  kind: RouteKind.PAGES_API\n  page: string\n  preferredRegion: string | string[] | undefined\n  absolutePagePath: string\n  middlewareConfig: MiddlewareConfig\n}\n\ntype RouteLoaderOptionsPagesInput = {\n  kind: RouteKind.PAGES\n  page: string\n  pages: MappedPages\n  preferredRegion: string | string[] | undefined\n  absolutePagePath: string\n  middlewareConfig: MiddlewareConfig\n}\n\ntype RouteLoaderOptionsInput =\n  | RouteLoaderOptionsPagesInput\n  | RouteLoaderOptionsPagesAPIInput\n\ntype RouteLoaderPagesAPIOptions = {\n  kind: RouteKind.PAGES_API\n\n  /**\n   * The page name for this particular route.\n   */\n  page: string\n\n  /**\n   * The preferred region for this route.\n   */\n  preferredRegion: string | string[] | undefined\n\n  /**\n   * The absolute path to the userland page file.\n   */\n  absolutePagePath: string\n\n  /**\n   * The middleware config for this route.\n   */\n  middlewareConfigBase64: string\n}\n\ntype RouteLoaderPagesOptions = {\n  kind: RouteKind.PAGES\n\n  /**\n   * The page name for this particular route.\n   */\n  page: string\n\n  /**\n   * The preferred region for this route.\n   */\n  preferredRegion: string | string[] | undefined\n\n  /**\n   * The absolute path to the userland page file.\n   */\n  absolutePagePath: string\n\n  /**\n   * The absolute paths to the app path file.\n   */\n  absoluteAppPath: string\n\n  /**\n   * The absolute paths to the document path file.\n   */\n  absoluteDocumentPath: string\n\n  /**\n   * The middleware config for this route.\n   */\n  middlewareConfigBase64: string\n}\n\n/**\n * The options for the route loader.\n */\ntype RouteLoaderOptions = RouteLoaderPagesOptions | RouteLoaderPagesAPIOptions\n\n/**\n * Returns the loader entry for a given page.\n *\n * @param options the options to create the loader entry\n * @returns the encoded loader entry\n */\nexport function getRouteLoaderEntry(options: RouteLoaderOptionsInput): string {\n  switch (options.kind) {\n    case RouteKind.PAGES: {\n      const query: RouteLoaderPagesOptions = {\n        kind: options.kind,\n        page: options.page,\n        preferredRegion: options.preferredRegion,\n        absolutePagePath: options.absolutePagePath,\n        // These are the path references to the internal components that may be\n        // overridden by userland components.\n        absoluteAppPath: options.pages['/_app'],\n        absoluteDocumentPath: options.pages['/_document'],\n        middlewareConfigBase64: encodeToBase64(options.middlewareConfig),\n      }\n\n      return `next-route-loader?${stringify(query)}!`\n    }\n    case RouteKind.PAGES_API: {\n      const query: RouteLoaderPagesAPIOptions = {\n        kind: options.kind,\n        page: options.page,\n        preferredRegion: options.preferredRegion,\n        absolutePagePath: options.absolutePagePath,\n        middlewareConfigBase64: encodeToBase64(options.middlewareConfig),\n      }\n\n      return `next-route-loader?${stringify(query)}!`\n    }\n    default: {\n      throw new Error('Invariant: Unexpected route kind')\n    }\n  }\n}\n\nconst loadPages = async (\n  {\n    page,\n    absolutePagePath,\n    absoluteDocumentPath,\n    absoluteAppPath,\n    preferredRegion,\n    middlewareConfigBase64,\n  }: RouteLoaderPagesOptions,\n  buildInfo: ModuleBuildInfo\n) => {\n  const middlewareConfig: MiddlewareConfig = decodeFromBase64(\n    middlewareConfigBase64\n  )\n\n  // Attach build info to the module.\n  buildInfo.route = {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfig,\n  }\n\n  let file = await loadEntrypoint('pages', {\n    VAR_USERLAND: absolutePagePath,\n    VAR_MODULE_DOCUMENT: absoluteDocumentPath,\n    VAR_MODULE_APP: absoluteAppPath,\n    VAR_DEFINITION_PAGE: normalizePagePath(page),\n    VAR_DEFINITION_PATHNAME: page,\n  })\n\n  if (isInstrumentationHookFile(page)) {\n    // When we're building the instrumentation page (only when the\n    // instrumentation file conflicts with a page also labeled\n    // /instrumentation) hoist the `register` method.\n    file += '\\nexport const register = hoist(userland, \"register\")'\n  }\n\n  return file\n}\n\nconst loadPagesAPI = async (\n  {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfigBase64,\n  }: RouteLoaderPagesAPIOptions,\n  buildInfo: ModuleBuildInfo\n) => {\n  const middlewareConfig: MiddlewareConfig = decodeFromBase64(\n    middlewareConfigBase64\n  )\n\n  // Attach build info to the module.\n  buildInfo.route = {\n    page,\n    absolutePagePath,\n    preferredRegion,\n    middlewareConfig,\n  }\n\n  return await loadEntrypoint('pages-api', {\n    VAR_USERLAND: absolutePagePath,\n    VAR_DEFINITION_PAGE: normalizePagePath(page),\n    VAR_DEFINITION_PATHNAME: page,\n  })\n}\n\n/**\n * Handles the `next-route-loader` options.\n * @returns the loader definition function\n */\nconst loader: webpack.LoaderDefinitionFunction<RouteLoaderOptions> =\n  async function () {\n    if (!this._module) {\n      throw new Error('Invariant: expected this to reference a module')\n    }\n\n    const buildInfo = getModuleBuildInfo(this._module)\n    const opts = this.getOptions()\n\n    switch (opts.kind) {\n      case RouteKind.PAGES: {\n        return await loadPages(opts, buildInfo)\n      }\n      case RouteKind.PAGES_API: {\n        return await loadPagesAPI(opts, buildInfo)\n      }\n      default: {\n        throw new Error('Invariant: Unexpected route kind')\n      }\n    }\n  }\n\nexport default loader\n"], "names": ["stringify", "getModuleBuildInfo", "RouteKind", "normalizePagePath", "decodeFromBase64", "encodeToBase64", "isInstrumentationHookFile", "loadEntrypoint", "getRouteLoaderEntry", "options", "kind", "PAGES", "query", "page", "preferredRegion", "absolutePagePath", "absoluteAppPath", "pages", "absoluteDocumentPath", "middlewareConfigBase64", "middlewareConfig", "PAGES_API", "Error", "loadPages", "buildInfo", "route", "file", "VAR_USERLAND", "VAR_MODULE_DOCUMENT", "VAR_MODULE_APP", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "loadPagesAPI", "loader", "_module", "opts", "getOptions"], "mappings": "AAGA,SAASA,SAAS,QAAQ,cAAa;AACvC,SAEEC,kBAAkB,QACb,2BAA0B;AACjC,SAASC,SAAS,QAAQ,gCAA+B;AACzD,SAASC,iBAAiB,QAAQ,uDAAsD;AACxF,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,WAAU;AAC3D,SAASC,yBAAyB,QAAQ,iBAAgB;AAC1D,SAASC,cAAc,QAAQ,2BAA0B;AAuFzD;;;;;CAKC,GACD,OAAO,SAASC,oBAAoBC,OAAgC;IAClE,OAAQA,QAAQC,IAAI;QAClB,KAAKR,UAAUS,KAAK;YAAE;gBACpB,MAAMC,QAAiC;oBACrCF,MAAMD,QAAQC,IAAI;oBAClBG,MAAMJ,QAAQI,IAAI;oBAClBC,iBAAiBL,QAAQK,eAAe;oBACxCC,kBAAkBN,QAAQM,gBAAgB;oBAC1C,uEAAuE;oBACvE,qCAAqC;oBACrCC,iBAAiBP,QAAQQ,KAAK,CAAC,QAAQ;oBACvCC,sBAAsBT,QAAQQ,KAAK,CAAC,aAAa;oBACjDE,wBAAwBd,eAAeI,QAAQW,gBAAgB;gBACjE;gBAEA,OAAO,CAAC,kBAAkB,EAAEpB,UAAUY,OAAO,CAAC,CAAC;YACjD;QACA,KAAKV,UAAUmB,SAAS;YAAE;gBACxB,MAAMT,QAAoC;oBACxCF,MAAMD,QAAQC,IAAI;oBAClBG,MAAMJ,QAAQI,IAAI;oBAClBC,iBAAiBL,QAAQK,eAAe;oBACxCC,kBAAkBN,QAAQM,gBAAgB;oBAC1CI,wBAAwBd,eAAeI,QAAQW,gBAAgB;gBACjE;gBAEA,OAAO,CAAC,kBAAkB,EAAEpB,UAAUY,OAAO,CAAC,CAAC;YACjD;QACA;YAAS;gBACP,MAAM,qBAA6C,CAA7C,IAAIU,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;IACF;AACF;AAEA,MAAMC,YAAY,OAChB,EACEV,IAAI,EACJE,gBAAgB,EAChBG,oBAAoB,EACpBF,eAAe,EACfF,eAAe,EACfK,sBAAsB,EACE,EAC1BK;IAEA,MAAMJ,mBAAqChB,iBACzCe;IAGF,mCAAmC;IACnCK,UAAUC,KAAK,GAAG;QAChBZ;QACAE;QACAD;QACAM;IACF;IAEA,IAAIM,OAAO,MAAMnB,eAAe,SAAS;QACvCoB,cAAcZ;QACda,qBAAqBV;QACrBW,gBAAgBb;QAChBc,qBAAqB3B,kBAAkBU;QACvCkB,yBAAyBlB;IAC3B;IAEA,IAAIP,0BAA0BO,OAAO;QACnC,8DAA8D;QAC9D,0DAA0D;QAC1D,iDAAiD;QACjDa,QAAQ;IACV;IAEA,OAAOA;AACT;AAEA,MAAMM,eAAe,OACnB,EACEnB,IAAI,EACJE,gBAAgB,EAChBD,eAAe,EACfK,sBAAsB,EACK,EAC7BK;IAEA,MAAMJ,mBAAqChB,iBACzCe;IAGF,mCAAmC;IACnCK,UAAUC,KAAK,GAAG;QAChBZ;QACAE;QACAD;QACAM;IACF;IAEA,OAAO,MAAMb,eAAe,aAAa;QACvCoB,cAAcZ;QACde,qBAAqB3B,kBAAkBU;QACvCkB,yBAAyBlB;IAC3B;AACF;AAEA;;;CAGC,GACD,MAAMoB,SACJ;IACE,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;QACjB,MAAM,qBAA2D,CAA3D,IAAIZ,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAME,YAAYvB,mBAAmB,IAAI,CAACiC,OAAO;IACjD,MAAMC,OAAO,IAAI,CAACC,UAAU;IAE5B,OAAQD,KAAKzB,IAAI;QACf,KAAKR,UAAUS,KAAK;YAAE;gBACpB,OAAO,MAAMY,UAAUY,MAAMX;YAC/B;QACA,KAAKtB,UAAUmB,SAAS;YAAE;gBACxB,OAAO,MAAMW,aAAaG,MAAMX;YAClC;QACA;YAAS;gBACP,MAAM,qBAA6C,CAA7C,IAAIF,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;IACF;AACF;AAEF,eAAeW,OAAM"}