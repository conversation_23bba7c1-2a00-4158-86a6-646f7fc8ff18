{"version": 3, "sources": ["../../src/server/image-optimizer.ts"], "sourcesContent": ["import { createHash } from 'crypto'\nimport { promises } from 'fs'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport { mediaType } from 'next/dist/compiled/@hapi/accept'\nimport contentDisposition from 'next/dist/compiled/content-disposition'\nimport imageSizeOf from 'next/dist/compiled/image-size'\nimport isAnimated from 'next/dist/compiled/is-animated'\nimport { join } from 'path'\nimport nodeUrl, { type UrlWithParsedQuery } from 'url'\n\nimport { getImageBlurSvg } from '../shared/lib/image-blur-svg'\nimport type { ImageConfigComplete } from '../shared/lib/image-config'\nimport { hasLocalMatch } from '../shared/lib/match-local-pattern'\nimport { hasRemoteMatch } from '../shared/lib/match-remote-pattern'\nimport type { NextConfigComplete } from './config-shared'\nimport { createRequestResponseMocks } from './lib/mock-request'\nimport type { NextUrlWithParsedQuery } from './request-meta'\nimport {\n  CachedRouteKind,\n  type CachedImageValue,\n  type IncrementalCacheEntry,\n  type IncrementalCacheValue,\n  type IncrementalResponseCacheEntry,\n} from './response-cache'\nimport { sendEtagResponse } from './send-payload'\nimport { getContentType, getExtension } from './serve-static'\nimport * as Log from '../build/output/log'\nimport isError from '../lib/is-error'\nimport { parseUrl } from '../lib/url'\nimport type { CacheControl } from './lib/cache-control'\nimport { InvariantError } from '../shared/lib/invariant-error'\n\ntype XCacheHeader = 'MISS' | 'HIT' | 'STALE'\n\nconst AVIF = 'image/avif'\nconst WEBP = 'image/webp'\nconst PNG = 'image/png'\nconst JPEG = 'image/jpeg'\nconst GIF = 'image/gif'\nconst SVG = 'image/svg+xml'\nconst ICO = 'image/x-icon'\nconst ICNS = 'image/x-icns'\nconst TIFF = 'image/tiff'\nconst BMP = 'image/bmp'\nconst CACHE_VERSION = 4\nconst ANIMATABLE_TYPES = [WEBP, PNG, GIF]\nconst BYPASS_TYPES = [SVG, ICO, ICNS, BMP]\nconst BLUR_IMG_SIZE = 8 // should match `next-image-loader`\nconst BLUR_QUALITY = 70 // should match `next-image-loader`\n\nlet _sharp: typeof import('sharp')\n\nexport function getSharp(concurrency: number | null | undefined) {\n  if (_sharp) {\n    return _sharp\n  }\n  try {\n    _sharp = require('sharp')\n    if (_sharp && _sharp.concurrency() > 1) {\n      // Reducing concurrency should reduce the memory usage too.\n      // We more aggressively reduce in dev but also reduce in prod.\n      // https://sharp.pixelplumbing.com/api-utility#concurrency\n      const divisor = process.env.NODE_ENV === 'development' ? 4 : 2\n      _sharp.concurrency(\n        concurrency ?? Math.floor(Math.max(_sharp.concurrency() / divisor, 1))\n      )\n    }\n  } catch (e: unknown) {\n    if (isError(e) && e.code === 'MODULE_NOT_FOUND') {\n      throw new Error(\n        'Module `sharp` not found. Please run `npm install --cpu=wasm32 sharp` to install it.'\n      )\n    }\n    throw e\n  }\n  return _sharp\n}\n\nexport interface ImageParamsResult {\n  href: string\n  isAbsolute: boolean\n  isStatic: boolean\n  width: number\n  quality: number\n  mimeType: string\n  sizes: number[]\n  minimumCacheTTL: number\n}\n\ninterface ImageUpstream {\n  buffer: Buffer\n  contentType: string | null | undefined\n  cacheControl: string | null | undefined\n  etag: string\n}\n\nfunction getSupportedMimeType(options: string[], accept = ''): string {\n  const mimeType = mediaType(accept, options)\n  return accept.includes(mimeType) ? mimeType : ''\n}\n\nexport function getHash(items: (string | number | Buffer)[]) {\n  const hash = createHash('sha256')\n  for (let item of items) {\n    if (typeof item === 'number') hash.update(String(item))\n    else {\n      hash.update(item)\n    }\n  }\n  // See https://en.wikipedia.org/wiki/Base64#URL_applications\n  return hash.digest('base64url')\n}\n\nexport function extractEtag(\n  etag: string | null | undefined,\n  imageBuffer: Buffer\n) {\n  if (etag) {\n    // upstream etag needs to be base64url encoded due to weak etag signature\n    // as we store this in the cache-entry file name.\n    return Buffer.from(etag).toString('base64url')\n  }\n  return getImageEtag(imageBuffer)\n}\n\nexport function getImageEtag(image: Buffer) {\n  return getHash([image])\n}\n\nasync function writeToCacheDir(\n  dir: string,\n  extension: string,\n  maxAge: number,\n  expireAt: number,\n  buffer: Buffer,\n  etag: string,\n  upstreamEtag: string\n) {\n  const filename = join(\n    dir,\n    `${maxAge}.${expireAt}.${etag}.${upstreamEtag}.${extension}`\n  )\n\n  await promises.rm(dir, { recursive: true, force: true }).catch(() => {})\n\n  await promises.mkdir(dir, { recursive: true })\n  await promises.writeFile(filename, buffer)\n}\n\n/**\n * Inspects the first few bytes of a buffer to determine if\n * it matches the \"magic number\" of known file signatures.\n * https://en.wikipedia.org/wiki/List_of_file_signatures\n */\nexport function detectContentType(buffer: Buffer) {\n  if ([0xff, 0xd8, 0xff].every((b, i) => buffer[i] === b)) {\n    return JPEG\n  }\n  if (\n    [0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a].every(\n      (b, i) => buffer[i] === b\n    )\n  ) {\n    return PNG\n  }\n  if ([0x47, 0x49, 0x46, 0x38].every((b, i) => buffer[i] === b)) {\n    return GIF\n  }\n  if (\n    [0x52, 0x49, 0x46, 0x46, 0, 0, 0, 0, 0x57, 0x45, 0x42, 0x50].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return WEBP\n  }\n  if ([0x3c, 0x3f, 0x78, 0x6d, 0x6c].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if ([0x3c, 0x73, 0x76, 0x67].every((b, i) => buffer[i] === b)) {\n    return SVG\n  }\n  if (\n    [0, 0, 0, 0, 0x66, 0x74, 0x79, 0x70, 0x61, 0x76, 0x69, 0x66].every(\n      (b, i) => !b || buffer[i] === b\n    )\n  ) {\n    return AVIF\n  }\n  if ([0x00, 0x00, 0x01, 0x00].every((b, i) => buffer[i] === b)) {\n    return ICO\n  }\n  if ([0x69, 0x63, 0x6e, 0x73].every((b, i) => buffer[i] === b)) {\n    return ICNS\n  }\n  if ([0x49, 0x49, 0x2a, 0x00].every((b, i) => buffer[i] === b)) {\n    return TIFF\n  }\n  if ([0x42, 0x4d].every((b, i) => buffer[i] === b)) {\n    return BMP\n  }\n  return null\n}\n\nexport class ImageOptimizerCache {\n  private cacheDir: string\n  private nextConfig: NextConfigComplete\n\n  static validateParams(\n    req: IncomingMessage,\n    query: UrlWithParsedQuery['query'],\n    nextConfig: NextConfigComplete,\n    isDev: boolean\n  ): ImageParamsResult | { errorMessage: string } {\n    const imageData = nextConfig.images\n    const {\n      deviceSizes = [],\n      imageSizes = [],\n      domains = [],\n      minimumCacheTTL = 60,\n      formats = ['image/webp'],\n    } = imageData\n    const remotePatterns = nextConfig.images?.remotePatterns || []\n    const localPatterns = nextConfig.images?.localPatterns\n    const qualities = nextConfig.images?.qualities\n    const { url, w, q } = query\n    let href: string\n\n    if (domains.length > 0) {\n      Log.warnOnce(\n        'The \"images.domains\" configuration is deprecated. Please use \"images.remotePatterns\" configuration instead.'\n      )\n    }\n\n    if (!url) {\n      return { errorMessage: '\"url\" parameter is required' }\n    } else if (Array.isArray(url)) {\n      return { errorMessage: '\"url\" parameter cannot be an array' }\n    }\n\n    if (url.length > 3072) {\n      return { errorMessage: '\"url\" parameter is too long' }\n    }\n\n    if (url.startsWith('//')) {\n      return {\n        errorMessage: '\"url\" parameter cannot be a protocol-relative URL (//)',\n      }\n    }\n\n    let isAbsolute: boolean\n\n    if (url.startsWith('/')) {\n      href = url\n      isAbsolute = false\n      if (\n        /\\/_next\\/image($|\\/)/.test(\n          decodeURIComponent(parseUrl(url)?.pathname ?? '')\n        )\n      ) {\n        return {\n          errorMessage: '\"url\" parameter cannot be recursive',\n        }\n      }\n      if (!hasLocalMatch(localPatterns, url)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    } else {\n      let hrefParsed: URL\n\n      try {\n        hrefParsed = new URL(url)\n        href = hrefParsed.toString()\n        isAbsolute = true\n      } catch (_error) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!['http:', 'https:'].includes(hrefParsed.protocol)) {\n        return { errorMessage: '\"url\" parameter is invalid' }\n      }\n\n      if (!hasRemoteMatch(domains, remotePatterns, hrefParsed)) {\n        return { errorMessage: '\"url\" parameter is not allowed' }\n      }\n    }\n\n    if (!w) {\n      return { errorMessage: '\"w\" parameter (width) is required' }\n    } else if (Array.isArray(w)) {\n      return { errorMessage: '\"w\" parameter (width) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(w)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    if (!q) {\n      return { errorMessage: '\"q\" parameter (quality) is required' }\n    } else if (Array.isArray(q)) {\n      return { errorMessage: '\"q\" parameter (quality) cannot be an array' }\n    } else if (!/^[0-9]+$/.test(q)) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    const width = parseInt(w, 10)\n\n    if (width <= 0 || isNaN(width)) {\n      return {\n        errorMessage: '\"w\" parameter (width) must be an integer greater than 0',\n      }\n    }\n\n    const sizes = [...(deviceSizes || []), ...(imageSizes || [])]\n\n    if (isDev) {\n      sizes.push(BLUR_IMG_SIZE)\n    }\n\n    const isValidSize =\n      sizes.includes(width) || (isDev && width <= BLUR_IMG_SIZE)\n\n    if (!isValidSize) {\n      return {\n        errorMessage: `\"w\" parameter (width) of ${width} is not allowed`,\n      }\n    }\n\n    const quality = parseInt(q, 10)\n\n    if (isNaN(quality) || quality < 1 || quality > 100) {\n      return {\n        errorMessage:\n          '\"q\" parameter (quality) must be an integer between 1 and 100',\n      }\n    }\n\n    if (qualities) {\n      if (isDev) {\n        qualities.push(BLUR_QUALITY)\n      }\n\n      if (!qualities.includes(quality)) {\n        return {\n          errorMessage: `\"q\" parameter (quality) of ${q} is not allowed`,\n        }\n      }\n    }\n\n    const mimeType = getSupportedMimeType(formats || [], req.headers['accept'])\n\n    const isStatic = url.startsWith(\n      `${nextConfig.basePath || ''}/_next/static/media`\n    )\n\n    return {\n      href,\n      sizes,\n      isAbsolute,\n      isStatic,\n      width,\n      quality,\n      mimeType,\n      minimumCacheTTL,\n    }\n  }\n\n  static getCacheKey({\n    href,\n    width,\n    quality,\n    mimeType,\n  }: {\n    href: string\n    width: number\n    quality: number\n    mimeType: string\n  }): string {\n    return getHash([CACHE_VERSION, href, width, quality, mimeType])\n  }\n\n  constructor({\n    distDir,\n    nextConfig,\n  }: {\n    distDir: string\n    nextConfig: NextConfigComplete\n  }) {\n    this.cacheDir = join(distDir, 'cache', 'images')\n    this.nextConfig = nextConfig\n  }\n\n  async get(cacheKey: string): Promise<IncrementalResponseCacheEntry | null> {\n    try {\n      const cacheDir = join(this.cacheDir, cacheKey)\n      const files = await promises.readdir(cacheDir)\n      const now = Date.now()\n\n      for (const file of files) {\n        const [maxAgeSt, expireAtSt, etag, upstreamEtag, extension] =\n          file.split('.', 5)\n        const buffer = await promises.readFile(join(cacheDir, file))\n        const expireAt = Number(expireAtSt)\n        const maxAge = Number(maxAgeSt)\n\n        return {\n          value: {\n            kind: CachedRouteKind.IMAGE,\n            etag,\n            buffer,\n            extension,\n            upstreamEtag,\n          },\n          revalidateAfter:\n            Math.max(maxAge, this.nextConfig.images.minimumCacheTTL) * 1000 +\n            Date.now(),\n          cacheControl: { revalidate: maxAge, expire: undefined },\n          isStale: now > expireAt,\n          isFallback: false,\n        }\n      }\n    } catch (_) {\n      // failed to read from cache dir, treat as cache miss\n    }\n    return null\n  }\n  async set(\n    cacheKey: string,\n    value: IncrementalCacheValue | null,\n    {\n      cacheControl,\n    }: {\n      cacheControl?: CacheControl\n    }\n  ) {\n    if (!this.nextConfig.experimental.isrFlushToDisk) {\n      return\n    }\n\n    if (value?.kind !== CachedRouteKind.IMAGE) {\n      throw new Error('invariant attempted to set non-image to image-cache')\n    }\n\n    const revalidate = cacheControl?.revalidate\n\n    if (typeof revalidate !== 'number') {\n      throw new InvariantError('revalidate must be a number for image-cache')\n    }\n\n    const expireAt =\n      Math.max(revalidate, this.nextConfig.images.minimumCacheTTL) * 1000 +\n      Date.now()\n\n    try {\n      await writeToCacheDir(\n        join(this.cacheDir, cacheKey),\n        value.extension,\n        revalidate,\n        expireAt,\n        value.buffer,\n        value.etag,\n        value.upstreamEtag\n      )\n    } catch (err) {\n      Log.error(`Failed to write image to cache ${cacheKey}`, err)\n    }\n  }\n}\nexport class ImageError extends Error {\n  statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n\n    // ensure an error status is used > 400\n    if (statusCode >= 400) {\n      this.statusCode = statusCode\n    } else {\n      this.statusCode = 500\n    }\n  }\n}\n\nfunction parseCacheControl(\n  str: string | null | undefined\n): Map<string, string> {\n  const map = new Map<string, string>()\n  if (!str) {\n    return map\n  }\n  for (let directive of str.split(',')) {\n    let [key, value] = directive.trim().split('=', 2)\n    key = key.toLowerCase()\n    if (value) {\n      value = value.toLowerCase()\n    }\n    map.set(key, value)\n  }\n  return map\n}\n\nexport function getMaxAge(str: string | null | undefined): number {\n  const map = parseCacheControl(str)\n  if (map) {\n    let age = map.get('s-maxage') || map.get('max-age') || ''\n    if (age.startsWith('\"') && age.endsWith('\"')) {\n      age = age.slice(1, -1)\n    }\n    const n = parseInt(age, 10)\n    if (!isNaN(n)) {\n      return n\n    }\n  }\n  return 0\n}\nexport function getPreviouslyCachedImageOrNull(\n  upstreamImage: ImageUpstream,\n  previousCacheEntry: IncrementalCacheEntry | null | undefined\n): CachedImageValue | null {\n  if (\n    previousCacheEntry?.value?.kind === 'IMAGE' &&\n    // Images that are SVGs, animated or failed the optimization previously end up using upstreamEtag as their etag as well,\n    // in these cases we want to trigger a new \"optimization\" attempt.\n    previousCacheEntry.value.upstreamEtag !== previousCacheEntry.value.etag &&\n    // and the upstream etag is the same as the previous cache entry's\n    upstreamImage.etag === previousCacheEntry.value.upstreamEtag\n  ) {\n    return previousCacheEntry.value\n  }\n  return null\n}\n\nexport async function optimizeImage({\n  buffer,\n  contentType,\n  quality,\n  width,\n  height,\n  concurrency,\n  limitInputPixels,\n  sequentialRead,\n  timeoutInSeconds,\n}: {\n  buffer: Buffer\n  contentType: string\n  quality: number\n  width: number\n  height?: number\n  concurrency?: number | null\n  limitInputPixels?: number\n  sequentialRead?: boolean | null\n  timeoutInSeconds?: number\n}): Promise<Buffer> {\n  const sharp = getSharp(concurrency)\n  const transformer = sharp(buffer, {\n    limitInputPixels,\n    sequentialRead: sequentialRead ?? undefined,\n  })\n    .timeout({\n      seconds: timeoutInSeconds ?? 7,\n    })\n    .rotate()\n\n  if (height) {\n    transformer.resize(width, height)\n  } else {\n    transformer.resize(width, undefined, {\n      withoutEnlargement: true,\n    })\n  }\n\n  if (contentType === AVIF) {\n    transformer.avif({\n      quality: Math.max(quality - 20, 1),\n      effort: 3,\n    })\n  } else if (contentType === WEBP) {\n    transformer.webp({ quality })\n  } else if (contentType === PNG) {\n    transformer.png({ quality })\n  } else if (contentType === JPEG) {\n    transformer.jpeg({ quality, mozjpeg: true })\n  }\n\n  const optimizedBuffer = await transformer.toBuffer()\n\n  return optimizedBuffer\n}\n\nexport async function fetchExternalImage(href: string): Promise<ImageUpstream> {\n  const res = await fetch(href, {\n    signal: AbortSignal.timeout(7_000),\n  }).catch((err) => err as Error)\n\n  if (res instanceof Error) {\n    const err = res as Error\n    if (err.name === 'TimeoutError') {\n      Log.error('upstream image response timed out for', href)\n      throw new ImageError(\n        504,\n        '\"url\" parameter is valid but upstream response timed out'\n      )\n    }\n    throw err\n  }\n\n  if (!res.ok) {\n    Log.error('upstream image response failed for', href, res.status)\n    throw new ImageError(\n      res.status,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n\n  const buffer = Buffer.from(await res.arrayBuffer())\n  const contentType = res.headers.get('Content-Type')\n  const cacheControl = res.headers.get('Cache-Control')\n  const etag = extractEtag(res.headers.get('ETag'), buffer)\n  return { buffer, contentType, cacheControl, etag }\n}\n\nexport async function fetchInternalImage(\n  href: string,\n  _req: IncomingMessage,\n  _res: ServerResponse,\n  handleRequest: (\n    newReq: IncomingMessage,\n    newRes: ServerResponse,\n    newParsedUrl?: NextUrlWithParsedQuery\n  ) => Promise<void>\n): Promise<ImageUpstream> {\n  try {\n    const mocked = createRequestResponseMocks({\n      url: href,\n      method: _req.method || 'GET',\n      headers: _req.headers,\n      socket: _req.socket,\n    })\n\n    await handleRequest(mocked.req, mocked.res, nodeUrl.parse(href, true))\n    await mocked.res.hasStreamed\n\n    if (!mocked.res.statusCode) {\n      Log.error('image response failed for', href, mocked.res.statusCode)\n      throw new ImageError(\n        mocked.res.statusCode,\n        '\"url\" parameter is valid but internal response is invalid'\n      )\n    }\n\n    const buffer = Buffer.concat(mocked.res.buffers)\n    const contentType = mocked.res.getHeader('Content-Type')\n    const cacheControl = mocked.res.getHeader('Cache-Control')\n    const etag = extractEtag(mocked.res.getHeader('ETag'), buffer)\n\n    return { buffer, contentType, cacheControl, etag }\n  } catch (err) {\n    Log.error('upstream image response failed for', href, err)\n    throw new ImageError(\n      500,\n      '\"url\" parameter is valid but upstream response is invalid'\n    )\n  }\n}\n\nexport async function imageOptimizer(\n  imageUpstream: ImageUpstream,\n  paramsResult: Pick<\n    ImageParamsResult,\n    'href' | 'width' | 'quality' | 'mimeType'\n  >,\n  nextConfig: {\n    experimental: Pick<\n      NextConfigComplete['experimental'],\n      | 'imgOptConcurrency'\n      | 'imgOptMaxInputPixels'\n      | 'imgOptSequentialRead'\n      | 'imgOptTimeoutInSeconds'\n    >\n    images: Pick<\n      NextConfigComplete['images'],\n      'dangerouslyAllowSVG' | 'minimumCacheTTL'\n    >\n  },\n  opts: {\n    isDev?: boolean\n    silent?: boolean\n    previousCacheEntry?: IncrementalResponseCacheEntry | null\n  }\n): Promise<{\n  buffer: Buffer\n  contentType: string\n  maxAge: number\n  etag: string\n  upstreamEtag: string\n  error?: unknown\n}> {\n  const { href, quality, width, mimeType } = paramsResult\n  const { buffer: upstreamBuffer, etag: upstreamEtag } = imageUpstream\n  const maxAge = Math.max(\n    nextConfig.images.minimumCacheTTL,\n    getMaxAge(imageUpstream.cacheControl)\n  )\n\n  const upstreamType =\n    detectContentType(upstreamBuffer) ||\n    imageUpstream.contentType?.toLowerCase().trim()\n\n  if (upstreamType) {\n    if (\n      upstreamType.startsWith('image/svg') &&\n      !nextConfig.images.dangerouslyAllowSVG\n    ) {\n      if (!opts.silent) {\n        Log.error(\n          `The requested resource \"${href}\" has type \"${upstreamType}\" but dangerouslyAllowSVG is disabled`\n        )\n      }\n      throw new ImageError(\n        400,\n        '\"url\" parameter is valid but image type is not allowed'\n      )\n    }\n    if (ANIMATABLE_TYPES.includes(upstreamType) && isAnimated(upstreamBuffer)) {\n      if (!opts.silent) {\n        Log.warnOnce(\n          `The requested resource \"${href}\" is an animated image so it will not be optimized. Consider adding the \"unoptimized\" property to the <Image>.`\n        )\n      }\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge,\n        etag: upstreamEtag,\n        upstreamEtag,\n      }\n    }\n    if (BYPASS_TYPES.includes(upstreamType)) {\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge,\n        etag: upstreamEtag,\n        upstreamEtag,\n      }\n    }\n    if (!upstreamType.startsWith('image/') || upstreamType.includes(',')) {\n      if (!opts.silent) {\n        Log.error(\n          \"The requested resource isn't a valid image for\",\n          href,\n          'received',\n          upstreamType\n        )\n      }\n      throw new ImageError(400, \"The requested resource isn't a valid image.\")\n    }\n  }\n\n  let contentType: string\n\n  if (mimeType) {\n    contentType = mimeType\n  } else if (\n    upstreamType?.startsWith('image/') &&\n    getExtension(upstreamType) &&\n    upstreamType !== WEBP &&\n    upstreamType !== AVIF\n  ) {\n    contentType = upstreamType\n  } else {\n    contentType = JPEG\n  }\n  const previouslyCachedImage = getPreviouslyCachedImageOrNull(\n    imageUpstream,\n    opts.previousCacheEntry\n  )\n  if (previouslyCachedImage) {\n    return {\n      buffer: previouslyCachedImage.buffer,\n      contentType,\n      maxAge: opts?.previousCacheEntry?.cacheControl?.revalidate || maxAge,\n      etag: previouslyCachedImage.etag,\n      upstreamEtag: previouslyCachedImage.upstreamEtag,\n    }\n  }\n\n  try {\n    let optimizedBuffer = await optimizeImage({\n      buffer: upstreamBuffer,\n      contentType,\n      quality,\n      width,\n      concurrency: nextConfig.experimental.imgOptConcurrency,\n      limitInputPixels: nextConfig.experimental.imgOptMaxInputPixels,\n      sequentialRead: nextConfig.experimental.imgOptSequentialRead,\n      timeoutInSeconds: nextConfig.experimental.imgOptTimeoutInSeconds,\n    })\n    if (opts.isDev && width <= BLUR_IMG_SIZE && quality === BLUR_QUALITY) {\n      // During `next dev`, we don't want to generate blur placeholders with webpack\n      // because it can delay starting the dev server. Instead, `next-image-loader.js`\n      // will inline a special url to lazily generate the blur placeholder at request time.\n      const meta = await getImageSize(optimizedBuffer)\n      const blurOpts = {\n        blurWidth: meta.width,\n        blurHeight: meta.height,\n        blurDataURL: `data:${contentType};base64,${optimizedBuffer.toString(\n          'base64'\n        )}`,\n      }\n      optimizedBuffer = Buffer.from(unescape(getImageBlurSvg(blurOpts)))\n      contentType = 'image/svg+xml'\n    }\n    return {\n      buffer: optimizedBuffer,\n      contentType,\n      maxAge,\n      etag: getImageEtag(optimizedBuffer),\n      upstreamEtag,\n    }\n  } catch (error) {\n    if (upstreamType) {\n      // If we fail to optimize, fallback to the original image\n      return {\n        buffer: upstreamBuffer,\n        contentType: upstreamType,\n        maxAge: nextConfig.images.minimumCacheTTL,\n        etag: upstreamEtag,\n        upstreamEtag,\n        error,\n      }\n    } else {\n      throw new ImageError(\n        400,\n        'Unable to optimize image and unable to fallback to upstream image'\n      )\n    }\n  }\n}\n\nfunction getFileNameWithExtension(\n  url: string,\n  contentType: string | null\n): string {\n  const [urlWithoutQueryParams] = url.split('?', 1)\n  const fileNameWithExtension = urlWithoutQueryParams.split('/').pop()\n  if (!contentType || !fileNameWithExtension) {\n    return 'image.bin'\n  }\n\n  const [fileName] = fileNameWithExtension.split('.', 1)\n  const extension = getExtension(contentType)\n  return `${fileName}.${extension}`\n}\n\nfunction setResponseHeaders(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  etag: string,\n  contentType: string | null,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  res.setHeader('Vary', 'Accept')\n  res.setHeader(\n    'Cache-Control',\n    isStatic\n      ? 'public, max-age=315360000, immutable'\n      : `public, max-age=${isDev ? 0 : maxAge}, must-revalidate`\n  )\n  if (sendEtagResponse(req, res, etag)) {\n    // already called res.end() so we're finished\n    return { finished: true }\n  }\n  if (contentType) {\n    res.setHeader('Content-Type', contentType)\n  }\n\n  const fileName = getFileNameWithExtension(url, contentType)\n  res.setHeader(\n    'Content-Disposition',\n    contentDisposition(fileName, { type: imagesConfig.contentDispositionType })\n  )\n\n  res.setHeader('Content-Security-Policy', imagesConfig.contentSecurityPolicy)\n  res.setHeader('X-Nextjs-Cache', xCache)\n\n  return { finished: false }\n}\n\nexport function sendResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  url: string,\n  extension: string,\n  buffer: Buffer,\n  etag: string,\n  isStatic: boolean,\n  xCache: XCacheHeader,\n  imagesConfig: ImageConfigComplete,\n  maxAge: number,\n  isDev: boolean\n) {\n  const contentType = getContentType(extension)\n  const result = setResponseHeaders(\n    req,\n    res,\n    url,\n    etag,\n    contentType,\n    isStatic,\n    xCache,\n    imagesConfig,\n    maxAge,\n    isDev\n  )\n  if (!result.finished) {\n    res.setHeader('Content-Length', Buffer.byteLength(buffer))\n    res.end(buffer)\n  }\n}\n\nexport async function getImageSize(buffer: Buffer): Promise<{\n  width?: number\n  height?: number\n}> {\n  const { width, height } = imageSizeOf(buffer)\n  return { width, height }\n}\n"], "names": ["ImageError", "ImageOptimizerCache", "detectContentType", "extractEtag", "fetchExternalImage", "fetchInternalImage", "getHash", "getImageEtag", "getImageSize", "getMaxAge", "getPreviouslyCachedImageOrNull", "getSharp", "imageOptimizer", "optimizeImage", "sendResponse", "AVIF", "WEBP", "PNG", "JPEG", "GIF", "SVG", "ICO", "ICNS", "TIFF", "BMP", "CACHE_VERSION", "ANIMATABLE_TYPES", "BYPASS_TYPES", "BLUR_IMG_SIZE", "BLUR_QUALITY", "_sharp", "concurrency", "require", "divisor", "process", "env", "NODE_ENV", "Math", "floor", "max", "e", "isError", "code", "Error", "getSupportedMimeType", "options", "accept", "mimeType", "mediaType", "includes", "items", "hash", "createHash", "item", "update", "String", "digest", "etag", "imageBuffer", "<PERSON><PERSON><PERSON>", "from", "toString", "image", "writeToCacheDir", "dir", "extension", "maxAge", "expireAt", "buffer", "upstreamEtag", "filename", "join", "promises", "rm", "recursive", "force", "catch", "mkdir", "writeFile", "every", "b", "i", "validateParams", "req", "query", "nextConfig", "isDev", "imageData", "images", "deviceSizes", "imageSizes", "domains", "minimumCacheTTL", "formats", "remotePatterns", "localPatterns", "qualities", "url", "w", "q", "href", "length", "Log", "warnOnce", "errorMessage", "Array", "isArray", "startsWith", "isAbsolute", "parseUrl", "test", "decodeURIComponent", "pathname", "hasLocalMatch", "hrefParsed", "URL", "_error", "protocol", "hasRemoteMatch", "width", "parseInt", "isNaN", "sizes", "push", "isValidSize", "quality", "headers", "isStatic", "basePath", "get<PERSON><PERSON><PERSON><PERSON>", "constructor", "distDir", "cacheDir", "get", "cache<PERSON>ey", "files", "readdir", "now", "Date", "file", "maxAgeSt", "expireAtSt", "split", "readFile", "Number", "value", "kind", "CachedRouteKind", "IMAGE", "revalidateAfter", "cacheControl", "revalidate", "expire", "undefined", "isStale", "<PERSON><PERSON><PERSON><PERSON>", "_", "set", "experimental", "isrFlushToDisk", "InvariantError", "err", "error", "statusCode", "message", "parseCacheControl", "str", "map", "Map", "directive", "key", "trim", "toLowerCase", "age", "endsWith", "slice", "n", "upstreamImage", "previousCacheEntry", "contentType", "height", "limitInputPixels", "sequentialRead", "timeoutInSeconds", "sharp", "transformer", "timeout", "seconds", "rotate", "resize", "withoutEnlargement", "avif", "effort", "webp", "png", "jpeg", "mozjpeg", "optimizedBuffer", "<PERSON><PERSON><PERSON><PERSON>", "res", "fetch", "signal", "AbortSignal", "name", "ok", "status", "arrayBuffer", "_req", "_res", "handleRequest", "mocked", "createRequestResponseMocks", "method", "socket", "nodeUrl", "parse", "hasStreamed", "concat", "buffers", "<PERSON><PERSON><PERSON><PERSON>", "imageUpstream", "paramsResult", "opts", "upstreamBuffer", "upstreamType", "dangerouslyAllowSVG", "silent", "isAnimated", "getExtension", "previouslyCachedImage", "imgOptConcurrency", "imgOptMaxInputPixels", "imgOptSequentialRead", "imgOptTimeoutInSeconds", "meta", "blurOpts", "blur<PERSON>idth", "blurHeight", "blurDataURL", "unescape", "getImageBlurSvg", "getFileNameWithExtension", "urlWithoutQueryParams", "fileNameWithExtension", "pop", "fileName", "setResponseHeaders", "xCache", "imagesConfig", "<PERSON><PERSON><PERSON><PERSON>", "sendEtagResponse", "finished", "contentDisposition", "type", "contentDispositionType", "contentSecurityPolicy", "getContentType", "result", "byteLength", "end", "imageSizeOf"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsdaA,UAAU;eAAVA;;IA3QAC,mBAAmB;eAAnBA;;IAjDGC,iBAAiB;eAAjBA;;IAzCAC,WAAW;eAAXA;;IA8dMC,kBAAkB;eAAlBA;;IAgCAC,kBAAkB;eAAlBA;;IA1gBNC,OAAO;eAAPA;;IAwBAC,YAAY;eAAZA;;IAmyBMC,YAAY;eAAZA;;IAzaNC,SAAS;eAATA;;IAcAC,8BAA8B;eAA9BA;;IAjdAC,QAAQ;eAARA;;IAumBMC,cAAc;eAAdA;;IArIAC,aAAa;eAAbA;;IA0WNC,YAAY;eAAZA;;;wBAh4BW;oBACF;wBAEC;2EACK;kEACP;mEACD;sBACF;4DAC4B;8BAEjB;mCAEF;oCACC;6BAEY;+BAQpC;6BAC0B;6BACY;6DACxB;gEACD;sBACK;gCAEM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI/B,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,MAAM;AACZ,MAAMC,OAAO;AACb,MAAMC,OAAO;AACb,MAAMC,MAAM;AACZ,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;IAACV;IAAMC;IAAKE;CAAI;AACzC,MAAMQ,eAAe;IAACP;IAAKC;IAAKC;IAAME;CAAI;AAC1C,MAAMI,gBAAgB,EAAE,mCAAmC;;AAC3D,MAAMC,eAAe,GAAG,mCAAmC;;AAE3D,IAAIC;AAEG,SAASnB,SAASoB,WAAsC;IAC7D,IAAID,QAAQ;QACV,OAAOA;IACT;IACA,IAAI;QACFA,SAASE,QAAQ;QACjB,IAAIF,UAAUA,OAAOC,WAAW,KAAK,GAAG;YACtC,2DAA2D;YAC3D,8DAA8D;YAC9D,0DAA0D;YAC1D,MAAME,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,IAAI;YAC7DN,OAAOC,WAAW,CAChBA,eAAeM,KAAKC,KAAK,CAACD,KAAKE,GAAG,CAACT,OAAOC,WAAW,KAAKE,SAAS;QAEvE;IACF,EAAE,OAAOO,GAAY;QACnB,IAAIC,IAAAA,gBAAO,EAACD,MAAMA,EAAEE,IAAI,KAAK,oBAAoB;YAC/C,MAAM,qBAEL,CAFK,IAAIC,MACR,yFADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,MAAMH;IACR;IACA,OAAOV;AACT;AAoBA,SAASc,qBAAqBC,OAAiB,EAAEC,SAAS,EAAE;IAC1D,MAAMC,WAAWC,IAAAA,iBAAS,EAACF,QAAQD;IACnC,OAAOC,OAAOG,QAAQ,CAACF,YAAYA,WAAW;AAChD;AAEO,SAASzC,QAAQ4C,KAAmC;IACzD,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;IACxB,KAAK,IAAIC,QAAQH,MAAO;QACtB,IAAI,OAAOG,SAAS,UAAUF,KAAKG,MAAM,CAACC,OAAOF;aAC5C;YACHF,KAAKG,MAAM,CAACD;QACd;IACF;IACA,4DAA4D;IAC5D,OAAOF,KAAKK,MAAM,CAAC;AACrB;AAEO,SAASrD,YACdsD,IAA+B,EAC/BC,WAAmB;IAEnB,IAAID,MAAM;QACR,yEAAyE;QACzE,iDAAiD;QACjD,OAAOE,OAAOC,IAAI,CAACH,MAAMI,QAAQ,CAAC;IACpC;IACA,OAAOtD,aAAamD;AACtB;AAEO,SAASnD,aAAauD,KAAa;IACxC,OAAOxD,QAAQ;QAACwD;KAAM;AACxB;AAEA,eAAeC,gBACbC,GAAW,EACXC,SAAiB,EACjBC,MAAc,EACdC,QAAgB,EAChBC,MAAc,EACdX,IAAY,EACZY,YAAoB;IAEpB,MAAMC,WAAWC,IAAAA,UAAI,EACnBP,KACA,GAAGE,OAAO,CAAC,EAAEC,SAAS,CAAC,EAAEV,KAAK,CAAC,EAAEY,aAAa,CAAC,EAAEJ,WAAW;IAG9D,MAAMO,YAAQ,CAACC,EAAE,CAACT,KAAK;QAAEU,WAAW;QAAMC,OAAO;IAAK,GAAGC,KAAK,CAAC,KAAO;IAEtE,MAAMJ,YAAQ,CAACK,KAAK,CAACb,KAAK;QAAEU,WAAW;IAAK;IAC5C,MAAMF,YAAQ,CAACM,SAAS,CAACR,UAAUF;AACrC;AAOO,SAASlE,kBAAkBkE,MAAc;IAC9C,IAAI;QAAC;QAAM;QAAM;KAAK,CAACW,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACvD,OAAO9D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC6D,KAAK,CACpD,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAE1B;QACA,OAAO/D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC8D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAO7D;IACT;IACA,IACE;QAAC;QAAM;QAAM;QAAM;QAAM;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;KAAK,CAAC4D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOhE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC+D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACnE,OAAO5D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC2D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAO5D;IACT;IACA,IACE;QAAC;QAAG;QAAG;QAAG;QAAG;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,CAAC2D,KAAK,CAChE,CAACC,GAAGC,IAAM,CAACD,KAAKZ,MAAM,CAACa,EAAE,KAAKD,IAEhC;QACA,OAAOjE;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACgE,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAO3D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAAC0D,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAO1D;IACT;IACA,IAAI;QAAC;QAAM;QAAM;QAAM;KAAK,CAACyD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QAC7D,OAAOzD;IACT;IACA,IAAI;QAAC;QAAM;KAAK,CAACwD,KAAK,CAAC,CAACC,GAAGC,IAAMb,MAAM,CAACa,EAAE,KAAKD,IAAI;QACjD,OAAOxD;IACT;IACA,OAAO;AACT;AAEO,MAAMvB;IAIX,OAAOiF,eACLC,GAAoB,EACpBC,KAAkC,EAClCC,UAA8B,EAC9BC,KAAc,EACgC;YASvBD,oBACDA,qBACJA;QAVlB,MAAME,YAAYF,WAAWG,MAAM;QACnC,MAAM,EACJC,cAAc,EAAE,EAChBC,aAAa,EAAE,EACfC,UAAU,EAAE,EACZC,kBAAkB,EAAE,EACpBC,UAAU;YAAC;SAAa,EACzB,GAAGN;QACJ,MAAMO,iBAAiBT,EAAAA,qBAAAA,WAAWG,MAAM,qBAAjBH,mBAAmBS,cAAc,KAAI,EAAE;QAC9D,MAAMC,iBAAgBV,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBU,aAAa;QACtD,MAAMC,aAAYX,sBAAAA,WAAWG,MAAM,qBAAjBH,oBAAmBW,SAAS;QAC9C,MAAM,EAAEC,GAAG,EAAEC,CAAC,EAAEC,CAAC,EAAE,GAAGf;QACtB,IAAIgB;QAEJ,IAAIT,QAAQU,MAAM,GAAG,GAAG;YACtBC,KAAIC,QAAQ,CACV;QAEJ;QAEA,IAAI,CAACN,KAAK;YACR,OAAO;gBAAEO,cAAc;YAA8B;QACvD,OAAO,IAAIC,MAAMC,OAAO,CAACT,MAAM;YAC7B,OAAO;gBAAEO,cAAc;YAAqC;QAC9D;QAEA,IAAIP,IAAII,MAAM,GAAG,MAAM;YACrB,OAAO;gBAAEG,cAAc;YAA8B;QACvD;QAEA,IAAIP,IAAIU,UAAU,CAAC,OAAO;YACxB,OAAO;gBACLH,cAAc;YAChB;QACF;QAEA,IAAII;QAEJ,IAAIX,IAAIU,UAAU,CAAC,MAAM;gBAKAE;YAJvBT,OAAOH;YACPW,aAAa;YACb,IACE,uBAAuBE,IAAI,CACzBC,mBAAmBF,EAAAA,YAAAA,IAAAA,cAAQ,EAACZ,yBAATY,UAAeG,QAAQ,KAAI,MAEhD;gBACA,OAAO;oBACLR,cAAc;gBAChB;YACF;YACA,IAAI,CAACS,IAAAA,gCAAa,EAAClB,eAAeE,MAAM;gBACtC,OAAO;oBAAEO,cAAc;gBAAiC;YAC1D;QACF,OAAO;YACL,IAAIU;YAEJ,IAAI;gBACFA,aAAa,IAAIC,IAAIlB;gBACrBG,OAAOc,WAAWrD,QAAQ;gBAC1B+C,aAAa;YACf,EAAE,OAAOQ,QAAQ;gBACf,OAAO;oBAAEZ,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAAC;gBAAC;gBAAS;aAAS,CAACvD,QAAQ,CAACiE,WAAWG,QAAQ,GAAG;gBACtD,OAAO;oBAAEb,cAAc;gBAA6B;YACtD;YAEA,IAAI,CAACc,IAAAA,kCAAc,EAAC3B,SAASG,gBAAgBoB,aAAa;gBACxD,OAAO;oBAAEV,cAAc;gBAAiC;YAC1D;QACF;QAEA,IAAI,CAACN,GAAG;YACN,OAAO;gBAAEM,cAAc;YAAoC;QAC7D,OAAO,IAAIC,MAAMC,OAAO,CAACR,IAAI;YAC3B,OAAO;gBAAEM,cAAc;YAA2C;QACpE,OAAO,IAAI,CAAC,WAAWM,IAAI,CAACZ,IAAI;YAC9B,OAAO;gBACLM,cAAc;YAChB;QACF;QAEA,IAAI,CAACL,GAAG;YACN,OAAO;gBAAEK,cAAc;YAAsC;QAC/D,OAAO,IAAIC,MAAMC,OAAO,CAACP,IAAI;YAC3B,OAAO;gBAAEK,cAAc;YAA6C;QACtE,OAAO,IAAI,CAAC,WAAWM,IAAI,CAACX,IAAI;YAC9B,OAAO;gBACLK,cACE;YACJ;QACF;QAEA,MAAMe,QAAQC,SAAStB,GAAG;QAE1B,IAAIqB,SAAS,KAAKE,MAAMF,QAAQ;YAC9B,OAAO;gBACLf,cAAc;YAChB;QACF;QAEA,MAAMkB,QAAQ;eAAKjC,eAAe,EAAE;eAAOC,cAAc,EAAE;SAAE;QAE7D,IAAIJ,OAAO;YACToC,MAAMC,IAAI,CAAC/F;QACb;QAEA,MAAMgG,cACJF,MAAMzE,QAAQ,CAACsE,UAAWjC,SAASiC,SAAS3F;QAE9C,IAAI,CAACgG,aAAa;YAChB,OAAO;gBACLpB,cAAc,CAAC,yBAAyB,EAAEe,MAAM,eAAe,CAAC;YAClE;QACF;QAEA,MAAMM,UAAUL,SAASrB,GAAG;QAE5B,IAAIsB,MAAMI,YAAYA,UAAU,KAAKA,UAAU,KAAK;YAClD,OAAO;gBACLrB,cACE;YACJ;QACF;QAEA,IAAIR,WAAW;YACb,IAAIV,OAAO;gBACTU,UAAU2B,IAAI,CAAC9F;YACjB;YAEA,IAAI,CAACmE,UAAU/C,QAAQ,CAAC4E,UAAU;gBAChC,OAAO;oBACLrB,cAAc,CAAC,2BAA2B,EAAEL,EAAE,eAAe,CAAC;gBAChE;YACF;QACF;QAEA,MAAMpD,WAAWH,qBAAqBiD,WAAW,EAAE,EAAEV,IAAI2C,OAAO,CAAC,SAAS;QAE1E,MAAMC,WAAW9B,IAAIU,UAAU,CAC7B,GAAGtB,WAAW2C,QAAQ,IAAI,GAAG,mBAAmB,CAAC;QAGnD,OAAO;YACL5B;YACAsB;YACAd;YACAmB;YACAR;YACAM;YACA9E;YACA6C;QACF;IACF;IAEA,OAAOqC,YAAY,EACjB7B,IAAI,EACJmB,KAAK,EACLM,OAAO,EACP9E,QAAQ,EAMT,EAAU;QACT,OAAOzC,QAAQ;YAACmB;YAAe2E;YAAMmB;YAAOM;YAAS9E;SAAS;IAChE;IAEAmF,YAAY,EACVC,OAAO,EACP9C,UAAU,EAIX,CAAE;QACD,IAAI,CAAC+C,QAAQ,GAAG7D,IAAAA,UAAI,EAAC4D,SAAS,SAAS;QACvC,IAAI,CAAC9C,UAAU,GAAGA;IACpB;IAEA,MAAMgD,IAAIC,QAAgB,EAAiD;QACzE,IAAI;YACF,MAAMF,WAAW7D,IAAAA,UAAI,EAAC,IAAI,CAAC6D,QAAQ,EAAEE;YACrC,MAAMC,QAAQ,MAAM/D,YAAQ,CAACgE,OAAO,CAACJ;YACrC,MAAMK,MAAMC,KAAKD,GAAG;YAEpB,KAAK,MAAME,QAAQJ,MAAO;gBACxB,MAAM,CAACK,UAAUC,YAAYpF,MAAMY,cAAcJ,UAAU,GACzD0E,KAAKG,KAAK,CAAC,KAAK;gBAClB,MAAM1E,SAAS,MAAMI,YAAQ,CAACuE,QAAQ,CAACxE,IAAAA,UAAI,EAAC6D,UAAUO;gBACtD,MAAMxE,WAAW6E,OAAOH;gBACxB,MAAM3E,SAAS8E,OAAOJ;gBAEtB,OAAO;oBACLK,OAAO;wBACLC,MAAMC,8BAAe,CAACC,KAAK;wBAC3B3F;wBACAW;wBACAH;wBACAI;oBACF;oBACAgF,iBACEhH,KAAKE,GAAG,CAAC2B,QAAQ,IAAI,CAACmB,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC3D8C,KAAKD,GAAG;oBACVa,cAAc;wBAAEC,YAAYrF;wBAAQsF,QAAQC;oBAAU;oBACtDC,SAASjB,MAAMtE;oBACfwF,YAAY;gBACd;YACF;QACF,EAAE,OAAOC,GAAG;QACV,qDAAqD;QACvD;QACA,OAAO;IACT;IACA,MAAMC,IACJvB,QAAgB,EAChBW,KAAmC,EACnC,EACEK,YAAY,EAGb,EACD;QACA,IAAI,CAAC,IAAI,CAACjE,UAAU,CAACyE,YAAY,CAACC,cAAc,EAAE;YAChD;QACF;QAEA,IAAId,CAAAA,yBAAAA,MAAOC,IAAI,MAAKC,8BAAe,CAACC,KAAK,EAAE;YACzC,MAAM,qBAAgE,CAAhE,IAAIzG,MAAM,wDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA+D;QACvE;QAEA,MAAM4G,aAAaD,gCAAAA,aAAcC,UAAU;QAE3C,IAAI,OAAOA,eAAe,UAAU;YAClC,MAAM,qBAAiE,CAAjE,IAAIS,8BAAc,CAAC,gDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAgE;QACxE;QAEA,MAAM7F,WACJ9B,KAAKE,GAAG,CAACgH,YAAY,IAAI,CAAClE,UAAU,CAACG,MAAM,CAACI,eAAe,IAAI,OAC/D8C,KAAKD,GAAG;QAEV,IAAI;YACF,MAAM1E,gBACJQ,IAAAA,UAAI,EAAC,IAAI,CAAC6D,QAAQ,EAAEE,WACpBW,MAAMhF,SAAS,EACfsF,YACApF,UACA8E,MAAM7E,MAAM,EACZ6E,MAAMxF,IAAI,EACVwF,MAAM5E,YAAY;QAEtB,EAAE,OAAO4F,KAAK;YACZ3D,KAAI4D,KAAK,CAAC,CAAC,+BAA+B,EAAE5B,UAAU,EAAE2B;QAC1D;IACF;AACF;AACO,MAAMjK,mBAAmB2C;IAG9BuF,YAAYiC,UAAkB,EAAEC,OAAe,CAAE;QAC/C,KAAK,CAACA;QAEN,uCAAuC;QACvC,IAAID,cAAc,KAAK;YACrB,IAAI,CAACA,UAAU,GAAGA;QACpB,OAAO;YACL,IAAI,CAACA,UAAU,GAAG;QACpB;IACF;AACF;AAEA,SAASE,kBACPC,GAA8B;IAE9B,MAAMC,MAAM,IAAIC;IAChB,IAAI,CAACF,KAAK;QACR,OAAOC;IACT;IACA,KAAK,IAAIE,aAAaH,IAAIxB,KAAK,CAAC,KAAM;QACpC,IAAI,CAAC4B,KAAKzB,MAAM,GAAGwB,UAAUE,IAAI,GAAG7B,KAAK,CAAC,KAAK;QAC/C4B,MAAMA,IAAIE,WAAW;QACrB,IAAI3B,OAAO;YACTA,QAAQA,MAAM2B,WAAW;QAC3B;QACAL,IAAIV,GAAG,CAACa,KAAKzB;IACf;IACA,OAAOsB;AACT;AAEO,SAAS9J,UAAU6J,GAA8B;IACtD,MAAMC,MAAMF,kBAAkBC;IAC9B,IAAIC,KAAK;QACP,IAAIM,MAAMN,IAAIlC,GAAG,CAAC,eAAekC,IAAIlC,GAAG,CAAC,cAAc;QACvD,IAAIwC,IAAIlE,UAAU,CAAC,QAAQkE,IAAIC,QAAQ,CAAC,MAAM;YAC5CD,MAAMA,IAAIE,KAAK,CAAC,GAAG,CAAC;QACtB;QACA,MAAMC,IAAIxD,SAASqD,KAAK;QACxB,IAAI,CAACpD,MAAMuD,IAAI;YACb,OAAOA;QACT;IACF;IACA,OAAO;AACT;AACO,SAAStK,+BACduK,aAA4B,EAC5BC,kBAA4D;QAG1DA;IADF,IACEA,CAAAA,uCAAAA,4BAAAA,mBAAoBjC,KAAK,qBAAzBiC,0BAA2BhC,IAAI,MAAK,WACpC,wHAAwH;IACxH,kEAAkE;IAClEgC,mBAAmBjC,KAAK,CAAC5E,YAAY,KAAK6G,mBAAmBjC,KAAK,CAACxF,IAAI,IACvE,kEAAkE;IAClEwH,cAAcxH,IAAI,KAAKyH,mBAAmBjC,KAAK,CAAC5E,YAAY,EAC5D;QACA,OAAO6G,mBAAmBjC,KAAK;IACjC;IACA,OAAO;AACT;AAEO,eAAepI,cAAc,EAClCuD,MAAM,EACN+G,WAAW,EACXtD,OAAO,EACPN,KAAK,EACL6D,MAAM,EACNrJ,WAAW,EACXsJ,gBAAgB,EAChBC,cAAc,EACdC,gBAAgB,EAWjB;IACC,MAAMC,QAAQ7K,SAASoB;IACvB,MAAM0J,cAAcD,MAAMpH,QAAQ;QAChCiH;QACAC,gBAAgBA,kBAAkB7B;IACpC,GACGiC,OAAO,CAAC;QACPC,SAASJ,oBAAoB;IAC/B,GACCK,MAAM;IAET,IAAIR,QAAQ;QACVK,YAAYI,MAAM,CAACtE,OAAO6D;IAC5B,OAAO;QACLK,YAAYI,MAAM,CAACtE,OAAOkC,WAAW;YACnCqC,oBAAoB;QACtB;IACF;IAEA,IAAIX,gBAAgBpK,MAAM;QACxB0K,YAAYM,IAAI,CAAC;YACflE,SAASxF,KAAKE,GAAG,CAACsF,UAAU,IAAI;YAChCmE,QAAQ;QACV;IACF,OAAO,IAAIb,gBAAgBnK,MAAM;QAC/ByK,YAAYQ,IAAI,CAAC;YAAEpE;QAAQ;IAC7B,OAAO,IAAIsD,gBAAgBlK,KAAK;QAC9BwK,YAAYS,GAAG,CAAC;YAAErE;QAAQ;IAC5B,OAAO,IAAIsD,gBAAgBjK,MAAM;QAC/BuK,YAAYU,IAAI,CAAC;YAAEtE;YAASuE,SAAS;QAAK;IAC5C;IAEA,MAAMC,kBAAkB,MAAMZ,YAAYa,QAAQ;IAElD,OAAOD;AACT;AAEO,eAAejM,mBAAmBgG,IAAY;IACnD,MAAMmG,MAAM,MAAMC,MAAMpG,MAAM;QAC5BqG,QAAQC,YAAYhB,OAAO,CAAC;IAC9B,GAAG9G,KAAK,CAAC,CAACqF,MAAQA;IAElB,IAAIsC,eAAe5J,OAAO;QACxB,MAAMsH,MAAMsC;QACZ,IAAItC,IAAI0C,IAAI,KAAK,gBAAgB;YAC/BrG,KAAI4D,KAAK,CAAC,yCAAyC9D;YACnD,MAAM,qBAGL,CAHK,IAAIpG,WACR,KACA,6DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,MAAMiK;IACR;IAEA,IAAI,CAACsC,IAAIK,EAAE,EAAE;QACXtG,KAAI4D,KAAK,CAAC,sCAAsC9D,MAAMmG,IAAIM,MAAM;QAChE,MAAM,qBAGL,CAHK,IAAI7M,WACRuM,IAAIM,MAAM,EACV,8DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;IAEA,MAAMzI,SAAST,OAAOC,IAAI,CAAC,MAAM2I,IAAIO,WAAW;IAChD,MAAM3B,cAAcoB,IAAIzE,OAAO,CAACO,GAAG,CAAC;IACpC,MAAMiB,eAAeiD,IAAIzE,OAAO,CAACO,GAAG,CAAC;IACrC,MAAM5E,OAAOtD,YAAYoM,IAAIzE,OAAO,CAACO,GAAG,CAAC,SAASjE;IAClD,OAAO;QAAEA;QAAQ+G;QAAa7B;QAAc7F;IAAK;AACnD;AAEO,eAAepD,mBACpB+F,IAAY,EACZ2G,IAAqB,EACrBC,IAAoB,EACpBC,aAIkB;IAElB,IAAI;QACF,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxClH,KAAKG;YACLgH,QAAQL,KAAKK,MAAM,IAAI;YACvBtF,SAASiF,KAAKjF,OAAO;YACrBuF,QAAQN,KAAKM,MAAM;QACrB;QAEA,MAAMJ,cAAcC,OAAO/H,GAAG,EAAE+H,OAAOX,GAAG,EAAEe,YAAO,CAACC,KAAK,CAACnH,MAAM;QAChE,MAAM8G,OAAOX,GAAG,CAACiB,WAAW;QAE5B,IAAI,CAACN,OAAOX,GAAG,CAACpC,UAAU,EAAE;YAC1B7D,KAAI4D,KAAK,CAAC,6BAA6B9D,MAAM8G,OAAOX,GAAG,CAACpC,UAAU;YAClE,MAAM,qBAGL,CAHK,IAAInK,WACRkN,OAAOX,GAAG,CAACpC,UAAU,EACrB,8DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,MAAM/F,SAAST,OAAO8J,MAAM,CAACP,OAAOX,GAAG,CAACmB,OAAO;QAC/C,MAAMvC,cAAc+B,OAAOX,GAAG,CAACoB,SAAS,CAAC;QACzC,MAAMrE,eAAe4D,OAAOX,GAAG,CAACoB,SAAS,CAAC;QAC1C,MAAMlK,OAAOtD,YAAY+M,OAAOX,GAAG,CAACoB,SAAS,CAAC,SAASvJ;QAEvD,OAAO;YAAEA;YAAQ+G;YAAa7B;YAAc7F;QAAK;IACnD,EAAE,OAAOwG,KAAK;QACZ3D,KAAI4D,KAAK,CAAC,sCAAsC9D,MAAM6D;QACtD,MAAM,qBAGL,CAHK,IAAIjK,WACR,KACA,8DAFI,qBAAA;mBAAA;wBAAA;0BAAA;QAGN;IACF;AACF;AAEO,eAAeY,eACpBgN,aAA4B,EAC5BC,YAGC,EACDxI,UAYC,EACDyI,IAIC;QAkBCF;IATF,MAAM,EAAExH,IAAI,EAAEyB,OAAO,EAAEN,KAAK,EAAExE,QAAQ,EAAE,GAAG8K;IAC3C,MAAM,EAAEzJ,QAAQ2J,cAAc,EAAEtK,MAAMY,YAAY,EAAE,GAAGuJ;IACvD,MAAM1J,SAAS7B,KAAKE,GAAG,CACrB8C,WAAWG,MAAM,CAACI,eAAe,EACjCnF,UAAUmN,cAActE,YAAY;IAGtC,MAAM0E,eACJ9N,kBAAkB6N,qBAClBH,6BAAAA,cAAczC,WAAW,qBAAzByC,2BAA2BhD,WAAW,GAAGD,IAAI;IAE/C,IAAIqD,cAAc;QAChB,IACEA,aAAarH,UAAU,CAAC,gBACxB,CAACtB,WAAWG,MAAM,CAACyI,mBAAmB,EACtC;YACA,IAAI,CAACH,KAAKI,MAAM,EAAE;gBAChB5H,KAAI4D,KAAK,CACP,CAAC,wBAAwB,EAAE9D,KAAK,YAAY,EAAE4H,aAAa,qCAAqC,CAAC;YAErG;YACA,MAAM,qBAGL,CAHK,IAAIhO,WACR,KACA,2DAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QACA,IAAI0B,iBAAiBuB,QAAQ,CAAC+K,iBAAiBG,IAAAA,mBAAU,EAACJ,iBAAiB;YACzE,IAAI,CAACD,KAAKI,MAAM,EAAE;gBAChB5H,KAAIC,QAAQ,CACV,CAAC,wBAAwB,EAAEH,KAAK,8GAA8G,CAAC;YAEnJ;YACA,OAAO;gBACLhC,QAAQ2J;gBACR5C,aAAa6C;gBACb9J;gBACAT,MAAMY;gBACNA;YACF;QACF;QACA,IAAI1C,aAAasB,QAAQ,CAAC+K,eAAe;YACvC,OAAO;gBACL5J,QAAQ2J;gBACR5C,aAAa6C;gBACb9J;gBACAT,MAAMY;gBACNA;YACF;QACF;QACA,IAAI,CAAC2J,aAAarH,UAAU,CAAC,aAAaqH,aAAa/K,QAAQ,CAAC,MAAM;YACpE,IAAI,CAAC6K,KAAKI,MAAM,EAAE;gBAChB5H,KAAI4D,KAAK,CACP,kDACA9D,MACA,YACA4H;YAEJ;YACA,MAAM,qBAAkE,CAAlE,IAAIhO,WAAW,KAAK,gDAApB,qBAAA;uBAAA;4BAAA;8BAAA;YAAiE;QACzE;IACF;IAEA,IAAImL;IAEJ,IAAIpI,UAAU;QACZoI,cAAcpI;IAChB,OAAO,IACLiL,CAAAA,gCAAAA,aAAcrH,UAAU,CAAC,cACzByH,IAAAA,yBAAY,EAACJ,iBACbA,iBAAiBhN,QACjBgN,iBAAiBjN,MACjB;QACAoK,cAAc6C;IAChB,OAAO;QACL7C,cAAcjK;IAChB;IACA,MAAMmN,wBAAwB3N,+BAC5BkN,eACAE,KAAK5C,kBAAkB;IAEzB,IAAImD,uBAAuB;YAIfP,uCAAAA;QAHV,OAAO;YACL1J,QAAQiK,sBAAsBjK,MAAM;YACpC+G;YACAjH,QAAQ4J,CAAAA,yBAAAA,2BAAAA,KAAM5C,kBAAkB,sBAAxB4C,wCAAAA,yBAA0BxE,YAAY,qBAAtCwE,sCAAwCvE,UAAU,KAAIrF;YAC9DT,MAAM4K,sBAAsB5K,IAAI;YAChCY,cAAcgK,sBAAsBhK,YAAY;QAClD;IACF;IAEA,IAAI;QACF,IAAIgI,kBAAkB,MAAMxL,cAAc;YACxCuD,QAAQ2J;YACR5C;YACAtD;YACAN;YACAxF,aAAasD,WAAWyE,YAAY,CAACwE,iBAAiB;YACtDjD,kBAAkBhG,WAAWyE,YAAY,CAACyE,oBAAoB;YAC9DjD,gBAAgBjG,WAAWyE,YAAY,CAAC0E,oBAAoB;YAC5DjD,kBAAkBlG,WAAWyE,YAAY,CAAC2E,sBAAsB;QAClE;QACA,IAAIX,KAAKxI,KAAK,IAAIiC,SAAS3F,iBAAiBiG,YAAYhG,cAAc;YACpE,8EAA8E;YAC9E,gFAAgF;YAChF,qFAAqF;YACrF,MAAM6M,OAAO,MAAMlO,aAAa6L;YAChC,MAAMsC,WAAW;gBACfC,WAAWF,KAAKnH,KAAK;gBACrBsH,YAAYH,KAAKtD,MAAM;gBACvB0D,aAAa,CAAC,KAAK,EAAE3D,YAAY,QAAQ,EAAEkB,gBAAgBxI,QAAQ,CACjE,WACC;YACL;YACAwI,kBAAkB1I,OAAOC,IAAI,CAACmL,SAASC,IAAAA,6BAAe,EAACL;YACvDxD,cAAc;QAChB;QACA,OAAO;YACL/G,QAAQiI;YACRlB;YACAjH;YACAT,MAAMlD,aAAa8L;YACnBhI;QACF;IACF,EAAE,OAAO6F,OAAO;QACd,IAAI8D,cAAc;YAChB,yDAAyD;YACzD,OAAO;gBACL5J,QAAQ2J;gBACR5C,aAAa6C;gBACb9J,QAAQmB,WAAWG,MAAM,CAACI,eAAe;gBACzCnC,MAAMY;gBACNA;gBACA6F;YACF;QACF,OAAO;YACL,MAAM,qBAGL,CAHK,IAAIlK,WACR,KACA,sEAFI,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;IACF;AACF;AAEA,SAASiP,yBACPhJ,GAAW,EACXkF,WAA0B;IAE1B,MAAM,CAAC+D,sBAAsB,GAAGjJ,IAAI6C,KAAK,CAAC,KAAK;IAC/C,MAAMqG,wBAAwBD,sBAAsBpG,KAAK,CAAC,KAAKsG,GAAG;IAClE,IAAI,CAACjE,eAAe,CAACgE,uBAAuB;QAC1C,OAAO;IACT;IAEA,MAAM,CAACE,SAAS,GAAGF,sBAAsBrG,KAAK,CAAC,KAAK;IACpD,MAAM7E,YAAYmK,IAAAA,yBAAY,EAACjD;IAC/B,OAAO,GAAGkE,SAAS,CAAC,EAAEpL,WAAW;AACnC;AAEA,SAASqL,mBACPnK,GAAoB,EACpBoH,GAAmB,EACnBtG,GAAW,EACXxC,IAAY,EACZ0H,WAA0B,EAC1BpD,QAAiB,EACjBwH,MAAoB,EACpBC,YAAiC,EACjCtL,MAAc,EACdoB,KAAc;IAEdiH,IAAIkD,SAAS,CAAC,QAAQ;IACtBlD,IAAIkD,SAAS,CACX,iBACA1H,WACI,yCACA,CAAC,gBAAgB,EAAEzC,QAAQ,IAAIpB,OAAO,iBAAiB,CAAC;IAE9D,IAAIwL,IAAAA,6BAAgB,EAACvK,KAAKoH,KAAK9I,OAAO;QACpC,6CAA6C;QAC7C,OAAO;YAAEkM,UAAU;QAAK;IAC1B;IACA,IAAIxE,aAAa;QACfoB,IAAIkD,SAAS,CAAC,gBAAgBtE;IAChC;IAEA,MAAMkE,WAAWJ,yBAAyBhJ,KAAKkF;IAC/CoB,IAAIkD,SAAS,CACX,uBACAG,IAAAA,2BAAkB,EAACP,UAAU;QAAEQ,MAAML,aAAaM,sBAAsB;IAAC;IAG3EvD,IAAIkD,SAAS,CAAC,2BAA2BD,aAAaO,qBAAqB;IAC3ExD,IAAIkD,SAAS,CAAC,kBAAkBF;IAEhC,OAAO;QAAEI,UAAU;IAAM;AAC3B;AAEO,SAAS7O,aACdqE,GAAoB,EACpBoH,GAAmB,EACnBtG,GAAW,EACXhC,SAAiB,EACjBG,MAAc,EACdX,IAAY,EACZsE,QAAiB,EACjBwH,MAAoB,EACpBC,YAAiC,EACjCtL,MAAc,EACdoB,KAAc;IAEd,MAAM6F,cAAc6E,IAAAA,2BAAc,EAAC/L;IACnC,MAAMgM,SAASX,mBACbnK,KACAoH,KACAtG,KACAxC,MACA0H,aACApD,UACAwH,QACAC,cACAtL,QACAoB;IAEF,IAAI,CAAC2K,OAAON,QAAQ,EAAE;QACpBpD,IAAIkD,SAAS,CAAC,kBAAkB9L,OAAOuM,UAAU,CAAC9L;QAClDmI,IAAI4D,GAAG,CAAC/L;IACV;AACF;AAEO,eAAe5D,aAAa4D,MAAc;IAI/C,MAAM,EAAEmD,KAAK,EAAE6D,MAAM,EAAE,GAAGgF,IAAAA,kBAAW,EAAChM;IACtC,OAAO;QAAEmD;QAAO6D;IAAO;AACzB"}