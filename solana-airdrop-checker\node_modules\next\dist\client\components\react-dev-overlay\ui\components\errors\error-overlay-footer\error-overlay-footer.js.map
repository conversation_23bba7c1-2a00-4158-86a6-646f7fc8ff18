{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-overlay-footer.tsx"], "sourcesContent": ["import { ErrorFeedback } from './error-feedback/error-feedback'\nimport { styles as feedbackStyles } from './error-feedback/error-feedback'\n\nexport type ErrorOverlayFooterProps = {\n  errorCode: string | undefined\n  footerMessage: string | undefined\n}\n\nexport function ErrorOverlayFooter({\n  errorCode,\n  footerMessage,\n}: ErrorOverlayFooterProps) {\n  return (\n    <footer className=\"error-overlay-footer\">\n      {footerMessage ? (\n        <p className=\"error-overlay-footer-message\">{footerMessage}</p>\n      ) : null}\n      {errorCode ? (\n        <ErrorFeedback className=\"error-feedback\" errorCode={errorCode} />\n      ) : null}\n    </footer>\n  )\n}\n\nexport const styles = `\n  .error-overlay-footer {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n\n    gap: 8px;\n    padding: 12px;\n    background: var(--color-background-200);\n    border-top: 1px solid var(--color-gray-400);\n  }\n\n  .error-feedback {\n    margin-left: auto;\n\n    p {\n      font-size: var(--size-14);\n      font-weight: 500;\n      margin: 0;\n    }\n  }\n\n  .error-overlay-footer-message {\n    color: var(--color-gray-900);\n    margin: 0;\n    font-size: var(--size-14);\n    font-weight: 400;\n    line-height: var(--size-20);\n  }\n\n  ${feedbackStyles}\n`\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "styles", "errorCode", "footerMessage", "footer", "className", "p", "ErrorFeedback", "feedbackStyles"], "mappings": ";;;;;;;;;;;;;;;IAQgBA,kBAAkB;eAAlBA;;IAgBHC,MAAM;eAANA;;;;+BAxBiB;AAQvB,SAASD,mBAAmB,KAGT;IAHS,IAAA,EACjCE,SAAS,EACTC,aAAa,EACW,GAHS;IAIjC,qBACE,sBAACC;QAAOC,WAAU;;YACfF,8BACC,qBAACG;gBAAED,WAAU;0BAAgCF;iBAC3C;YACHD,0BACC,qBAACK,4BAAa;gBAACF,WAAU;gBAAiBH,WAAWA;iBACnD;;;AAGV;AAEO,MAAMD,SAAS,AAAC,4kBA8BnBO,qBAAc,GAAC"}