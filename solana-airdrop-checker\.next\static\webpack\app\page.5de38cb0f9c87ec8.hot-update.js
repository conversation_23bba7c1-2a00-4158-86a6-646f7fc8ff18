"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/solana-api.ts":
/*!*******************************!*\
  !*** ./src/lib/solana-api.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WCTAirdropAPI: () => (/* binding */ WCTAirdropAPI),\n/* harmony export */   wctAirdropAPI: () => (/* binding */ wctAirdropAPI)\n/* harmony export */ });\n// WalletConnect Token (WCT) Airdrop API service\nclass WCTAirdropAPI {\n    async checkWCTEligibility(address) {\n        try {\n            const response = await fetch(\"\".concat(this.AIRDROP_API_URL, \"/\").concat(address), {\n                method: 'GET',\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            // Check if recipient not found (not eligible)\n            if (data.error && data.error.includes('not found')) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: undefined\n                };\n            }\n            // If there's any other error\n            if (data.error) {\n                return {\n                    address,\n                    balance: 0,\n                    isEligible: false,\n                    eligibleAmount: 0,\n                    error: data.error\n                };\n            }\n            // Extract eligible amount from response\n            const eligibleAmount = data.amount || data.value || data.eligibleAmount || data.tokens || 0;\n            return {\n                address,\n                balance: eligibleAmount,\n                isEligible: eligibleAmount > 0,\n                eligibleAmount,\n                error: undefined\n            };\n        } catch (error) {\n            return {\n                address,\n                balance: 0,\n                isEligible: false,\n                eligibleAmount: 0,\n                error: error instanceof Error ? error.message : 'Unknown error occurred'\n            };\n        }\n    }\n    async checkMultipleWCTEligibility(addresses) {\n        const promises = addresses.map((address)=>this.checkWCTEligibility(address));\n        return Promise.all(promises);\n    }\n    // Validate Solana address format\n    isValidSolanaAddress(address) {\n        // Basic validation for Solana address (base58, 32-44 characters)\n        const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;\n        return base58Regex.test(address);\n    }\n    constructor(){\n        this.AIRDROP_API_URL = 'https://api.walletconnect.network/airdrop/solana';\n    }\n}\nconst wctAirdropAPI = new WCTAirdropAPI();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/solana-api.ts\n"));

/***/ })

});