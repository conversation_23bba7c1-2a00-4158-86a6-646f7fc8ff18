// WalletConnect Token (WCT) Airdrop API service
export interface WCTAirdropResult {
  address: string;
  balance: number;
  isEligible: boolean;
  eligibleAmount: number;
  error?: string;
}

export interface SolanaRPCResponse {
  jsonrpc: string;
  result?: {
    context: {
      apiVersion: string;
      slot: number;
    };
    value: number;
  };
  error?: {
    code: number;
    message: string;
  };
  id: string;
}

export class WCTAirdropAPI {
  async checkWCTEligibility(address: string): Promise<WCTAirdropResult> {
    try {
      const response = await fetch(`/api/wct-airdrop/${address}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      return {
        address: data.address,
        balance: data.eligibleAmount,
        isEligible: data.isEligible,
        eligibleAmount: data.eligibleAmount,
        error: data.error
      };

    } catch (error) {
      console.error('WCT Airdrop API Error:', error);

      // Handle network errors or CORS issues
      if (error instanceof TypeError && error.message.includes('fetch')) {
        return {
          address,
          balance: 0,
          isEligible: false,
          eligibleAmount: 0,
          error: 'Network error - please check your connection'
        };
      }

      return {
        address,
        balance: 0,
        isEligible: false,
        eligibleAmount: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async checkMultipleWCTEligibility(addresses: string[]): Promise<WCTAirdropResult[]> {
    const promises = addresses.map(address => this.checkWCTEligibility(address));
    return Promise.all(promises);
  }

  // Validate Solana address format
  isValidSolanaAddress(address: string): boolean {
    // Basic validation for Solana address (base58, 32-44 characters)
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    return base58Regex.test(address);
  }
}

export const wctAirdropAPI = new WCTAirdropAPI();
