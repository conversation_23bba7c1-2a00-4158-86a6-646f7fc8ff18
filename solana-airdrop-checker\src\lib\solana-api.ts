// WalletConnect Token (WCT) Airdrop API service
export interface WCTAirdropResult {
  address: string;
  balance: number;
  isEligible: boolean;
  eligibleAmount: number;
  error?: string;
}

export interface SolanaRPCResponse {
  jsonrpc: string;
  result?: {
    context: {
      apiVersion: string;
      slot: number;
    };
    value: number;
  };
  error?: {
    code: number;
    message: string;
  };
  id: string;
}

export class WCTAirdropAPI {
  private readonly RPC_URL = 'https://rpc.walletconnect.org/v1/?chainId=solana%**********************************&projectId=b366e97e24223af7a0c0ad4003303698';

  private generateId(): string {
    return crypto.randomUUID();
  }

  async checkWCTEligibility(address: string): Promise<WCTAirdropResult> {
    try {
      const requestBody = {
        method: "getBalance",
        jsonrpc: "2.0",
        params: [address, { commitment: "confirmed" }],
        id: this.generateId()
      };

      const response = await fetch(this.RPC_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*',
          'solana-client': 'js/1.0.0-maintenance'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: SolanaRPCResponse = await response.json();

      if (data.error) {
        return {
          address,
          balance: 0,
          isEligible: false,
          eligibleAmount: 0,
          error: data.error.message
        };
      }

      const wctAmount = data.result?.value || 0;

      return {
        address,
        balance: wctAmount, // Keep for compatibility
        isEligible: wctAmount > 0,
        eligibleAmount: wctAmount, // Direct WCT amount from API
        error: undefined
      };

    } catch (error) {
      return {
        address,
        balance: 0,
        isEligible: false,
        eligibleAmount: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async checkMultipleWCTEligibility(addresses: string[]): Promise<WCTAirdropResult[]> {
    const promises = addresses.map(address => this.checkWCTEligibility(address));
    return Promise.all(promises);
  }

  // Validate Solana address format
  isValidSolanaAddress(address: string): boolean {
    // Basic validation for Solana address (base58, 32-44 characters)
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    return base58Regex.test(address);
  }
}

export const wctAirdropAPI = new WCTAirdropAPI();
