// WalletConnect Token (WCT) Airdrop API service
export interface WCTAirdropResult {
  address: string;
  balance: number;
  isEligible: boolean;
  eligibleAmount: number;
  error?: string;
}

export interface SolanaRPCResponse {
  jsonrpc: string;
  result?: {
    context: {
      apiVersion: string;
      slot: number;
    };
    value: number;
  };
  error?: {
    code: number;
    message: string;
  };
  id: string;
}

export class WCTAirdropAPI {
  private readonly AIRDROP_API_URL = 'https://api.walletconnect.network/airdrop/solana';

  async checkWCTEligibility(address: string): Promise<WCTAirdropResult> {
    try {
      const response = await fetch(`${this.AIRDROP_API_URL}/${address}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Check if recipient not found (not eligible)
      if (data.error && data.error.includes('not found')) {
        return {
          address,
          balance: 0,
          isEligible: false,
          eligibleAmount: 0,
          error: undefined
        };
      }

      // If there's any other error
      if (data.error) {
        return {
          address,
          balance: 0,
          isEligible: false,
          eligibleAmount: 0,
          error: data.error
        };
      }

      // Extract eligible amount from response (hex format)
      let eligibleAmount = 0;
      if (data.amount) {
        // Convert hex amount to decimal and then to WCT tokens
        const hexAmount = data.amount.startsWith('0x') ? data.amount : '0x' + data.amount;
        const decimalAmount = parseInt(hexAmount, 16);
        // Convert from smallest unit to WCT tokens (9 decimals like SOL)
        eligibleAmount = decimalAmount / Math.pow(10, 9);
      }

      return {
        address,
        balance: eligibleAmount,
        isEligible: eligibleAmount > 0,
        eligibleAmount,
        error: undefined
      };

    } catch (error) {
      return {
        address,
        balance: 0,
        isEligible: false,
        eligibleAmount: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async checkMultipleWCTEligibility(addresses: string[]): Promise<WCTAirdropResult[]> {
    const promises = addresses.map(address => this.checkWCTEligibility(address));
    return Promise.all(promises);
  }

  // Validate Solana address format
  isValidSolanaAddress(address: string): boolean {
    // Basic validation for Solana address (base58, 32-44 characters)
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    return base58Regex.test(address);
  }
}

export const wctAirdropAPI = new WCTAirdropAPI();
