{"version": 3, "sources": ["../../../src/build/webpack-build/impl.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { stringBufferUtils } from 'next/dist/compiled/webpack-sources3'\nimport { red } from '../../lib/picocolors'\nimport formatWebpackMessages from '../../client/components/react-dev-overlay/utils/format-webpack-messages'\nimport { nonNullable } from '../../lib/non-nullable'\nimport type { COMPILER_INDEXES } from '../../shared/lib/constants'\nimport {\n  COMPILER_NAMES,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  APP_CLIENT_INTERNALS,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport { runCompiler } from '../compiler'\nimport * as Log from '../output/log'\nimport getBaseWebpackConfig, { loadProjectInfo } from '../webpack-config'\nimport type { NextError } from '../../lib/is-error'\nimport {\n  TelemetryPlugin,\n  type TelemetryPluginState,\n} from '../webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport {\n  NextBuildContext,\n  resumePluginState,\n  getPluginState,\n} from '../build-context'\nimport { createEntrypoints } from '../entries'\nimport loadConfig from '../../server/config'\nimport {\n  getTraceEvents,\n  initializeTraceState,\n  setGlobal,\n  trace,\n  type TraceEvent,\n  type TraceState,\n} from '../../trace'\nimport { WEBPACK_LAYERS } from '../../lib/constants'\nimport { TraceEntryPointsPlugin } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport type { BuildTraceContext } from '../webpack/plugins/next-trace-entrypoints-plugin'\nimport type { UnwrapPromise } from '../../lib/coalesced-function'\n\nimport origDebug from 'next/dist/compiled/debug'\nimport { Telemetry } from '../../telemetry/storage'\nimport { durationToString } from '../duration-to-string'\n\nconst debug = origDebug('next:build:webpack-build')\n\ntype CompilerResult = {\n  errors: webpack.StatsError[]\n  warnings: webpack.StatsError[]\n  stats: (webpack.Stats | undefined)[]\n}\n\ntype SingleCompilerResult = {\n  errors: webpack.StatsError[]\n  warnings: webpack.StatsError[]\n  stats: webpack.Stats | undefined\n}\n\nfunction isTelemetryPlugin(plugin: unknown): plugin is TelemetryPlugin {\n  return plugin instanceof TelemetryPlugin\n}\n\nfunction isTraceEntryPointsPlugin(\n  plugin: unknown\n): plugin is TraceEntryPointsPlugin {\n  return plugin instanceof TraceEntryPointsPlugin\n}\n\nexport async function webpackBuildImpl(\n  compilerName: keyof typeof COMPILER_INDEXES | null\n): Promise<{\n  duration: number\n  pluginState: any\n  buildTraceContext?: BuildTraceContext\n  telemetryState?: TelemetryPluginState\n}> {\n  let result: CompilerResult | null = {\n    warnings: [],\n    errors: [],\n    stats: [],\n  }\n  let webpackBuildStart\n  const nextBuildSpan = NextBuildContext.nextBuildSpan!\n  const dir = NextBuildContext.dir!\n  const config = NextBuildContext.config!\n  process.env.NEXT_COMPILER_NAME = compilerName || 'server'\n\n  const runWebpackSpan = nextBuildSpan.traceChild('run-webpack-compiler')\n  const entrypoints = await nextBuildSpan\n    .traceChild('create-entrypoints')\n    .traceAsyncFn(() =>\n      createEntrypoints({\n        buildId: NextBuildContext.buildId!,\n        config: config,\n        envFiles: NextBuildContext.loadedEnvFiles!,\n        isDev: false,\n        rootDir: dir,\n        pageExtensions: config.pageExtensions!,\n        pagesDir: NextBuildContext.pagesDir!,\n        appDir: NextBuildContext.appDir!,\n        pages: NextBuildContext.mappedPages!,\n        appPaths: NextBuildContext.mappedAppPages!,\n        previewMode: NextBuildContext.previewProps!,\n        rootPaths: NextBuildContext.mappedRootPaths!,\n        hasInstrumentationHook: NextBuildContext.hasInstrumentationHook!,\n      })\n    )\n\n  const commonWebpackOptions = {\n    isServer: false,\n    isCompileMode: NextBuildContext.isCompileMode,\n    buildId: NextBuildContext.buildId!,\n    encryptionKey: NextBuildContext.encryptionKey!,\n    config: config,\n    appDir: NextBuildContext.appDir!,\n    pagesDir: NextBuildContext.pagesDir!,\n    rewrites: NextBuildContext.rewrites!,\n    originalRewrites: NextBuildContext.originalRewrites,\n    originalRedirects: NextBuildContext.originalRedirects,\n    reactProductionProfiling: NextBuildContext.reactProductionProfiling!,\n    noMangling: NextBuildContext.noMangling!,\n    clientRouterFilters: NextBuildContext.clientRouterFilters!,\n    previewModeId: NextBuildContext.previewModeId!,\n    allowedRevalidateHeaderKeys: NextBuildContext.allowedRevalidateHeaderKeys!,\n    fetchCacheKeyPrefix: NextBuildContext.fetchCacheKeyPrefix!,\n  }\n\n  const configs = await runWebpackSpan\n    .traceChild('generate-webpack-config')\n    .traceAsyncFn(async () => {\n      const info = await loadProjectInfo({\n        dir,\n        config: commonWebpackOptions.config,\n        dev: false,\n      })\n      return Promise.all([\n        getBaseWebpackConfig(dir, {\n          ...commonWebpackOptions,\n          middlewareMatchers: entrypoints.middlewareMatchers,\n          runWebpackSpan,\n          compilerType: COMPILER_NAMES.client,\n          entrypoints: entrypoints.client,\n          ...info,\n        }),\n        getBaseWebpackConfig(dir, {\n          ...commonWebpackOptions,\n          runWebpackSpan,\n          middlewareMatchers: entrypoints.middlewareMatchers,\n          compilerType: COMPILER_NAMES.server,\n          entrypoints: entrypoints.server,\n          ...info,\n        }),\n        getBaseWebpackConfig(dir, {\n          ...commonWebpackOptions,\n          runWebpackSpan,\n          middlewareMatchers: entrypoints.middlewareMatchers,\n          compilerType: COMPILER_NAMES.edgeServer,\n          entrypoints: entrypoints.edgeServer,\n          edgePreviewProps: {\n            __NEXT_PREVIEW_MODE_ID:\n              NextBuildContext.previewProps!.previewModeId,\n            __NEXT_PREVIEW_MODE_ENCRYPTION_KEY:\n              NextBuildContext.previewProps!.previewModeEncryptionKey,\n            __NEXT_PREVIEW_MODE_SIGNING_KEY:\n              NextBuildContext.previewProps!.previewModeSigningKey,\n          },\n          ...info,\n        }),\n      ])\n    })\n\n  const clientConfig = configs[0]\n  const serverConfig = configs[1]\n  const edgeConfig = configs[2]\n\n  if (\n    clientConfig.optimization &&\n    (clientConfig.optimization.minimize !== true ||\n      (clientConfig.optimization.minimizer &&\n        clientConfig.optimization.minimizer.length === 0))\n  ) {\n    Log.warn(\n      `Production code optimization has been disabled in your project. Read more: https://nextjs.org/docs/messages/minification-disabled`\n    )\n  }\n\n  webpackBuildStart = process.hrtime()\n\n  debug(`starting compiler`, compilerName)\n  // We run client and server compilation separately to optimize for memory usage\n  await runWebpackSpan.traceAsyncFn(async () => {\n    if (config.experimental.webpackMemoryOptimizations) {\n      stringBufferUtils.disableDualStringBufferCaching()\n      stringBufferUtils.enterStringInterningRange()\n    }\n\n    // Run the server compilers first and then the client\n    // compiler to track the boundary of server/client components.\n    let clientResult: SingleCompilerResult | null = null\n\n    // During the server compilations, entries of client components will be\n    // injected to this set and then will be consumed by the client compiler.\n    let serverResult: UnwrapPromise<ReturnType<typeof runCompiler>>[0] | null =\n      null\n    let edgeServerResult:\n      | UnwrapPromise<ReturnType<typeof runCompiler>>[0]\n      | null = null\n\n    let inputFileSystem: webpack.Compiler['inputFileSystem'] | undefined\n\n    if (!compilerName || compilerName === 'server') {\n      debug('starting server compiler')\n      const start = Date.now()\n      ;[serverResult, inputFileSystem] = await runCompiler(serverConfig, {\n        runWebpackSpan,\n        inputFileSystem,\n      })\n      debug(`server compiler finished ${Date.now() - start}ms`)\n    }\n\n    if (!compilerName || compilerName === 'edge-server') {\n      debug('starting edge-server compiler')\n      const start = Date.now()\n      ;[edgeServerResult, inputFileSystem] = edgeConfig\n        ? await runCompiler(edgeConfig, { runWebpackSpan, inputFileSystem })\n        : [null]\n      debug(`edge-server compiler finished ${Date.now() - start}ms`)\n    }\n\n    // Only continue if there were no errors\n    if (!serverResult?.errors.length && !edgeServerResult?.errors.length) {\n      const pluginState = getPluginState()\n      for (const key in pluginState.injectedClientEntries) {\n        const value = pluginState.injectedClientEntries[key]\n        const clientEntry = clientConfig.entry as webpack.EntryObject\n        if (key === APP_CLIENT_INTERNALS) {\n          clientEntry[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP] = {\n            import: [\n              // TODO-APP: cast clientEntry[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP] to type EntryDescription once it's available from webpack\n              // @ts-expect-error clientEntry['main-app'] is type EntryDescription { import: ... }\n              ...clientEntry[CLIENT_STATIC_FILES_RUNTIME_MAIN_APP].import,\n              value,\n            ],\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n          }\n        } else {\n          clientEntry[key] = {\n            dependOn: [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP],\n            import: value,\n            layer: WEBPACK_LAYERS.appPagesBrowser,\n          }\n        }\n      }\n\n      if (!compilerName || compilerName === 'client') {\n        debug('starting client compiler')\n        const start = Date.now()\n        ;[clientResult, inputFileSystem] = await runCompiler(clientConfig, {\n          runWebpackSpan,\n          inputFileSystem,\n        })\n        debug(`client compiler finished ${Date.now() - start}ms`)\n      }\n    }\n\n    if (config.experimental.webpackMemoryOptimizations) {\n      stringBufferUtils.exitStringInterningRange()\n    }\n    inputFileSystem?.purge?.()\n\n    result = {\n      warnings: [\n        ...(clientResult?.warnings ?? []),\n        ...(serverResult?.warnings ?? []),\n        ...(edgeServerResult?.warnings ?? []),\n      ].filter(nonNullable),\n      errors: [\n        ...(clientResult?.errors ?? []),\n        ...(serverResult?.errors ?? []),\n        ...(edgeServerResult?.errors ?? []),\n      ].filter(nonNullable),\n      stats: [\n        clientResult?.stats,\n        serverResult?.stats,\n        edgeServerResult?.stats,\n      ],\n    }\n  })\n  result = nextBuildSpan\n    .traceChild('format-webpack-messages')\n    .traceFn(() => formatWebpackMessages(result, true)) as CompilerResult\n\n  const telemetryPlugin = (clientConfig as webpack.Configuration).plugins?.find(\n    isTelemetryPlugin\n  )\n\n  const traceEntryPointsPlugin = (\n    serverConfig as webpack.Configuration\n  ).plugins?.find(isTraceEntryPointsPlugin)\n\n  const webpackBuildEnd = process.hrtime(webpackBuildStart)\n\n  if (result.errors.length > 0) {\n    // Only keep the first few errors. Others are often indicative\n    // of the same problem, but confuse the reader with noise.\n    if (result.errors.length > 5) {\n      result.errors.length = 5\n    }\n    let error = result.errors.filter(Boolean).join('\\n\\n')\n\n    console.error(red('Failed to compile.\\n'))\n\n    if (\n      error.indexOf('private-next-pages') > -1 &&\n      error.indexOf('does not contain a default export') > -1\n    ) {\n      const page_name_regex = /'private-next-pages\\/(?<page_name>[^']*)'/\n      const parsed = page_name_regex.exec(error)\n      const page_name = parsed && parsed.groups && parsed.groups.page_name\n      throw new Error(\n        `webpack build failed: found page without a React Component as default export in pages/${page_name}\\n\\nSee https://nextjs.org/docs/messages/page-without-valid-component for more info.`\n      )\n    }\n\n    console.error(error)\n    console.error()\n\n    if (\n      error.indexOf('private-next-pages') > -1 ||\n      error.indexOf('__next_polyfill__') > -1\n    ) {\n      const err = new Error(\n        'webpack config.resolve.alias was incorrectly overridden. https://nextjs.org/docs/messages/invalid-resolve-alias'\n      ) as NextError\n      err.code = 'INVALID_RESOLVE_ALIAS'\n      throw err\n    }\n    const err = new Error(\n      `Build failed because of ${process.env.NEXT_RSPACK ? 'rspack' : 'webpack'} errors`\n    ) as NextError\n    err.code = 'WEBPACK_ERRORS'\n    throw err\n  } else {\n    const duration = webpackBuildEnd[0]\n    const durationString = durationToString(duration)\n\n    if (result.warnings.length > 0) {\n      Log.warn(`Compiled with warnings in ${durationString}\\n`)\n      console.warn(result.warnings.filter(Boolean).join('\\n\\n'))\n      console.warn()\n    } else if (!compilerName) {\n      Log.event(`Compiled successfully in ${durationString}`)\n    }\n\n    return {\n      duration: webpackBuildEnd[0],\n      buildTraceContext: traceEntryPointsPlugin?.buildTraceContext,\n      pluginState: getPluginState(),\n      telemetryState: {\n        usages: telemetryPlugin?.usages() || [],\n        packagesUsedInServerSideProps:\n          telemetryPlugin?.packagesUsedInServerSideProps() || [],\n        useCacheTracker: telemetryPlugin?.getUseCacheTracker() || {},\n      },\n    }\n  }\n}\n\n// the main function when this file is run as a worker\nexport async function workerMain(workerData: {\n  compilerName: keyof typeof COMPILER_INDEXES\n  buildContext: typeof NextBuildContext\n  traceState: TraceState\n}): Promise<\n  Awaited<ReturnType<typeof webpackBuildImpl>> & {\n    debugTraceEvents: TraceEvent[]\n  }\n> {\n  // Clone the telemetry for worker\n  const telemetry = new Telemetry({\n    distDir: workerData.buildContext.config!.distDir,\n  })\n  setGlobal('telemetry', telemetry)\n  // setup new build context from the serialized data passed from the parent\n  Object.assign(NextBuildContext, workerData.buildContext)\n\n  // Initialize tracer state from the parent\n  initializeTraceState(workerData.traceState)\n\n  // Resume plugin state\n  resumePluginState(NextBuildContext.pluginState)\n\n  /// load the config because it's not serializable\n  NextBuildContext.config = await loadConfig(\n    PHASE_PRODUCTION_BUILD,\n    NextBuildContext.dir!\n  )\n  NextBuildContext.nextBuildSpan = trace(\n    `worker-main-${workerData.compilerName}`\n  )\n\n  const result = await webpackBuildImpl(workerData.compilerName)\n  const { entriesTrace, chunksTrace } = result.buildTraceContext ?? {}\n  if (entriesTrace) {\n    const { entryNameMap, depModArray } = entriesTrace\n    if (depModArray) {\n      result.buildTraceContext!.entriesTrace!.depModArray = depModArray\n    }\n    if (entryNameMap) {\n      const entryEntries = entryNameMap\n      result.buildTraceContext!.entriesTrace!.entryNameMap = entryEntries\n    }\n  }\n  if (chunksTrace?.entryNameFilesMap) {\n    const entryNameFilesMap = chunksTrace.entryNameFilesMap\n    result.buildTraceContext!.chunksTrace!.entryNameFilesMap = entryNameFilesMap\n  }\n  NextBuildContext.nextBuildSpan.stop()\n  return { ...result, debugTraceEvents: getTraceEvents() }\n}\n"], "names": ["string<PERSON><PERSON><PERSON><PERSON><PERSON>s", "red", "formatWebpackMessages", "nonNullable", "COMPILER_NAMES", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "PHASE_PRODUCTION_BUILD", "runCompiler", "Log", "getBaseWebpackConfig", "loadProjectInfo", "TelemetryPlugin", "NextBuildContext", "resumePluginState", "getPluginState", "createEntrypoints", "loadConfig", "getTraceEvents", "initializeTraceState", "setGlobal", "trace", "WEBPACK_LAYERS", "TraceEntryPointsPlugin", "origDebug", "Telemetry", "durationToString", "debug", "isTelemetryPlugin", "plugin", "isTraceEntryPointsPlugin", "webpackBuildImpl", "compilerName", "result", "warnings", "errors", "stats", "webpackBuildStart", "nextBuildSpan", "dir", "config", "process", "env", "NEXT_COMPILER_NAME", "runWebpackSpan", "<PERSON><PERSON><PERSON><PERSON>", "entrypoints", "traceAsyncFn", "buildId", "envFiles", "loadedEnvFiles", "isDev", "rootDir", "pageExtensions", "pagesDir", "appDir", "pages", "mappedPages", "appPaths", "mappedAppPages", "previewMode", "previewProps", "rootPaths", "mappedRootPaths", "hasInstrumentationHook", "commonWebpackOptions", "isServer", "isCompileMode", "<PERSON><PERSON><PERSON>", "rewrites", "originalRewrites", "originalRedirects", "reactProductionProfiling", "noMangling", "clientRouterFilters", "previewModeId", "allowedRevalidateHeaderKeys", "fetchCacheKeyPrefix", "configs", "info", "dev", "Promise", "all", "middlewareMatchers", "compilerType", "client", "server", "edgeServer", "edgePreviewProps", "__NEXT_PREVIEW_MODE_ID", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY", "previewModeEncryptionKey", "__NEXT_PREVIEW_MODE_SIGNING_KEY", "previewModeSigningKey", "clientConfig", "serverConfig", "edgeConfig", "optimization", "minimize", "minimizer", "length", "warn", "hrtime", "inputFileSystem", "experimental", "webpackMemoryOptimizations", "disableDualStringBufferCaching", "enterStringInterningRange", "clientResult", "serverResult", "edgeServerResult", "start", "Date", "now", "pluginState", "key", "injectedClientEntries", "value", "clientEntry", "entry", "import", "layer", "appPagesBrowser", "dependOn", "exitStringInterningRange", "purge", "filter", "traceFn", "telemetryPlugin", "plugins", "find", "traceEntryPointsPlugin", "webpackBuildEnd", "error", "Boolean", "join", "console", "indexOf", "page_name_regex", "parsed", "exec", "page_name", "groups", "Error", "err", "code", "NEXT_RSPACK", "duration", "durationString", "event", "buildTraceContext", "telemetryState", "usages", "packagesUsedInServerSideProps", "useCacheTracker", "getUseCacheTracker", "worker<PERSON>ain", "workerData", "telemetry", "distDir", "buildContext", "Object", "assign", "traceState", "entriesTrace", "chunksTrace", "entryNameMap", "depModArray", "entryEntries", "entryNameFilesMap", "stop", "debugTraceEvents"], "mappings": "AACA,SAASA,iBAAiB,QAAQ,sCAAqC;AACvE,SAASC,GAAG,QAAQ,uBAAsB;AAC1C,OAAOC,2BAA2B,0EAAyE;AAC3G,SAASC,WAAW,QAAQ,yBAAwB;AAEpD,SACEC,cAAc,EACdC,oCAAoC,EACpCC,oBAAoB,EACpBC,sBAAsB,QACjB,6BAA4B;AACnC,SAASC,WAAW,QAAQ,cAAa;AACzC,YAAYC,SAAS,gBAAe;AACpC,OAAOC,wBAAwBC,eAAe,QAAQ,oBAAmB;AAEzE,SACEC,eAAe,QAEV,uDAAsD;AAC7D,SACEC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,QACT,mBAAkB;AACzB,SAASC,iBAAiB,QAAQ,aAAY;AAC9C,OAAOC,gBAAgB,sBAAqB;AAC5C,SACEC,cAAc,EACdC,oBAAoB,EACpBC,SAAS,EACTC,KAAK,QAGA,cAAa;AACpB,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,sBAAsB,QAAQ,mDAAkD;AAIzF,OAAOC,eAAe,2BAA0B;AAChD,SAASC,SAAS,QAAQ,0BAAyB;AACnD,SAASC,gBAAgB,QAAQ,wBAAuB;AAExD,MAAMC,QAAQH,UAAU;AAcxB,SAASI,kBAAkBC,MAAe;IACxC,OAAOA,kBAAkBjB;AAC3B;AAEA,SAASkB,yBACPD,MAAe;IAEf,OAAOA,kBAAkBN;AAC3B;AAEA,OAAO,eAAeQ,iBACpBC,YAAkD;QA+N1B,uBAIO;IA5N/B,IAAIC,SAAgC;QAClCC,UAAU,EAAE;QACZC,QAAQ,EAAE;QACVC,OAAO,EAAE;IACX;IACA,IAAIC;IACJ,MAAMC,gBAAgBzB,iBAAiByB,aAAa;IACpD,MAAMC,MAAM1B,iBAAiB0B,GAAG;IAChC,MAAMC,SAAS3B,iBAAiB2B,MAAM;IACtCC,QAAQC,GAAG,CAACC,kBAAkB,GAAGX,gBAAgB;IAEjD,MAAMY,iBAAiBN,cAAcO,UAAU,CAAC;IAChD,MAAMC,cAAc,MAAMR,cACvBO,UAAU,CAAC,sBACXE,YAAY,CAAC,IACZ/B,kBAAkB;YAChBgC,SAASnC,iBAAiBmC,OAAO;YACjCR,QAAQA;YACRS,UAAUpC,iBAAiBqC,cAAc;YACzCC,OAAO;YACPC,SAASb;YACTc,gBAAgBb,OAAOa,cAAc;YACrCC,UAAUzC,iBAAiByC,QAAQ;YACnCC,QAAQ1C,iBAAiB0C,MAAM;YAC/BC,OAAO3C,iBAAiB4C,WAAW;YACnCC,UAAU7C,iBAAiB8C,cAAc;YACzCC,aAAa/C,iBAAiBgD,YAAY;YAC1CC,WAAWjD,iBAAiBkD,eAAe;YAC3CC,wBAAwBnD,iBAAiBmD,sBAAsB;QACjE;IAGJ,MAAMC,uBAAuB;QAC3BC,UAAU;QACVC,eAAetD,iBAAiBsD,aAAa;QAC7CnB,SAASnC,iBAAiBmC,OAAO;QACjCoB,eAAevD,iBAAiBuD,aAAa;QAC7C5B,QAAQA;QACRe,QAAQ1C,iBAAiB0C,MAAM;QAC/BD,UAAUzC,iBAAiByC,QAAQ;QACnCe,UAAUxD,iBAAiBwD,QAAQ;QACnCC,kBAAkBzD,iBAAiByD,gBAAgB;QACnDC,mBAAmB1D,iBAAiB0D,iBAAiB;QACrDC,0BAA0B3D,iBAAiB2D,wBAAwB;QACnEC,YAAY5D,iBAAiB4D,UAAU;QACvCC,qBAAqB7D,iBAAiB6D,mBAAmB;QACzDC,eAAe9D,iBAAiB8D,aAAa;QAC7CC,6BAA6B/D,iBAAiB+D,2BAA2B;QACzEC,qBAAqBhE,iBAAiBgE,mBAAmB;IAC3D;IAEA,MAAMC,UAAU,MAAMlC,eACnBC,UAAU,CAAC,2BACXE,YAAY,CAAC;QACZ,MAAMgC,OAAO,MAAMpE,gBAAgB;YACjC4B;YACAC,QAAQyB,qBAAqBzB,MAAM;YACnCwC,KAAK;QACP;QACA,OAAOC,QAAQC,GAAG,CAAC;YACjBxE,qBAAqB6B,KAAK;gBACxB,GAAG0B,oBAAoB;gBACvBkB,oBAAoBrC,YAAYqC,kBAAkB;gBAClDvC;gBACAwC,cAAchF,eAAeiF,MAAM;gBACnCvC,aAAaA,YAAYuC,MAAM;gBAC/B,GAAGN,IAAI;YACT;YACArE,qBAAqB6B,KAAK;gBACxB,GAAG0B,oBAAoB;gBACvBrB;gBACAuC,oBAAoBrC,YAAYqC,kBAAkB;gBAClDC,cAAchF,eAAekF,MAAM;gBACnCxC,aAAaA,YAAYwC,MAAM;gBAC/B,GAAGP,IAAI;YACT;YACArE,qBAAqB6B,KAAK;gBACxB,GAAG0B,oBAAoB;gBACvBrB;gBACAuC,oBAAoBrC,YAAYqC,kBAAkB;gBAClDC,cAAchF,eAAemF,UAAU;gBACvCzC,aAAaA,YAAYyC,UAAU;gBACnCC,kBAAkB;oBAChBC,wBACE5E,iBAAiBgD,YAAY,CAAEc,aAAa;oBAC9Ce,oCACE7E,iBAAiBgD,YAAY,CAAE8B,wBAAwB;oBACzDC,iCACE/E,iBAAiBgD,YAAY,CAAEgC,qBAAqB;gBACxD;gBACA,GAAGd,IAAI;YACT;SACD;IACH;IAEF,MAAMe,eAAehB,OAAO,CAAC,EAAE;IAC/B,MAAMiB,eAAejB,OAAO,CAAC,EAAE;IAC/B,MAAMkB,aAAalB,OAAO,CAAC,EAAE;IAE7B,IACEgB,aAAaG,YAAY,IACxBH,CAAAA,aAAaG,YAAY,CAACC,QAAQ,KAAK,QACrCJ,aAAaG,YAAY,CAACE,SAAS,IAClCL,aAAaG,YAAY,CAACE,SAAS,CAACC,MAAM,KAAK,CAAC,GACpD;QACA3F,IAAI4F,IAAI,CACN,CAAC,iIAAiI,CAAC;IAEvI;IAEAhE,oBAAoBI,QAAQ6D,MAAM;IAElC3E,MAAM,CAAC,iBAAiB,CAAC,EAAEK;IAC3B,+EAA+E;IAC/E,MAAMY,eAAeG,YAAY,CAAC;YA8EhCwD;QA7EA,IAAI/D,OAAOgE,YAAY,CAACC,0BAA0B,EAAE;YAClDzG,kBAAkB0G,8BAA8B;YAChD1G,kBAAkB2G,yBAAyB;QAC7C;QAEA,qDAAqD;QACrD,8DAA8D;QAC9D,IAAIC,eAA4C;QAEhD,uEAAuE;QACvE,yEAAyE;QACzE,IAAIC,eACF;QACF,IAAIC,mBAEO;QAEX,IAAIP;QAEJ,IAAI,CAACvE,gBAAgBA,iBAAiB,UAAU;YAC9CL,MAAM;YACN,MAAMoF,QAAQC,KAAKC,GAAG;YACrB,CAACJ,cAAcN,gBAAgB,GAAG,MAAM/F,YAAYuF,cAAc;gBACjEnD;gBACA2D;YACF;YACA5E,MAAM,CAAC,yBAAyB,EAAEqF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC1D;QAEA,IAAI,CAAC/E,gBAAgBA,iBAAiB,eAAe;YACnDL,MAAM;YACN,MAAMoF,QAAQC,KAAKC,GAAG;YACrB,CAACH,kBAAkBP,gBAAgB,GAAGP,aACnC,MAAMxF,YAAYwF,YAAY;gBAAEpD;gBAAgB2D;YAAgB,KAChE;gBAAC;aAAK;YACV5E,MAAM,CAAC,8BAA8B,EAAEqF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;QAC/D;QAEA,wCAAwC;QACxC,IAAI,EAACF,gCAAAA,aAAc1E,MAAM,CAACiE,MAAM,KAAI,EAACU,oCAAAA,iBAAkB3E,MAAM,CAACiE,MAAM,GAAE;YACpE,MAAMc,cAAcnG;YACpB,IAAK,MAAMoG,OAAOD,YAAYE,qBAAqB,CAAE;gBACnD,MAAMC,QAAQH,YAAYE,qBAAqB,CAACD,IAAI;gBACpD,MAAMG,cAAcxB,aAAayB,KAAK;gBACtC,IAAIJ,QAAQ7G,sBAAsB;oBAChCgH,WAAW,CAACjH,qCAAqC,GAAG;wBAClDmH,QAAQ;4BACN,6HAA6H;4BAC7H,oFAAoF;+BACjFF,WAAW,CAACjH,qCAAqC,CAACmH,MAAM;4BAC3DH;yBACD;wBACDI,OAAOnG,eAAeoG,eAAe;oBACvC;gBACF,OAAO;oBACLJ,WAAW,CAACH,IAAI,GAAG;wBACjBQ,UAAU;4BAACtH;yBAAqC;wBAChDmH,QAAQH;wBACRI,OAAOnG,eAAeoG,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,CAAC1F,gBAAgBA,iBAAiB,UAAU;gBAC9CL,MAAM;gBACN,MAAMoF,QAAQC,KAAKC,GAAG;gBACrB,CAACL,cAAcL,gBAAgB,GAAG,MAAM/F,YAAYsF,cAAc;oBACjElD;oBACA2D;gBACF;gBACA5E,MAAM,CAAC,yBAAyB,EAAEqF,KAAKC,GAAG,KAAKF,MAAM,EAAE,CAAC;YAC1D;QACF;QAEA,IAAIvE,OAAOgE,YAAY,CAACC,0BAA0B,EAAE;YAClDzG,kBAAkB4H,wBAAwB;QAC5C;QACArB,oCAAAA,yBAAAA,gBAAiBsB,KAAK,qBAAtBtB,4BAAAA;QAEAtE,SAAS;YACPC,UAAU;mBACJ0E,CAAAA,gCAAAA,aAAc1E,QAAQ,KAAI,EAAE;mBAC5B2E,CAAAA,gCAAAA,aAAc3E,QAAQ,KAAI,EAAE;mBAC5B4E,CAAAA,oCAAAA,iBAAkB5E,QAAQ,KAAI,EAAE;aACrC,CAAC4F,MAAM,CAAC3H;YACTgC,QAAQ;mBACFyE,CAAAA,gCAAAA,aAAczE,MAAM,KAAI,EAAE;mBAC1B0E,CAAAA,gCAAAA,aAAc1E,MAAM,KAAI,EAAE;mBAC1B2E,CAAAA,oCAAAA,iBAAkB3E,MAAM,KAAI,EAAE;aACnC,CAAC2F,MAAM,CAAC3H;YACTiC,OAAO;gBACLwE,gCAAAA,aAAcxE,KAAK;gBACnByE,gCAAAA,aAAczE,KAAK;gBACnB0E,oCAAAA,iBAAkB1E,KAAK;aACxB;QACH;IACF;IACAH,SAASK,cACNO,UAAU,CAAC,2BACXkF,OAAO,CAAC,IAAM7H,sBAAsB+B,QAAQ;IAE/C,MAAM+F,mBAAkB,wBAAA,AAAClC,aAAuCmC,OAAO,qBAA/C,sBAAiDC,IAAI,CAC3EtG;IAGF,MAAMuG,0BAAyB,wBAAA,AAC7BpC,aACAkC,OAAO,qBAFsB,sBAEpBC,IAAI,CAACpG;IAEhB,MAAMsG,kBAAkB3F,QAAQ6D,MAAM,CAACjE;IAEvC,IAAIJ,OAAOE,MAAM,CAACiE,MAAM,GAAG,GAAG;QAC5B,8DAA8D;QAC9D,0DAA0D;QAC1D,IAAInE,OAAOE,MAAM,CAACiE,MAAM,GAAG,GAAG;YAC5BnE,OAAOE,MAAM,CAACiE,MAAM,GAAG;QACzB;QACA,IAAIiC,QAAQpG,OAAOE,MAAM,CAAC2F,MAAM,CAACQ,SAASC,IAAI,CAAC;QAE/CC,QAAQH,KAAK,CAACpI,IAAI;QAElB,IACEoI,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uCAAuC,CAAC,GACtD;YACA,MAAMC,kBAAkB;YACxB,MAAMC,SAASD,gBAAgBE,IAAI,CAACP;YACpC,MAAMQ,YAAYF,UAAUA,OAAOG,MAAM,IAAIH,OAAOG,MAAM,CAACD,SAAS;YACpE,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,sFAAsF,EAAEF,UAAU,oFAAoF,CAAC,GADpL,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAL,QAAQH,KAAK,CAACA;QACdG,QAAQH,KAAK;QAEb,IACEA,MAAMI,OAAO,CAAC,wBAAwB,CAAC,KACvCJ,MAAMI,OAAO,CAAC,uBAAuB,CAAC,GACtC;YACA,MAAMO,MAAM,qBAEX,CAFW,IAAID,MACd,oHADU,qBAAA;uBAAA;4BAAA;8BAAA;YAEZ;YACAC,IAAIC,IAAI,GAAG;YACX,MAAMD;QACR;QACA,MAAMA,MAAM,qBAEX,CAFW,IAAID,MACd,CAAC,wBAAwB,EAAEtG,QAAQC,GAAG,CAACwG,WAAW,GAAG,WAAW,UAAU,OAAO,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEZ;QACAF,IAAIC,IAAI,GAAG;QACX,MAAMD;IACR,OAAO;QACL,MAAMG,WAAWf,eAAe,CAAC,EAAE;QACnC,MAAMgB,iBAAiB1H,iBAAiByH;QAExC,IAAIlH,OAAOC,QAAQ,CAACkE,MAAM,GAAG,GAAG;YAC9B3F,IAAI4F,IAAI,CAAC,CAAC,0BAA0B,EAAE+C,eAAe,EAAE,CAAC;YACxDZ,QAAQnC,IAAI,CAACpE,OAAOC,QAAQ,CAAC4F,MAAM,CAACQ,SAASC,IAAI,CAAC;YAClDC,QAAQnC,IAAI;QACd,OAAO,IAAI,CAACrE,cAAc;YACxBvB,IAAI4I,KAAK,CAAC,CAAC,yBAAyB,EAAED,gBAAgB;QACxD;QAEA,OAAO;YACLD,UAAUf,eAAe,CAAC,EAAE;YAC5BkB,iBAAiB,EAAEnB,0CAAAA,uBAAwBmB,iBAAiB;YAC5DpC,aAAanG;YACbwI,gBAAgB;gBACdC,QAAQxB,CAAAA,mCAAAA,gBAAiBwB,MAAM,OAAM,EAAE;gBACvCC,+BACEzB,CAAAA,mCAAAA,gBAAiByB,6BAA6B,OAAM,EAAE;gBACxDC,iBAAiB1B,CAAAA,mCAAAA,gBAAiB2B,kBAAkB,OAAM,CAAC;YAC7D;QACF;IACF;AACF;AAEA,sDAAsD;AACtD,OAAO,eAAeC,WAAWC,UAIhC;IAKC,iCAAiC;IACjC,MAAMC,YAAY,IAAIrI,UAAU;QAC9BsI,SAASF,WAAWG,YAAY,CAACxH,MAAM,CAAEuH,OAAO;IAClD;IACA3I,UAAU,aAAa0I;IACvB,0EAA0E;IAC1EG,OAAOC,MAAM,CAACrJ,kBAAkBgJ,WAAWG,YAAY;IAEvD,0CAA0C;IAC1C7I,qBAAqB0I,WAAWM,UAAU;IAE1C,sBAAsB;IACtBrJ,kBAAkBD,iBAAiBqG,WAAW;IAE9C,iDAAiD;IACjDrG,iBAAiB2B,MAAM,GAAG,MAAMvB,WAC9BV,wBACAM,iBAAiB0B,GAAG;IAEtB1B,iBAAiByB,aAAa,GAAGjB,MAC/B,CAAC,YAAY,EAAEwI,WAAW7H,YAAY,EAAE;IAG1C,MAAMC,SAAS,MAAMF,iBAAiB8H,WAAW7H,YAAY;IAC7D,MAAM,EAAEoI,YAAY,EAAEC,WAAW,EAAE,GAAGpI,OAAOqH,iBAAiB,IAAI,CAAC;IACnE,IAAIc,cAAc;QAChB,MAAM,EAAEE,YAAY,EAAEC,WAAW,EAAE,GAAGH;QACtC,IAAIG,aAAa;YACftI,OAAOqH,iBAAiB,CAAEc,YAAY,CAAEG,WAAW,GAAGA;QACxD;QACA,IAAID,cAAc;YAChB,MAAME,eAAeF;YACrBrI,OAAOqH,iBAAiB,CAAEc,YAAY,CAAEE,YAAY,GAAGE;QACzD;IACF;IACA,IAAIH,+BAAAA,YAAaI,iBAAiB,EAAE;QAClC,MAAMA,oBAAoBJ,YAAYI,iBAAiB;QACvDxI,OAAOqH,iBAAiB,CAAEe,WAAW,CAAEI,iBAAiB,GAAGA;IAC7D;IACA5J,iBAAiByB,aAAa,CAACoI,IAAI;IACnC,OAAO;QAAE,GAAGzI,MAAM;QAAE0I,kBAAkBzJ;IAAiB;AACzD"}