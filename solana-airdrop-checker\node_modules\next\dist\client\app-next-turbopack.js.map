{"version": 3, "sources": ["../../src/client/app-next-turbopack.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport { appBootstrap } from './app-bootstrap'\n\nwindow.next.version += '-turbo'\n;(self as any).__webpack_hash__ = ''\n\nconst instrumentationHooks = require('../lib/require-instrumentation-client')\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  hydrate(instrumentationHooks)\n\n  if (process.env.NODE_ENV !== 'production') {\n    const { initializeDevBuildIndicatorForAppRouter } =\n      require('./dev/dev-build-indicator/initialize-for-app-router') as typeof import('./dev/dev-build-indicator/initialize-for-app-router')\n    initializeDevBuildIndicatorForAppRouter()\n  }\n})\n"], "names": ["window", "next", "version", "self", "__webpack_hash__", "<PERSON><PERSON><PERSON><PERSON>", "require", "appBootstrap", "hydrate", "process", "env", "NODE_ENV", "initializeDevBuildIndicatorForAppRouter"], "mappings": "AAAA,8BAA8B;;;;;8BAED;AAE7BA,OAAOC,IAAI,CAACC,OAAO,IAAI;AACrBC,KAAaC,gBAAgB,GAAG;AAElC,MAAMC,uBAAuBC,QAAQ;AAErCC,IAAAA,0BAAY,EAAC;IACX,MAAM,EAAEC,OAAO,EAAE,GAAGF,QAAQ;IAC5BE,QAAQH;IAER,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAM,EAAEC,uCAAuC,EAAE,GAC/CN,QAAQ;QACVM;IACF;AACF"}