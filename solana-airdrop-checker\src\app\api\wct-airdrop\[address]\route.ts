import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: { address: string } }
) {
  try {
    const { address } = params;
    
    if (!address) {
      return NextResponse.json(
        { error: 'Address parameter is required' },
        { status: 400 }
      );
    }

    // Validate Solana address format
    const base58Regex = /^[1-9A-HJ-NP-Za-km-z]{32,44}$/;
    if (!base58Regex.test(address)) {
      return NextResponse.json(
        { error: 'Invalid Solana address format' },
        { status: 400 }
      );
    }

    // Fetch from WalletConnect API
    const response = await fetch(
      `https://api.walletconnect.network/airdrop/solana/${address}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      // Handle different error cases
      if (response.status === 404) {
        return NextResponse.json({
          address,
          isEligible: false,
          eligibleAmount: 0,
          error: null
        });
      }
      
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Check if recipient not found (not eligible)
    if (data.error && data.error.includes('not found')) {
      return NextResponse.json({
        address,
        isEligible: false,
        eligibleAmount: 0,
        error: null
      });
    }

    // If there's any other error
    if (data.error) {
      return NextResponse.json({
        address,
        isEligible: false,
        eligibleAmount: 0,
        error: data.error
      });
    }

    // Extract eligible amount from response (hex format)
    let eligibleAmount = 0;
    if (data.amount) {
      // Convert hex amount to decimal and then to WCT tokens
      const hexAmount = data.amount.startsWith('0x') ? data.amount : '0x' + data.amount;
      const decimalAmount = parseInt(hexAmount, 16);
      // Convert from smallest unit to WCT tokens (9 decimals like SOL)
      eligibleAmount = decimalAmount / Math.pow(10, 9);
    }

    return NextResponse.json({
      address,
      isEligible: eligibleAmount > 0,
      eligibleAmount,
      error: null,
      // Include original data for debugging
      originalData: data
    });

  } catch (error) {
    console.error('WCT Airdrop API Error:', error);
    
    return NextResponse.json(
      {
        address: params.address,
        isEligible: false,
        eligibleAmount: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      },
      { status: 500 }
    );
  }
}
