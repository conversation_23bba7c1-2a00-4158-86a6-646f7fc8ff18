{"version": 3, "sources": ["../../../../src/server/web/sandbox/context.ts"], "sourcesContent": ["import type { AssetBinding } from '../../../build/webpack/loaders/get-module-build-info'\nimport type {\n  EdgeFunctionDefinition,\n  SUPPORTED_NATIVE_MODULES,\n} from '../../../build/webpack/plugins/middleware-plugin'\nimport type { UnwrapPromise } from '../../../lib/coalesced-function'\nimport { AsyncLocalStorage } from 'async_hooks'\nimport {\n  COMPILER_NAMES,\n  EDGE_UNSUPPORTED_NODE_APIS,\n} from '../../../shared/lib/constants'\nimport { EdgeRuntime } from 'next/dist/compiled/edge-runtime'\nimport { readFileSync, promises as fs } from 'fs'\nimport { validateURL } from '../utils'\nimport { pick } from '../../../lib/pick'\nimport { fetchInlineAsset } from './fetch-inline-assets'\nimport { runInContext } from 'vm'\nimport BufferImplementation from 'node:buffer'\nimport EventsImplementation from 'node:events'\nimport AssertImplementation from 'node:assert'\nimport UtilImplementation from 'node:util'\nimport AsyncHooksImplementation from 'node:async_hooks'\nimport { intervalsManager, timeoutsManager } from './resource-managers'\nimport { createLocalRequestContext } from '../../after/builtin-request-context'\nimport {\n  patchErrorInspectEdgeLite,\n  patchErrorInspectNodeJS,\n} from '../../patch-error-inspect'\n\ninterface ModuleContext {\n  runtime: EdgeRuntime\n  paths: Map<string, string>\n  warnedEvals: Set<string>\n}\n\nlet getServerError: typeof import('../../../client/components/react-dev-overlay/server/middleware-webpack').getServerError\nlet decorateServerError: typeof import('../../../shared/lib/error-source').decorateServerError\n\nif (process.env.NODE_ENV === 'development') {\n  const middleware =\n    require('../../../client/components/react-dev-overlay/server/middleware-webpack') as typeof import('../../../client/components/react-dev-overlay/server/middleware-webpack')\n  getServerError = middleware.getServerError\n  decorateServerError =\n    require('../../../shared/lib/error-source').decorateServerError\n} else {\n  getServerError = (error: Error, _: string) => error\n  decorateServerError = (_: Error, __: string) => {}\n}\n\n/**\n * A Map of cached module contexts indexed by the module name. It allows\n * to have a different cache scoped per module name or depending on the\n * provided module key on creation.\n */\nconst moduleContexts = new Map<string, ModuleContext>()\n\nconst pendingModuleCaches = new Map<string, Promise<ModuleContext>>()\n\n/**\n * Same as clearModuleContext but for all module contexts.\n */\nexport async function clearAllModuleContexts() {\n  intervalsManager.removeAll()\n  timeoutsManager.removeAll()\n  moduleContexts.clear()\n  pendingModuleCaches.clear()\n}\n\n/**\n * For a given path a context, this function checks if there is any module\n * context that contains the path with an older content and, if that's the\n * case, removes the context from the cache.\n *\n * This function also clears all intervals and timeouts created by the\n * module context.\n */\nexport async function clearModuleContext(path: string) {\n  intervalsManager.removeAll()\n  timeoutsManager.removeAll()\n\n  const handleContext = (\n    key: string,\n    cache: ReturnType<(typeof moduleContexts)['get']>,\n    context: typeof moduleContexts | typeof pendingModuleCaches\n  ) => {\n    if (cache?.paths.has(path)) {\n      context.delete(key)\n    }\n  }\n\n  for (const [key, cache] of moduleContexts) {\n    handleContext(key, cache, moduleContexts)\n  }\n  for (const [key, cache] of pendingModuleCaches) {\n    handleContext(key, await cache, pendingModuleCaches)\n  }\n}\n\nasync function loadWasm(\n  wasm: AssetBinding[]\n): Promise<Record<string, WebAssembly.Module>> {\n  const modules: Record<string, WebAssembly.Module> = {}\n\n  await Promise.all(\n    wasm.map(async (binding) => {\n      const module = await WebAssembly.compile(\n        await fs.readFile(binding.filePath)\n      )\n      modules[binding.name] = module\n    })\n  )\n\n  return modules\n}\n\nfunction buildEnvironmentVariablesFrom(\n  injectedEnvironments: Record<string, string>\n): Record<string, string | undefined> {\n  const pairs = Object.keys(process.env).map((key) => [key, process.env[key]])\n  const env = Object.fromEntries(pairs)\n  for (const key of Object.keys(injectedEnvironments)) {\n    env[key] = injectedEnvironments[key]\n  }\n  env.NEXT_RUNTIME = 'edge'\n  return env\n}\n\nfunction throwUnsupportedAPIError(name: string) {\n  const error =\n    new Error(`A Node.js API is used (${name}) which is not supported in the Edge Runtime.\nLearn more: https://nextjs.org/docs/api-reference/edge-runtime`)\n  decorateServerError(error, COMPILER_NAMES.edgeServer)\n  throw error\n}\n\nfunction createProcessPolyfill(env: Record<string, string>) {\n  const processPolyfill = { env: buildEnvironmentVariablesFrom(env) }\n  const overriddenValue: Record<string, any> = {}\n\n  for (const key of Object.keys(process)) {\n    if (key === 'env') continue\n    Object.defineProperty(processPolyfill, key, {\n      get() {\n        if (overriddenValue[key] !== undefined) {\n          return overriddenValue[key]\n        }\n        if (typeof (process as any)[key] === 'function') {\n          return () => throwUnsupportedAPIError(`process.${key}`)\n        }\n        return undefined\n      },\n      set(value) {\n        overriddenValue[key] = value\n      },\n      enumerable: false,\n    })\n  }\n  return processPolyfill\n}\n\nfunction addStub(context: EdgeRuntime['context'], name: string) {\n  Object.defineProperty(context, name, {\n    get() {\n      return function () {\n        throwUnsupportedAPIError(name)\n      }\n    },\n    enumerable: false,\n  })\n}\n\nfunction getDecorateUnhandledError(runtime: EdgeRuntime) {\n  const EdgeRuntimeError = runtime.evaluate(`Error`)\n  return (error: any) => {\n    if (error instanceof EdgeRuntimeError) {\n      decorateServerError(error, COMPILER_NAMES.edgeServer)\n    }\n  }\n}\n\nfunction getDecorateUnhandledRejection(runtime: EdgeRuntime) {\n  const EdgeRuntimeError = runtime.evaluate(`Error`)\n  return (rejected: { reason: typeof EdgeRuntimeError }) => {\n    if (rejected.reason instanceof EdgeRuntimeError) {\n      decorateServerError(rejected.reason, COMPILER_NAMES.edgeServer)\n    }\n  }\n}\n\nconst NativeModuleMap = (() => {\n  const mods: Record<\n    `node:${(typeof SUPPORTED_NATIVE_MODULES)[number]}`,\n    unknown\n  > = {\n    'node:buffer': pick(BufferImplementation, [\n      'constants',\n      'kMaxLength',\n      'kStringMaxLength',\n      'Buffer',\n      'SlowBuffer',\n    ]),\n    'node:events': pick(EventsImplementation, [\n      'EventEmitter',\n      'captureRejectionSymbol',\n      'defaultMaxListeners',\n      'errorMonitor',\n      'listenerCount',\n      'on',\n      'once',\n    ]),\n    'node:async_hooks': pick(AsyncHooksImplementation, [\n      'AsyncLocalStorage',\n      'AsyncResource',\n    ]),\n    'node:assert': pick(AssertImplementation, [\n      'AssertionError',\n      'deepEqual',\n      'deepStrictEqual',\n      'doesNotMatch',\n      'doesNotReject',\n      'doesNotThrow',\n      'equal',\n      'fail',\n      'ifError',\n      'match',\n      'notDeepEqual',\n      'notDeepStrictEqual',\n      'notEqual',\n      'notStrictEqual',\n      'ok',\n      'rejects',\n      'strict',\n      'strictEqual',\n      'throws',\n    ]),\n    'node:util': pick(UtilImplementation, [\n      '_extend' as any,\n      'callbackify',\n      'format',\n      'inherits',\n      'promisify',\n      'types',\n    ]),\n  }\n  return new Map(Object.entries(mods))\n})()\n\nexport const requestStore = new AsyncLocalStorage<{\n  headers: Headers\n}>()\n\nexport const edgeSandboxNextRequestContext = createLocalRequestContext()\n\n/**\n * Create a module cache specific for the provided parameters. It includes\n * a runtime context, require cache and paths cache.\n */\nasync function createModuleContext(options: ModuleContextOptions) {\n  const warnedEvals = new Set<string>()\n  const warnedWasmCodegens = new Set<string>()\n  const { edgeFunctionEntry } = options\n  const wasm = await loadWasm(edgeFunctionEntry.wasm ?? [])\n  const runtime = new EdgeRuntime({\n    codeGeneration:\n      process.env.NODE_ENV !== 'production'\n        ? { strings: true, wasm: true }\n        : undefined,\n    extend: (context) => {\n      context.process = createProcessPolyfill(edgeFunctionEntry.env)\n\n      Object.defineProperty(context, 'require', {\n        enumerable: false,\n        value: (id: string) => {\n          const value = NativeModuleMap.get(id)\n          if (!value) {\n            throw TypeError('Native module not found: ' + id)\n          }\n          return value\n        },\n      })\n\n      if (process.env.NODE_ENV !== 'production') {\n        context.__next_log_error__ = function (err: unknown) {\n          options.onError(err)\n        }\n      }\n\n      context.__next_eval__ = function __next_eval__(fn: Function) {\n        const key = fn.toString()\n        if (!warnedEvals.has(key)) {\n          const warning = getServerError(\n            new Error(\n              `Dynamic Code Evaluation (e. g. 'eval', 'new Function') not allowed in Edge Runtime\nLearn More: https://nextjs.org/docs/messages/edge-dynamic-code-evaluation`\n            ),\n            COMPILER_NAMES.edgeServer\n          )\n          warning.name = 'DynamicCodeEvaluationWarning'\n          Error.captureStackTrace(warning, __next_eval__)\n          warnedEvals.add(key)\n          options.onWarning(warning)\n        }\n        return fn()\n      }\n\n      context.__next_webassembly_compile__ =\n        function __next_webassembly_compile__(fn: Function) {\n          const key = fn.toString()\n          if (!warnedWasmCodegens.has(key)) {\n            const warning = getServerError(\n              new Error(`Dynamic WASM code generation (e. g. 'WebAssembly.compile') not allowed in Edge Runtime.\nLearn More: https://nextjs.org/docs/messages/edge-dynamic-code-evaluation`),\n              COMPILER_NAMES.edgeServer\n            )\n            warning.name = 'DynamicWasmCodeGenerationWarning'\n            Error.captureStackTrace(warning, __next_webassembly_compile__)\n            warnedWasmCodegens.add(key)\n            options.onWarning(warning)\n          }\n          return fn()\n        }\n\n      context.__next_webassembly_instantiate__ =\n        async function __next_webassembly_instantiate__(fn: Function) {\n          const result = await fn()\n\n          // If a buffer is given, WebAssembly.instantiate returns an object\n          // containing both a module and an instance while it returns only an\n          // instance if a WASM module is given. Utilize the fact to determine\n          // if the WASM code generation happens.\n          //\n          // https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/WebAssembly/instantiate#primary_overload_%E2%80%94_taking_wasm_binary_code\n          const instantiatedFromBuffer = result.hasOwnProperty('module')\n\n          const key = fn.toString()\n          if (instantiatedFromBuffer && !warnedWasmCodegens.has(key)) {\n            const warning = getServerError(\n              new Error(`Dynamic WASM code generation ('WebAssembly.instantiate' with a buffer parameter) not allowed in Edge Runtime.\nLearn More: https://nextjs.org/docs/messages/edge-dynamic-code-evaluation`),\n              COMPILER_NAMES.edgeServer\n            )\n            warning.name = 'DynamicWasmCodeGenerationWarning'\n            Error.captureStackTrace(warning, __next_webassembly_instantiate__)\n            warnedWasmCodegens.add(key)\n            options.onWarning(warning)\n          }\n          return result\n        }\n\n      const __fetch = context.fetch\n      context.fetch = async (input, init = {}) => {\n        const callingError = new Error('[internal]')\n        const assetResponse = await fetchInlineAsset({\n          input,\n          assets: options.edgeFunctionEntry.assets,\n          distDir: options.distDir,\n          context,\n        })\n        if (assetResponse) {\n          return assetResponse\n        }\n\n        init.headers = new Headers(init.headers ?? {})\n\n        if (!init.headers.has('user-agent')) {\n          init.headers.set(`user-agent`, `Next.js Middleware`)\n        }\n\n        const response =\n          typeof input === 'object' && 'url' in input\n            ? __fetch(input.url, {\n                ...pick(input, [\n                  'method',\n                  'body',\n                  'cache',\n                  'credentials',\n                  'integrity',\n                  'keepalive',\n                  'mode',\n                  'redirect',\n                  'referrer',\n                  'referrerPolicy',\n                  'signal',\n                ]),\n                ...init,\n                headers: {\n                  ...Object.fromEntries(input.headers),\n                  ...Object.fromEntries(init.headers),\n                },\n              })\n            : __fetch(String(input), init)\n\n        return await response.catch((err) => {\n          callingError.message = err.message\n          err.stack = callingError.stack\n          throw err\n        })\n      }\n\n      const __Request = context.Request\n      context.Request = class extends __Request {\n        next?: NextFetchRequestConfig | undefined\n        constructor(input: URL | RequestInfo, init?: RequestInit | undefined) {\n          const url =\n            typeof input !== 'string' && 'url' in input\n              ? input.url\n              : String(input)\n          validateURL(url)\n          super(url, init)\n          this.next = init?.next\n        }\n      }\n\n      const __redirect = context.Response.redirect.bind(context.Response)\n      context.Response.redirect = (...args) => {\n        validateURL(args[0])\n        return __redirect(...args)\n      }\n\n      for (const name of EDGE_UNSUPPORTED_NODE_APIS) {\n        addStub(context, name)\n      }\n\n      Object.assign(context, wasm)\n\n      context.performance = performance\n\n      context.AsyncLocalStorage = AsyncLocalStorage\n\n      // @ts-ignore the timeouts have weird types in the edge runtime\n      context.setInterval = (...args: Parameters<typeof setInterval>) =>\n        intervalsManager.add(args)\n\n      // @ts-ignore the timeouts have weird types in the edge runtime\n      context.clearInterval = (interval: number) =>\n        intervalsManager.remove(interval)\n\n      // @ts-ignore the timeouts have weird types in the edge runtime\n      context.setTimeout = (...args: Parameters<typeof setTimeout>) =>\n        timeoutsManager.add(args)\n\n      // @ts-ignore the timeouts have weird types in the edge runtime\n      context.clearTimeout = (timeout: number) =>\n        timeoutsManager.remove(timeout)\n\n      // Duplicated from packages/next/src/server/after/builtin-request-context.ts\n      // because we need to use the sandboxed `Symbol.for`, not the one from the outside\n      const NEXT_REQUEST_CONTEXT_SYMBOL = context.Symbol.for(\n        '@next/request-context'\n      )\n      Object.defineProperty(context, NEXT_REQUEST_CONTEXT_SYMBOL, {\n        enumerable: false,\n        value: edgeSandboxNextRequestContext,\n      })\n\n      return context\n    },\n  })\n\n  const decorateUnhandledError = getDecorateUnhandledError(runtime)\n  runtime.context.addEventListener('error', decorateUnhandledError)\n  const decorateUnhandledRejection = getDecorateUnhandledRejection(runtime)\n  runtime.context.addEventListener(\n    'unhandledrejection',\n    decorateUnhandledRejection\n  )\n\n  patchErrorInspectEdgeLite(runtime.context.Error)\n  // An Error from within the Edge Runtime could also bubble up into the Node.js process.\n  // For example, uncaught errors are handled in the Node.js runtime.\n  patchErrorInspectNodeJS(runtime.context.Error)\n\n  return {\n    runtime,\n    paths: new Map<string, string>(),\n    warnedEvals: new Set<string>(),\n  }\n}\n\ninterface ModuleContextOptions {\n  moduleName: string\n  onError: (err: unknown) => void\n  onWarning: (warn: Error) => void\n  useCache: boolean\n  distDir: string\n  edgeFunctionEntry: Pick<EdgeFunctionDefinition, 'assets' | 'wasm' | 'env'>\n}\n\nfunction getModuleContextShared(options: ModuleContextOptions) {\n  let deferredModuleContext = pendingModuleCaches.get(options.moduleName)\n  if (!deferredModuleContext) {\n    deferredModuleContext = createModuleContext(options)\n    pendingModuleCaches.set(options.moduleName, deferredModuleContext)\n  }\n  return deferredModuleContext\n}\n\n/**\n * For a given module name this function will get a cached module\n * context or create it. It will return the module context along\n * with a function that allows to run some code from a given\n * filepath within the context.\n */\nexport async function getModuleContext(options: ModuleContextOptions): Promise<{\n  evaluateInContext: (filepath: string) => void\n  runtime: EdgeRuntime\n  paths: Map<string, string>\n  warnedEvals: Set<string>\n}> {\n  let lazyModuleContext:\n    | UnwrapPromise<ReturnType<typeof getModuleContextShared>>\n    | undefined\n\n  if (options.useCache) {\n    lazyModuleContext =\n      moduleContexts.get(options.moduleName) ||\n      (await getModuleContextShared(options))\n  }\n\n  if (!lazyModuleContext) {\n    lazyModuleContext = await createModuleContext(options)\n    moduleContexts.set(options.moduleName, lazyModuleContext)\n  }\n\n  const moduleContext = lazyModuleContext\n\n  const evaluateInContext = (filepath: string) => {\n    if (!moduleContext.paths.has(filepath)) {\n      const content = readFileSync(filepath, 'utf-8')\n      try {\n        runInContext(content, moduleContext.runtime.context, {\n          filename: filepath,\n        })\n        moduleContext.paths.set(filepath, content)\n      } catch (error) {\n        if (options.useCache) {\n          moduleContext?.paths.delete(filepath)\n        }\n        throw error\n      }\n    }\n  }\n\n  return { ...moduleContext, evaluateInContext }\n}\n"], "names": ["clearAllModuleContexts", "clearModuleContext", "edgeSandboxNextRequestContext", "getModuleContext", "requestStore", "getServerError", "decorateServerError", "process", "env", "NODE_ENV", "middleware", "require", "error", "_", "__", "moduleContexts", "Map", "pendingModuleCaches", "intervalsManager", "removeAll", "timeouts<PERSON><PERSON><PERSON>", "clear", "path", "handleContext", "key", "cache", "context", "paths", "has", "delete", "loadWasm", "wasm", "modules", "Promise", "all", "map", "binding", "module", "WebAssembly", "compile", "fs", "readFile", "filePath", "name", "buildEnvironmentVariablesFrom", "injectedEnvironments", "pairs", "Object", "keys", "fromEntries", "NEXT_RUNTIME", "throwUnsupportedAPIError", "Error", "COMPILER_NAMES", "edgeServer", "createProcessPolyfill", "processPolyfill", "overriddenValue", "defineProperty", "get", "undefined", "set", "value", "enumerable", "addStub", "getDecorateUnhandledError", "runtime", "EdgeRuntimeError", "evaluate", "getDecorateUnhandledRejection", "rejected", "reason", "NativeModuleMap", "mods", "pick", "BufferImplementation", "EventsImplementation", "AsyncHooksImplementation", "AssertImplementation", "UtilImplementation", "entries", "AsyncLocalStorage", "createLocalRequestContext", "createModuleContext", "options", "warnedEvals", "Set", "warnedWasmCodegens", "edgeFunctionEntry", "EdgeRuntime", "codeGeneration", "strings", "extend", "id", "TypeError", "__next_log_error__", "err", "onError", "__next_eval__", "fn", "toString", "warning", "captureStackTrace", "add", "onWarning", "__next_webassembly_compile__", "__next_webassembly_instantiate__", "result", "instantiatedFromBuffer", "hasOwnProperty", "__fetch", "fetch", "input", "init", "callingError", "assetResponse", "fetchInlineAsset", "assets", "distDir", "headers", "Headers", "response", "url", "String", "catch", "message", "stack", "__Request", "Request", "constructor", "validateURL", "next", "__redirect", "Response", "redirect", "bind", "args", "EDGE_UNSUPPORTED_NODE_APIS", "assign", "performance", "setInterval", "clearInterval", "interval", "remove", "setTimeout", "clearTimeout", "timeout", "NEXT_REQUEST_CONTEXT_SYMBOL", "Symbol", "for", "decorateUnhandledError", "addEventListener", "decorateUnhandledRejection", "patchErrorInspectEdgeLite", "patchErrorInspectNodeJS", "getModuleContextShared", "deferredModuleContext", "moduleName", "lazyModuleContext", "useCache", "moduleContext", "evaluateInContext", "filepath", "content", "readFileSync", "runInContext", "filename"], "mappings": ";;;;;;;;;;;;;;;;;;IA6DsBA,sBAAsB;eAAtBA;;IAeAC,kBAAkB;eAAlBA;;IA+KTC,6BAA6B;eAA7BA;;IA4PSC,gBAAgB;eAAhBA;;IAhQTC,YAAY;eAAZA;;;6BAjPqB;2BAI3B;6BACqB;oBACiB;uBACjB;sBACP;mCACY;oBACJ;mEACI;mEACA;mEACA;iEACF;wEACM;kCACa;uCACR;mCAInC;;;;;;AAQP,IAAIC;AACJ,IAAIC;AAEJ,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;IAC1C,MAAMC,aACJC,QAAQ;IACVN,iBAAiBK,WAAWL,cAAc;IAC1CC,sBACEK,QAAQ,oCAAoCL,mBAAmB;AACnE,OAAO;IACLD,iBAAiB,CAACO,OAAcC,IAAcD;IAC9CN,sBAAsB,CAACO,GAAUC,MAAgB;AACnD;AAEA;;;;CAIC,GACD,MAAMC,iBAAiB,IAAIC;AAE3B,MAAMC,sBAAsB,IAAID;AAKzB,eAAehB;IACpBkB,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IACzBJ,eAAeM,KAAK;IACpBJ,oBAAoBI,KAAK;AAC3B;AAUO,eAAepB,mBAAmBqB,IAAY;IACnDJ,kCAAgB,CAACC,SAAS;IAC1BC,iCAAe,CAACD,SAAS;IAEzB,MAAMI,gBAAgB,CACpBC,KACAC,OACAC;QAEA,IAAID,yBAAAA,MAAOE,KAAK,CAACC,GAAG,CAACN,OAAO;YAC1BI,QAAQG,MAAM,CAACL;QACjB;IACF;IAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIV,eAAgB;QACzCQ,cAAcC,KAAKC,OAAOV;IAC5B;IACA,KAAK,MAAM,CAACS,KAAKC,MAAM,IAAIR,oBAAqB;QAC9CM,cAAcC,KAAK,MAAMC,OAAOR;IAClC;AACF;AAEA,eAAea,SACbC,IAAoB;IAEpB,MAAMC,UAA8C,CAAC;IAErD,MAAMC,QAAQC,GAAG,CACfH,KAAKI,GAAG,CAAC,OAAOC;QACd,MAAMC,UAAS,MAAMC,YAAYC,OAAO,CACtC,MAAMC,YAAE,CAACC,QAAQ,CAACL,QAAQM,QAAQ;QAEpCV,OAAO,CAACI,QAAQO,IAAI,CAAC,GAAGN;IAC1B;IAGF,OAAOL;AACT;AAEA,SAASY,8BACPC,oBAA4C;IAE5C,MAAMC,QAAQC,OAAOC,IAAI,CAACzC,QAAQC,GAAG,EAAE2B,GAAG,CAAC,CAACX,MAAQ;YAACA;YAAKjB,QAAQC,GAAG,CAACgB,IAAI;SAAC;IAC3E,MAAMhB,MAAMuC,OAAOE,WAAW,CAACH;IAC/B,KAAK,MAAMtB,OAAOuB,OAAOC,IAAI,CAACH,sBAAuB;QACnDrC,GAAG,CAACgB,IAAI,GAAGqB,oBAAoB,CAACrB,IAAI;IACtC;IACAhB,IAAI0C,YAAY,GAAG;IACnB,OAAO1C;AACT;AAEA,SAAS2C,yBAAyBR,IAAY;IAC5C,MAAM/B,QACJ,qBAC4D,CAD5D,IAAIwC,MAAM,CAAC,uBAAuB,EAAET,KAAK;8DACiB,CAAC,GAD3D,qBAAA;eAAA;oBAAA;sBAAA;IAC2D;IAC7DrC,oBAAoBM,OAAOyC,yBAAc,CAACC,UAAU;IACpD,MAAM1C;AACR;AAEA,SAAS2C,sBAAsB/C,GAA2B;IACxD,MAAMgD,kBAAkB;QAAEhD,KAAKoC,8BAA8BpC;IAAK;IAClE,MAAMiD,kBAAuC,CAAC;IAE9C,KAAK,MAAMjC,OAAOuB,OAAOC,IAAI,CAACzC,SAAU;QACtC,IAAIiB,QAAQ,OAAO;QACnBuB,OAAOW,cAAc,CAACF,iBAAiBhC,KAAK;YAC1CmC;gBACE,IAAIF,eAAe,CAACjC,IAAI,KAAKoC,WAAW;oBACtC,OAAOH,eAAe,CAACjC,IAAI;gBAC7B;gBACA,IAAI,OAAO,AAACjB,OAAe,CAACiB,IAAI,KAAK,YAAY;oBAC/C,OAAO,IAAM2B,yBAAyB,CAAC,QAAQ,EAAE3B,KAAK;gBACxD;gBACA,OAAOoC;YACT;YACAC,KAAIC,KAAK;gBACPL,eAAe,CAACjC,IAAI,GAAGsC;YACzB;YACAC,YAAY;QACd;IACF;IACA,OAAOP;AACT;AAEA,SAASQ,QAAQtC,OAA+B,EAAEiB,IAAY;IAC5DI,OAAOW,cAAc,CAAChC,SAASiB,MAAM;QACnCgB;YACE,OAAO;gBACLR,yBAAyBR;YAC3B;QACF;QACAoB,YAAY;IACd;AACF;AAEA,SAASE,0BAA0BC,OAAoB;IACrD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACxD;QACN,IAAIA,iBAAiBuD,kBAAkB;YACrC7D,oBAAoBM,OAAOyC,yBAAc,CAACC,UAAU;QACtD;IACF;AACF;AAEA,SAASe,8BAA8BH,OAAoB;IACzD,MAAMC,mBAAmBD,QAAQE,QAAQ,CAAC,CAAC,KAAK,CAAC;IACjD,OAAO,CAACE;QACN,IAAIA,SAASC,MAAM,YAAYJ,kBAAkB;YAC/C7D,oBAAoBgE,SAASC,MAAM,EAAElB,yBAAc,CAACC,UAAU;QAChE;IACF;AACF;AAEA,MAAMkB,kBAAkB,AAAC,CAAA;IACvB,MAAMC,OAGF;QACF,eAAeC,IAAAA,UAAI,EAACC,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;SACD;QACD,eAAeD,IAAAA,UAAI,EAACE,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,oBAAoBF,IAAAA,UAAI,EAACG,wBAAwB,EAAE;YACjD;YACA;SACD;QACD,eAAeH,IAAAA,UAAI,EAACI,mBAAoB,EAAE;YACxC;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,aAAaJ,IAAAA,UAAI,EAACK,iBAAkB,EAAE;YACpC;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,OAAO,IAAI/D,IAAI+B,OAAOiC,OAAO,CAACP;AAChC,CAAA;AAEO,MAAMrE,eAAe,IAAI6E,8BAAiB;AAI1C,MAAM/E,gCAAgCgF,IAAAA,gDAAyB;AAEtE;;;CAGC,GACD,eAAeC,oBAAoBC,OAA6B;IAC9D,MAAMC,cAAc,IAAIC;IACxB,MAAMC,qBAAqB,IAAID;IAC/B,MAAM,EAAEE,iBAAiB,EAAE,GAAGJ;IAC9B,MAAMrD,OAAO,MAAMD,SAAS0D,kBAAkBzD,IAAI,IAAI,EAAE;IACxD,MAAMmC,UAAU,IAAIuB,wBAAW,CAAC;QAC9BC,gBACEnF,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB;YAAEkF,SAAS;YAAM5D,MAAM;QAAK,IAC5B6B;QACNgC,QAAQ,CAAClE;YACPA,QAAQnB,OAAO,GAAGgD,sBAAsBiC,kBAAkBhF,GAAG;YAE7DuC,OAAOW,cAAc,CAAChC,SAAS,WAAW;gBACxCqC,YAAY;gBACZD,OAAO,CAAC+B;oBACN,MAAM/B,QAAQU,gBAAgBb,GAAG,CAACkC;oBAClC,IAAI,CAAC/B,OAAO;wBACV,MAAMgC,qBAA2C,CAA3CA,IAAAA,UAAU,8BAA8BD,KAAxCC,qBAAAA;mCAAAA;wCAAAA;0CAAAA;wBAA0C;oBAClD;oBACA,OAAOhC;gBACT;YACF;YAEA,IAAIvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzCiB,QAAQqE,kBAAkB,GAAG,SAAUC,GAAY;oBACjDZ,QAAQa,OAAO,CAACD;gBAClB;YACF;YAEAtE,QAAQwE,aAAa,GAAG,SAASA,cAAcC,EAAY;gBACzD,MAAM3E,MAAM2E,GAAGC,QAAQ;gBACvB,IAAI,CAACf,YAAYzD,GAAG,CAACJ,MAAM;oBACzB,MAAM6E,UAAUhG,eACd,qBAGC,CAHD,IAAI+C,MACF,CAAC;yEAC0D,CAAC,GAF9D,qBAAA;+BAAA;oCAAA;sCAAA;oBAGA,IACAC,yBAAc,CAACC,UAAU;oBAE3B+C,QAAQ1D,IAAI,GAAG;oBACfS,MAAMkD,iBAAiB,CAACD,SAASH;oBACjCb,YAAYkB,GAAG,CAAC/E;oBAChB4D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEAzE,QAAQ+E,4BAA4B,GAClC,SAASA,6BAA6BN,EAAY;gBAChD,MAAM3E,MAAM2E,GAAGC,QAAQ;gBACvB,IAAI,CAACb,mBAAmB3D,GAAG,CAACJ,MAAM;oBAChC,MAAM6E,UAAUhG,eACd,qBAC6D,CAD7D,IAAI+C,MAAM,CAAC;yEACgD,CAAC,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAC4D,IAC5DC,yBAAc,CAACC,UAAU;oBAE3B+C,QAAQ1D,IAAI,GAAG;oBACfS,MAAMkD,iBAAiB,CAACD,SAASI;oBACjClB,mBAAmBgB,GAAG,CAAC/E;oBACvB4D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOF;YACT;YAEFzE,QAAQgF,gCAAgC,GACtC,eAAeA,iCAAiCP,EAAY;gBAC1D,MAAMQ,SAAS,MAAMR;gBAErB,kEAAkE;gBAClE,oEAAoE;gBACpE,oEAAoE;gBACpE,uCAAuC;gBACvC,EAAE;gBACF,wJAAwJ;gBACxJ,MAAMS,yBAAyBD,OAAOE,cAAc,CAAC;gBAErD,MAAMrF,MAAM2E,GAAGC,QAAQ;gBACvB,IAAIQ,0BAA0B,CAACrB,mBAAmB3D,GAAG,CAACJ,MAAM;oBAC1D,MAAM6E,UAAUhG,eACd,qBAC6D,CAD7D,IAAI+C,MAAM,CAAC;yEACgD,CAAC,GAD5D,qBAAA;+BAAA;oCAAA;sCAAA;oBAC4D,IAC5DC,yBAAc,CAACC,UAAU;oBAE3B+C,QAAQ1D,IAAI,GAAG;oBACfS,MAAMkD,iBAAiB,CAACD,SAASK;oBACjCnB,mBAAmBgB,GAAG,CAAC/E;oBACvB4D,QAAQoB,SAAS,CAACH;gBACpB;gBACA,OAAOM;YACT;YAEF,MAAMG,UAAUpF,QAAQqF,KAAK;YAC7BrF,QAAQqF,KAAK,GAAG,OAAOC,OAAOC,OAAO,CAAC,CAAC;gBACrC,MAAMC,eAAe,qBAAuB,CAAvB,IAAI9D,MAAM,eAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAsB;gBAC3C,MAAM+D,gBAAgB,MAAMC,IAAAA,mCAAgB,EAAC;oBAC3CJ;oBACAK,QAAQjC,QAAQI,iBAAiB,CAAC6B,MAAM;oBACxCC,SAASlC,QAAQkC,OAAO;oBACxB5F;gBACF;gBACA,IAAIyF,eAAe;oBACjB,OAAOA;gBACT;gBAEAF,KAAKM,OAAO,GAAG,IAAIC,QAAQP,KAAKM,OAAO,IAAI,CAAC;gBAE5C,IAAI,CAACN,KAAKM,OAAO,CAAC3F,GAAG,CAAC,eAAe;oBACnCqF,KAAKM,OAAO,CAAC1D,GAAG,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,kBAAkB,CAAC;gBACrD;gBAEA,MAAM4D,WACJ,OAAOT,UAAU,YAAY,SAASA,QAClCF,QAAQE,MAAMU,GAAG,EAAE;oBACjB,GAAGhD,IAAAA,UAAI,EAACsC,OAAO;wBACb;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD,CAAC;oBACF,GAAGC,IAAI;oBACPM,SAAS;wBACP,GAAGxE,OAAOE,WAAW,CAAC+D,MAAMO,OAAO,CAAC;wBACpC,GAAGxE,OAAOE,WAAW,CAACgE,KAAKM,OAAO,CAAC;oBACrC;gBACF,KACAT,QAAQa,OAAOX,QAAQC;gBAE7B,OAAO,MAAMQ,SAASG,KAAK,CAAC,CAAC5B;oBAC3BkB,aAAaW,OAAO,GAAG7B,IAAI6B,OAAO;oBAClC7B,IAAI8B,KAAK,GAAGZ,aAAaY,KAAK;oBAC9B,MAAM9B;gBACR;YACF;YAEA,MAAM+B,YAAYrG,QAAQsG,OAAO;YACjCtG,QAAQsG,OAAO,GAAG,cAAcD;gBAE9BE,YAAYjB,KAAwB,EAAEC,IAA8B,CAAE;oBACpE,MAAMS,MACJ,OAAOV,UAAU,YAAY,SAASA,QAClCA,MAAMU,GAAG,GACTC,OAAOX;oBACbkB,IAAAA,kBAAW,EAACR;oBACZ,KAAK,CAACA,KAAKT;oBACX,IAAI,CAACkB,IAAI,GAAGlB,wBAAAA,KAAMkB,IAAI;gBACxB;YACF;YAEA,MAAMC,aAAa1G,QAAQ2G,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAAC7G,QAAQ2G,QAAQ;YAClE3G,QAAQ2G,QAAQ,CAACC,QAAQ,GAAG,CAAC,GAAGE;gBAC9BN,IAAAA,kBAAW,EAACM,IAAI,CAAC,EAAE;gBACnB,OAAOJ,cAAcI;YACvB;YAEA,KAAK,MAAM7F,QAAQ8F,qCAA0B,CAAE;gBAC7CzE,QAAQtC,SAASiB;YACnB;YAEAI,OAAO2F,MAAM,CAAChH,SAASK;YAEvBL,QAAQiH,WAAW,GAAGA;YAEtBjH,QAAQuD,iBAAiB,GAAGA,8BAAiB;YAE7C,+DAA+D;YAC/DvD,QAAQkH,WAAW,GAAG,CAAC,GAAGJ,OACxBtH,kCAAgB,CAACqF,GAAG,CAACiC;YAEvB,+DAA+D;YAC/D9G,QAAQmH,aAAa,GAAG,CAACC,WACvB5H,kCAAgB,CAAC6H,MAAM,CAACD;YAE1B,+DAA+D;YAC/DpH,QAAQsH,UAAU,GAAG,CAAC,GAAGR,OACvBpH,iCAAe,CAACmF,GAAG,CAACiC;YAEtB,+DAA+D;YAC/D9G,QAAQuH,YAAY,GAAG,CAACC,UACtB9H,iCAAe,CAAC2H,MAAM,CAACG;YAEzB,4EAA4E;YAC5E,kFAAkF;YAClF,MAAMC,8BAA8BzH,QAAQ0H,MAAM,CAACC,GAAG,CACpD;YAEFtG,OAAOW,cAAc,CAAChC,SAASyH,6BAA6B;gBAC1DpF,YAAY;gBACZD,OAAO5D;YACT;YAEA,OAAOwB;QACT;IACF;IAEA,MAAM4H,yBAAyBrF,0BAA0BC;IACzDA,QAAQxC,OAAO,CAAC6H,gBAAgB,CAAC,SAASD;IAC1C,MAAME,6BAA6BnF,8BAA8BH;IACjEA,QAAQxC,OAAO,CAAC6H,gBAAgB,CAC9B,sBACAC;IAGFC,IAAAA,4CAAyB,EAACvF,QAAQxC,OAAO,CAAC0B,KAAK;IAC/C,uFAAuF;IACvF,mEAAmE;IACnEsG,IAAAA,0CAAuB,EAACxF,QAAQxC,OAAO,CAAC0B,KAAK;IAE7C,OAAO;QACLc;QACAvC,OAAO,IAAIX;QACXqE,aAAa,IAAIC;IACnB;AACF;AAWA,SAASqE,uBAAuBvE,OAA6B;IAC3D,IAAIwE,wBAAwB3I,oBAAoB0C,GAAG,CAACyB,QAAQyE,UAAU;IACtE,IAAI,CAACD,uBAAuB;QAC1BA,wBAAwBzE,oBAAoBC;QAC5CnE,oBAAoB4C,GAAG,CAACuB,QAAQyE,UAAU,EAAED;IAC9C;IACA,OAAOA;AACT;AAQO,eAAezJ,iBAAiBiF,OAA6B;IAMlE,IAAI0E;IAIJ,IAAI1E,QAAQ2E,QAAQ,EAAE;QACpBD,oBACE/I,eAAe4C,GAAG,CAACyB,QAAQyE,UAAU,KACpC,MAAMF,uBAAuBvE;IAClC;IAEA,IAAI,CAAC0E,mBAAmB;QACtBA,oBAAoB,MAAM3E,oBAAoBC;QAC9CrE,eAAe8C,GAAG,CAACuB,QAAQyE,UAAU,EAAEC;IACzC;IAEA,MAAME,gBAAgBF;IAEtB,MAAMG,oBAAoB,CAACC;QACzB,IAAI,CAACF,cAAcrI,KAAK,CAACC,GAAG,CAACsI,WAAW;YACtC,MAAMC,UAAUC,IAAAA,gBAAY,EAACF,UAAU;YACvC,IAAI;gBACFG,IAAAA,gBAAY,EAACF,SAASH,cAAc9F,OAAO,CAACxC,OAAO,EAAE;oBACnD4I,UAAUJ;gBACZ;gBACAF,cAAcrI,KAAK,CAACkC,GAAG,CAACqG,UAAUC;YACpC,EAAE,OAAOvJ,OAAO;gBACd,IAAIwE,QAAQ2E,QAAQ,EAAE;oBACpBC,iCAAAA,cAAerI,KAAK,CAACE,MAAM,CAACqI;gBAC9B;gBACA,MAAMtJ;YACR;QACF;IACF;IAEA,OAAO;QAAE,GAAGoJ,aAAa;QAAEC;IAAkB;AAC/C"}