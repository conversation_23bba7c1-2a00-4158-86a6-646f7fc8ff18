{"version": 3, "sources": ["../../../src/server/og/image-response.ts"], "sourcesContent": ["type OgModule = typeof import('next/dist/compiled/@vercel/og')\n\nfunction importModule(): Promise<\n  typeof import('next/dist/compiled/@vercel/og')\n> {\n  return import(\n    process.env.NEXT_RUNTIME === 'edge'\n      ? 'next/dist/compiled/@vercel/og/index.edge.js'\n      : 'next/dist/compiled/@vercel/og/index.node.js'\n  )\n}\n\n/**\n * The ImageResponse class allows you to generate dynamic images using JSX and CSS.\n * This is useful for generating social media images such as Open Graph images, Twitter cards, and more.\n *\n * Read more: [Next.js Docs: `ImageResponse`](https://nextjs.org/docs/app/api-reference/functions/image-response)\n */\nexport class ImageResponse extends Response {\n  public static displayName = 'ImageResponse'\n  constructor(...args: ConstructorParameters<OgModule['ImageResponse']>) {\n    const readable = new ReadableStream({\n      async start(controller) {\n        const OGImageResponse: typeof import('next/dist/compiled/@vercel/og').ImageResponse =\n          // So far we have to manually determine which build to use,\n          // as the auto resolving is not working\n          (await importModule()).ImageResponse\n        const imageResponse = new OGImageResponse(...args) as Response\n\n        if (!imageResponse.body) {\n          return controller.close()\n        }\n\n        const reader = imageResponse.body!.getReader()\n        while (true) {\n          const { done, value } = await reader.read()\n          if (done) {\n            return controller.close()\n          }\n          controller.enqueue(value)\n        }\n      },\n    })\n\n    const options = args[1] || {}\n\n    const headers = new Headers({\n      'content-type': 'image/png',\n      'cache-control':\n        process.env.NODE_ENV === 'development'\n          ? 'no-cache, no-store'\n          : 'public, immutable, no-transform, max-age=31536000',\n    })\n    if (options.headers) {\n      const newHeaders = new Headers(options.headers)\n      newHeaders.forEach((value, key) => headers.set(key, value))\n    }\n    super(readable, {\n      headers,\n      status: options.status,\n      statusText: options.statusText,\n    })\n  }\n}\n"], "names": ["ImageResponse", "importModule", "process", "env", "NEXT_RUNTIME", "Response", "displayName", "constructor", "args", "readable", "ReadableStream", "start", "controller", "OGImageResponse", "imageResponse", "body", "close", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "value", "read", "enqueue", "options", "headers", "Headers", "NODE_ENV", "newHeaders", "for<PERSON>ach", "key", "set", "status", "statusText"], "mappings": ";;;;+BAkBaA;;;eAAAA;;;AAhBb,SAASC;IAGP,OAAO,MAAM,CACXC,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,gDACA;AAER;AAQO,MAAMJ,sBAAsBK;qBACnBC,cAAc;IAC5BC,YAAY,GAAGC,IAAsD,CAAE;QACrE,MAAMC,WAAW,IAAIC,eAAe;YAClC,MAAMC,OAAMC,UAAU;gBACpB,MAAMC,kBAGJ,AAFA,2DAA2D;gBAC3D,uCAAuC;gBACtC,CAAA,MAAMZ,cAAa,EAAGD,aAAa;gBACtC,MAAMc,gBAAgB,IAAID,mBAAmBL;gBAE7C,IAAI,CAACM,cAAcC,IAAI,EAAE;oBACvB,OAAOH,WAAWI,KAAK;gBACzB;gBAEA,MAAMC,SAASH,cAAcC,IAAI,CAAEG,SAAS;gBAC5C,MAAO,KAAM;oBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMH,OAAOI,IAAI;oBACzC,IAAIF,MAAM;wBACR,OAAOP,WAAWI,KAAK;oBACzB;oBACAJ,WAAWU,OAAO,CAACF;gBACrB;YACF;QACF;QAEA,MAAMG,UAAUf,IAAI,CAAC,EAAE,IAAI,CAAC;QAE5B,MAAMgB,UAAU,IAAIC,QAAQ;YAC1B,gBAAgB;YAChB,iBACEvB,QAAQC,GAAG,CAACuB,QAAQ,KAAK,gBACrB,uBACA;QACR;QACA,IAAIH,QAAQC,OAAO,EAAE;YACnB,MAAMG,aAAa,IAAIF,QAAQF,QAAQC,OAAO;YAC9CG,WAAWC,OAAO,CAAC,CAACR,OAAOS,MAAQL,QAAQM,GAAG,CAACD,KAAKT;QACtD;QACA,KAAK,CAACX,UAAU;YACde;YACAO,QAAQR,QAAQQ,MAAM;YACtBC,YAAYT,QAAQS,UAAU;QAChC;IACF;AACF"}