{"version": 3, "sources": ["../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-toolbar/error-overlay-toolbar.tsx"], "sourcesContent": ["import type { DebugInfo } from '../../../../types'\nimport { NodejsInspectorButton } from './nodejs-inspector-button'\nimport { CopyStackTraceButton } from './copy-stack-trace-button'\nimport { DocsLinkButton } from './docs-link-button'\n\ntype ErrorOverlayToolbarProps = {\n  error: Error\n  debugInfo: DebugInfo | undefined\n}\n\nexport function ErrorOverlayToolbar({\n  error,\n  debugInfo,\n}: ErrorOverlayToolbarProps) {\n  return (\n    <span className=\"error-overlay-toolbar\">\n      <CopyStackTraceButton error={error} />\n      <DocsLinkButton errorMessage={error.message} />\n      <NodejsInspectorButton\n        devtoolsFrontendUrl={debugInfo?.devtoolsFrontendUrl}\n      />\n    </span>\n  )\n}\n\nexport const styles = `\n  .error-overlay-toolbar {\n    display: flex;\n    gap: 6px;\n  }\n\n  .nodejs-inspector-button,\n  .copy-stack-trace-button,\n  .docs-link-button {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    width: var(--size-28);\n    height: var(--size-28);\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: 1px solid var(--color-gray-alpha-400);\n    box-shadow: var(--shadow-small);\n    border-radius: var(--rounded-full);\n\n    svg {\n      width: var(--size-14);\n      height: var(--size-14);\n    }\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n\n    &:not(:disabled):hover {\n      background: var(--color-gray-alpha-100);\n    }\n\n    &:not(:disabled):active {\n      background: var(--color-gray-alpha-200);\n    }\n\n    &:disabled {\n      background-color: var(--color-gray-100);\n      cursor: not-allowed;\n    }\n  }\n\n  .error-overlay-toolbar-button-icon {\n    color: var(--color-gray-900);\n  }\n`\n"], "names": ["ErrorOverlayToolbar", "styles", "error", "debugInfo", "span", "className", "CopyStackTraceButton", "DocsLinkButton", "errorMessage", "message", "NodejsInspectorButton", "devtoolsFrontendUrl"], "mappings": ";;;;;;;;;;;;;;;IAUgBA,mBAAmB;eAAnBA;;IAeHC,MAAM;eAANA;;;;uCAxByB;sCACD;gCACN;AAOxB,SAASD,oBAAoB,KAGT;IAHS,IAAA,EAClCE,KAAK,EACLC,SAAS,EACgB,GAHS;IAIlC,qBACE,sBAACC;QAAKC,WAAU;;0BACd,qBAACC,0CAAoB;gBAACJ,OAAOA;;0BAC7B,qBAACK,8BAAc;gBAACC,cAAcN,MAAMO,OAAO;;0BAC3C,qBAACC,4CAAqB;gBACpBC,mBAAmB,EAAER,6BAAAA,UAAWQ,mBAAmB;;;;AAI3D;AAEO,MAAMV,SAAU"}