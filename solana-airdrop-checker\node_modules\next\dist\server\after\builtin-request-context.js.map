{"version": 3, "sources": ["../../../src/server/after/builtin-request-context.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from '../app-render/async-local-storage'\n\nexport function getBuiltinRequestContext():\n  | BuiltinRequestContextValue\n  | undefined {\n  const _globalThis = globalThis as GlobalThisWithRequestContext\n  const ctx = _globalThis[NEXT_REQUEST_CONTEXT_SYMBOL]\n  return ctx?.get()\n}\n\nconst NEXT_REQUEST_CONTEXT_SYMBOL = Symbol.for('@next/request-context')\n\ntype GlobalThisWithRequestContext = typeof globalThis & {\n  [NEXT_REQUEST_CONTEXT_SYMBOL]?: BuiltinRequestContext\n}\n\n/** A request context provided by the platform. */\nexport type BuiltinRequestContext = {\n  get(): BuiltinRequestContextValue | undefined\n}\n\nexport type RunnableBuiltinRequestContext = BuiltinRequestContext & {\n  run<T>(value: BuiltinRequestContextValue, callback: () => T): T\n}\n\nexport type BuiltinRequestContextValue = {\n  waitUntil?: WaitUntil\n}\nexport type WaitUntil = (promise: Promise<any>) => void\n\n/** \"@next/request-context\" has a different signature from AsyncLocalStorage,\n * matching [AsyncContext.Variable](https://github.com/tc39/proposal-async-context).\n * We don't need a full AsyncContext adapter here, just having `.get()` is enough\n */\nexport function createLocalRequestContext(): RunnableBuiltinRequestContext {\n  const storage = createAsyncLocalStorage<BuiltinRequestContextValue>()\n  return {\n    get: () => storage.getStore(),\n    run: (value, callback) => storage.run(value, callback),\n  }\n}\n"], "names": ["createLocalRequestContext", "getBuiltinRequestContext", "_globalThis", "globalThis", "ctx", "NEXT_REQUEST_CONTEXT_SYMBOL", "get", "Symbol", "for", "storage", "createAsyncLocalStorage", "getStore", "run", "value", "callback"], "mappings": ";;;;;;;;;;;;;;;IAkCgBA,yBAAyB;eAAzBA;;IAhCAC,wBAAwB;eAAxBA;;;mCAFwB;AAEjC,SAASA;IAGd,MAAMC,cAAcC;IACpB,MAAMC,MAAMF,WAAW,CAACG,4BAA4B;IACpD,OAAOD,uBAAAA,IAAKE,GAAG;AACjB;AAEA,MAAMD,8BAA8BE,OAAOC,GAAG,CAAC;AAwBxC,SAASR;IACd,MAAMS,UAAUC,IAAAA,0CAAuB;IACvC,OAAO;QACLJ,KAAK,IAAMG,QAAQE,QAAQ;QAC3BC,KAAK,CAACC,OAAOC,WAAaL,QAAQG,GAAG,CAACC,OAAOC;IAC/C;AACF"}