{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n} from '../../client/components/app-router-headers'\nimport {\n  createTrackedMetadataContext,\n  createMetadataContext,\n} from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { setReferenceManifestsSingleton } from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  getFirstDynamicReason,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { ServerPrerenderStreamResult } from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n  prerenderServerWithPhases,\n  prerenderClientWithPhases,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport './clean-async-snapshot.external'\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { isUseCacheTimeoutError } from '../use-cache/use-cache-errors'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n    previouslyRevalidatedTags,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  // Align the segment with parallel-route-default in next-app-loader\n  const components = loaderTree[2]\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['not-found'],\n        },\n      ],\n    },\n    components,\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      metadataContext: createTrackedMetadataContext(\n        url.pathname,\n        ctx.renderOpts,\n        workStore\n      ),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex\n              pagePath={ctx.pagePath}\n              statusCode={ctx.res.statusCode}\n              isPossibleServerAction={ctx.isPossibleServerAction}\n            />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={getFlightViewportKey(requestId)} />\n            {/* Not add requestId as react key to ensure segment prefetch could result consistently if nothing changed */}\n            <MetadataTree key={getFlightMetadataKey(requestId)} />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      ctx.workStore.route,\n      requestStore\n    )\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  if (!renderOpts.dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    componentMod.tree,\n    getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash: req.cookies[NEXT_HMR_REFRESH_HASH_COOKIE],\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    componentMod.renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling\n  await cacheSignal.cacheReady()\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: workStore.fetchMetrics,\n    devRenderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    errorType: is404 ? 'not-found' : undefined,\n    parsedQuery: query,\n    metadataContext: createTrackedMetadataContext(\n      url.pathname,\n      ctx.renderOpts,\n      workStore\n    ),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      <ViewportTree key={getFlightViewportKey(ctx.requestId)} />\n      {/* Not add requestId as react key to ensure segment prefetch could result consistently if nothing changed */}\n      <MetadataTree />\n    </React.Fragment>\n  )\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    requestId,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    // We create an untracked metadata context here because we can't postpone\n    // again during the error render.\n    metadataContext: createMetadataContext(url.pathname, ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  // {/* Adding requestId as react key to make metadata remount for each render */}\n  const metadata = <MetadataTree key={getFlightMetadataKey(requestId)} />\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex\n        pagePath={ctx.pagePath}\n        statusCode={ctx.res.statusCode}\n        isPossibleServerAction={ctx.isPossibleServerAction}\n      />\n      {/* Adding requestId as react key to make metadata remount for each render */}\n      <ViewportTree key={getFlightViewportKey(requestId)} />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      {metadata}\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head>{metadata}</head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n  ServerInsertedHTMLProvider,\n  ServerInsertedMetadataProvider,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  ServerInsertedMetadataProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedMetadataProvider>\n        <ServerInsertedHTMLProvider>\n          <AppRouter\n            actionQueue={actionQueue}\n            globalErrorComponentAndStyles={response.G}\n            assetPrefix={response.p}\n          />\n        </ServerInsertedHTMLProvider>\n      </ServerInsertedMetadataProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedMetadataProvider,\n  ServerInsertedHTMLProvider,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedMetadataProvider: React.ComponentType<{ children: JSX.Element }>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ServerInsertedMetadataProvider>\n      <ServerInsertedHTMLProvider>\n        <AppRouter\n          actionQueue={actionQueue}\n          globalErrorComponentAndStyles={response.G}\n          assetPrefix={response.p}\n        />\n      </ServerInsertedHTMLProvider>\n    </ServerInsertedMetadataProvider>\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n    // @ts-ignore\n    globalThis.__next_require__ = instrumented.require\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we wrap the loadChunk in this tracking. This allows us\n    // to treat chunk loading with similar semantics as cache reads to avoid\n    // async loading chunks from causing a prerender to abort too early.\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      trackChunkLoading(loadingChunk)\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to client components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = require('next/dist/compiled/nanoid').nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      workStore,\n      loaderTree\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidUsageError) {\n      throw workStore.invalidUsageError\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } else {\n      // Copy the cache control value onto the render result metadata.\n      metadata.cacheControl = {\n        revalidate:\n          response.collectedRevalidate >= INFINITE_CACHE\n            ? false\n            : response.collectedRevalidate,\n        expire:\n          response.collectedExpire >= INFINITE_CACHE\n            ? undefined\n            : response.collectedExpire,\n      }\n    }\n\n    // provide bailout info for debugging\n    if (metadata.cacheControl?.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.devRenderResumeDataCache ??\n      postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            workStore,\n            notFoundLoaderTree,\n            formState,\n            postponedState\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      workStore,\n      loaderTree,\n      formState,\n      postponedState\n    )\n\n    if (workStore.invalidUsageError) {\n      throw workStore.invalidUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.devRenderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null\n): Promise<ReadableStream<Uint8Array>> {\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(ctx.nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      renderOpts.dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      renderOpts.experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame(url: string, _functionName: string): boolean {\n                // The default implementation filters out <anonymous> stack frames\n                // but we want to retain them because current Server Components and\n                // built-in Components in parent stacks don't have source location.\n                return !url.startsWith('node:') && !url.includes('node_modules')\n              },\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        workStore.route,\n        requestStore\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          ctx.nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = require('react-dom/server.edge')\n          .resume as (typeof import('react-dom/server.edge'))['resume']\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          postponed,\n          {\n            onError: htmlRendererErrorHandler,\n            nonce: ctx.nonce,\n          }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            ctx.nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = require('react-dom/server.edge')\n      .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={ctx.nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce: ctx.nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: renderOpts.reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath: renderOpts.basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      renderOpts.supportsDynamicResponse !== true ||\n      !!renderOpts.shouldWaitOnAllReady\n\n    const validateRootLayout = renderOpts.dev\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        ctx.nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer: require('react-dom/server.edge'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={ctx.nonce}\n            />\n          ),\n          streamOptions: {\n            nonce: ctx.nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        renderOpts.supportsDynamicResponse !== true ||\n        !!renderOpts.shouldWaitOnAllReady\n      const validateRootLayout = renderOpts.dev\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          ctx.nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  route: string,\n  requestStore: RequestStore\n): Promise<void> {\n  const { componentMod: ComponentMod, implicitTags } = ctx\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    ctx.getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  const cacheSignal = new CacheSignal()\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  const initialClientController = new AbortController()\n  const initialClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialClientController.signal,\n    controller: initialClientController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  let initialServerStream\n  try {\n    initialServerStream = workUnitAsyncStorage.run(\n      initialServerPrerenderStore,\n      ComponentMod.renderToReadableStream,\n      firstAttemptRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // The render aborted before this error was handled which indicates\n            // the error is caused by unfinished components within the render\n            return\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n        signal: initialServerRenderController.signal,\n      }\n    )\n  } catch (err: unknown) {\n    if (\n      initialServerPrerenderController.signal.aborted ||\n      initialServerRenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, route)\n    }\n  }\n\n  const nonce = '1'\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider } = createServerInsertedMetadata(nonce)\n\n  if (initialServerStream) {\n    const [warmupStream, renderStream] = initialServerStream.tee()\n    initialServerStream = null\n    // Before we attempt the SSR initial render we need to ensure all client modules\n    // are already loaded.\n    await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={renderStream}\n        preinitScripts={() => {}}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (initialClientController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n      }\n    )\n    pendingInitialClientResult.catch((err: unknown) => {\n      if (initialClientController.signal.aborted) {\n        // We aborted the render normally and can ignore this error\n      } else {\n        // We're going to retry to so we normally would suppress this error but\n        // when verbose logging is on we print it\n        if (process.env.__NEXT_VERBOSE_LOGGING) {\n          printDebugThrownValueForProspectiveRender(err, route)\n        }\n      }\n    })\n  }\n\n  await cacheSignal.cacheReady()\n  // It is important that we abort the SSR render first to avoid\n  // connection closed errors from having an incomplete RSC stream\n  initialClientController.abort()\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We've now filled caches and triggered any inadvertent sync bailouts\n  // due to lazy module initialization. We can restart our render to capture results\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(false)\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  const finalClientController = new AbortController()\n  const clientDynamicTracking = createDynamicTrackingState(false)\n  const dynamicValidation = createDynamicValidationState()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n    hmrRefreshHash,\n  }\n\n  const finalServerPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverPrerenderStreamResult = await prerenderServerWithPhases(\n    finalServerController.signal,\n    () =>\n      workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.renderToReadableStream,\n        finalServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          onError: (err) => {\n            if (isUseCacheTimeoutError(err)) {\n              return err.digest\n            }\n\n            if (\n              finalServerController.signal.aborted &&\n              isPrerenderInterruptedError(err)\n            ) {\n              return err.digest\n            }\n\n            return getDigestForWellKnownError(err)\n          },\n          signal: finalServerController.signal,\n        }\n      ),\n    () => {\n      finalServerController.abort()\n    }\n  )\n\n  let rootDidError = false\n  const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n  try {\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    await prerenderClientWithPhases(\n      () =>\n        workUnitAsyncStorage.run(\n          finalClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={serverPhasedStream}\n            preinitScripts={() => {}}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          {\n            signal: finalClientController.signal,\n            onError: (err, errorInfo) => {\n              if (isUseCacheTimeoutError(err)) {\n                dynamicValidation.dynamicErrors.push(err)\n\n                return\n              }\n\n              if (\n                isPrerenderInterruptedError(err) ||\n                finalClientController.signal.aborted\n              ) {\n                if (!rootDidError) {\n                  // If the root errored before we observe this error then it wasn't caused by something dynamic.\n                  // If the root did not error or is erroring because of a sync dynamic API or a prerender interrupt error\n                  // then we are a dynamic route.\n                  requestStore.usedDynamic = true\n                }\n\n                const componentStack = errorInfo.componentStack\n                if (typeof componentStack === 'string') {\n                  trackAllowedDynamicAccess(\n                    route,\n                    componentStack,\n                    dynamicValidation,\n                    serverDynamicTracking,\n                    clientDynamicTracking\n                  )\n                }\n                return\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n          }\n        ),\n      () => {\n        finalClientController.abort()\n        serverPhasedStream.assertExhausted()\n      }\n    )\n  } catch (err) {\n    rootDidError = true\n    if (\n      isPrerenderInterruptedError(err) ||\n      finalClientController.signal.aborted\n    ) {\n      // we don't have a root because the abort errored in the root. We can just ignore this error\n    } else {\n      // If an error is thrown in the root before prerendering is aborted, we\n      // don't want to rethrow it here, otherwise this would lead to a hanging\n      // response and unhandled rejection. We also don't want to log it, because\n      // it's most likely already logged as part of the normal render. So we\n      // just fall through here, to make sure `resolveValidation` is called.\n    }\n  }\n\n  function LogDynamicValidation() {\n    try {\n      throwIfDisallowedDynamic(\n        route,\n        dynamicValidation,\n        serverDynamicTracking,\n        clientDynamicTracking\n      )\n    } catch {}\n    return null\n  }\n\n  resolveValidation(<LogDynamicValidation />)\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore,\n  tree: LoaderTree\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n  } = ctx\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!renderOpts.experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof renderOpts.experimental.staleTimes?.static === 'number'\n      ? renderOpts.experimental.staleTimes.static\n      : stale\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (renderOpts.experimental.dynamicIO) {\n      if (renderOpts.experimental.isRoutePPREnabled) {\n        /**\n         * dynamicIO with PPR\n         *\n         * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n         * Once we have settled all cache reads we restart the render and abort after a single Task.\n         *\n         * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n         * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n         * and a synchronous abort might prevent us from filling all caches.\n         *\n         * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n         * and the reactServerIsDynamic value to determine how to treat the resulting render\n         */\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        // The cacheSignal helps us track whether caches are still filling or we are ready\n        // to cut the render off.\n        const cacheSignal = new CacheSignal()\n\n        // The resume data cache here should use a fresh instance as it's\n        // performing a fresh prerender. If we get to implementing the\n        // prerendering of an already prerendered page, we should use the passed\n        // resume data cache instead.\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const initialServerPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const pendingInitialServerResult = workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          ComponentMod.prerender,\n          initialServerPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (initialServerPrerenderController.signal.aborted) {\n                // The render aborted before this error was handled which indicates\n                // the error is caused by unfinished components within the render\n                return\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            // we don't care to track postpones during the prospective render because we need\n            // to always do a final render anyway\n            onPostpone: undefined,\n            // We don't want to stop rendering until the cacheSignal is complete so we pass\n            // a different signal to this render call than is used by dynamic APIs to signify\n            // transitioning out of the prerender environment\n            signal: initialServerRenderController.signal,\n          }\n        )\n\n        await cacheSignal.cacheReady()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        let initialServerResult\n        try {\n          initialServerResult = await createReactServerPrerenderResult(\n            pendingInitialServerResult\n          )\n        } catch (err) {\n          if (\n            initialServerRenderController.signal.aborted ||\n            initialServerPrerenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerResult) {\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(\n            initialServerResult.asStream(),\n            clientReferenceManifest\n          )\n\n          const initialClientController = new AbortController()\n          const initialClientPrerenderStore: PrerenderStore = {\n            type: 'prerender',\n            phase: 'render',\n            rootParams,\n            implicitTags,\n            renderSignal: initialClientController.signal,\n            controller: initialClientController,\n            cacheSignal: null,\n            dynamicTracking: null,\n            revalidate: INFINITE_CACHE,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags.tags],\n            prerenderResumeDataCache,\n            hmrRefreshHash: undefined,\n          }\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          await prerenderAndAbortInSequentialTasks(\n            () =>\n              workUnitAsyncStorage.run(\n                initialClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={initialServerResult.asUnclosingStream()}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={nonce}\n                />,\n                {\n                  signal: initialClientController.signal,\n                  onError: (err) => {\n                    const digest = getDigestForWellKnownError(err)\n\n                    if (digest) {\n                      return digest\n                    }\n\n                    if (initialClientController.signal.aborted) {\n                      // These are expected errors that might error the prerender. we ignore them.\n                    } else if (\n                      process.env.NEXT_DEBUG_BUILD ||\n                      process.env.__NEXT_VERBOSE_LOGGING\n                    ) {\n                      // We don't normally log these errors because we are going to retry anyway but\n                      // it can be useful for debugging Next.js itself to get visibility here when needed\n                      printDebugThrownValueForProspectiveRender(\n                        err,\n                        workStore.route\n                      )\n                    }\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              initialClientController.abort()\n            }\n          ).catch((err) => {\n            if (\n              initialServerRenderController.signal.aborted ||\n              isPrerenderInterruptedError(err)\n            ) {\n              // These are expected errors that might error the prerender. we ignore them.\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              // We don't normally log these errors because we are going to retry anyway but\n              // it can be useful for debugging Next.js itself to get visibility here when needed\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          })\n        }\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalRenderPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n          finalRenderPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n        let prerenderIsPending = true\n        const reactServerResult = (reactServerPrerenderResult =\n          await createReactServerPrerenderResult(\n            prerenderAndAbortInSequentialTasks(\n              async () => {\n                const prerenderResult = await workUnitAsyncStorage.run(\n                  // The store to scope\n                  finalRenderPrerenderStore,\n                  // The function to run\n                  ComponentMod.prerender,\n                  // ... the arguments for the function to run\n                  finalAttemptRSCPayload,\n                  clientReferenceManifest.clientModules,\n                  {\n                    onError: (err: unknown) => {\n                      return serverComponentsErrorHandler(err)\n                    },\n                    signal: finalServerController.signal,\n                  }\n                )\n                prerenderIsPending = false\n                return prerenderResult\n              },\n              () => {\n                if (finalServerController.signal.aborted) {\n                  // If the server controller is already aborted we must have called something\n                  // that required aborting the prerender synchronously such as with new Date()\n                  serverIsDynamic = true\n                  return\n                }\n\n                if (prerenderIsPending) {\n                  // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                  // there is something unfinished.\n                  serverIsDynamic = true\n                }\n                finalServerController.abort()\n              }\n            )\n          ))\n\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const finalClientController = new AbortController()\n        const finalClientPrerenderStore: PrerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // For HTML Generation we don't need to track cache reads (RSC only)\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        }\n\n        let clientIsDynamic = false\n        let dynamicValidation = createDynamicValidationState()\n\n        const prerender = require('react-dom/static.edge')\n          .prerender as (typeof import('react-dom/static.edge'))['prerender']\n        let { prelude, postponed } = await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore.route,\n                        componentStack,\n                        dynamicValidation,\n                        serverDynamicTracking,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: renderOpts.reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalRenderPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          if (postponed != null) {\n            // Dynamic HTML case\n            metadata.postponed = await getDynamicHTMLPostponedState(\n              postponed,\n              fallbackRouteParams,\n              prerenderResumeDataCache\n            )\n          } else {\n            // Dynamic Data case\n            metadata.postponed = await getDynamicDataPostponedState(\n              prerenderResumeDataCache\n            )\n          }\n          reactServerResult.consume()\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueDynamicPrerender(prelude, {\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: selectStaleTime(finalRenderPrerenderStore.stale),\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        } else {\n          // Static case\n          if (workStore.forceDynamic) {\n            throw new StaticGenBailoutError(\n              'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n            )\n          }\n\n          let htmlStream = prelude\n          if (postponed != null) {\n            // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n            // so we can set all the postponed boundaries to client render mode before we store the HTML response\n            const resume = require('react-dom/server.edge')\n              .resume as (typeof import('react-dom/server.edge'))['resume']\n\n            // We don't actually want to render anything so we just pass a stream\n            // that never resolves. The resume call is going to abort immediately anyway\n            const foreverStream = new ReadableStream<Uint8Array>()\n\n            const resumeStream = await resume(\n              <App\n                reactServerStream={foreverStream}\n                preinitScripts={() => {}}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={nonce}\n              />,\n              JSON.parse(JSON.stringify(postponed)),\n              {\n                signal: createPostponedAbortSignal('static prerender resume'),\n                onError: htmlRendererErrorHandler,\n                nonce,\n              }\n            )\n\n            // First we write everything from the prerender, then we write everything from the aborted resume render\n            htmlStream = chainStreams(prelude, resumeStream)\n          }\n\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueStaticPrerender(htmlStream, {\n              inlinedDataStream: createInlinedDataReadableStream(\n                reactServerResult.consumeAsStream(),\n                nonce,\n                formState\n              ),\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: selectStaleTime(finalRenderPrerenderStore.stale),\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        }\n      } else {\n        /**\n         * dynamicIO without PPR\n         *\n         * The general approach is to render the RSC tree first allowing for any inflight\n         * caches to resolve. Once we have settled inflight caches we can check and see if any\n         * synchronous dynamic APIs were used. If so we don't need to bother doing anything more\n         * because the page will be dynamic on re-render anyway\n         *\n         * If no sync dynamic APIs were used we then re-render and abort after a single Task.\n         * If the render errors we know that the page has some dynamic IO. This assumes and relies\n         * upon caches reading from a in process memory cache and resolving in a microtask. While this\n         * is true from our own default cache implementation and if you don't exceed our LRU size it\n         * might not be true for custom cache implementations.\n         *\n         * Future implementations can do some different strategies during build like using IPC to\n         * synchronously fill caches during this special rendering mode. For now this heuristic should work\n         */\n\n        const cache = workStore.incrementalCache\n        if (!cache) {\n          throw new Error(\n            'Expected incremental cache to exist. This is a bug in Next.js'\n          )\n        }\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        const cacheSignal = new CacheSignal()\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        const initialClientController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: initialClientController.signal,\n          controller: initialClientController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        let initialServerStream\n        try {\n          initialServerStream = workUnitAsyncStorage.run(\n            initialServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            firstAttemptRSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (\n                  initialServerPrerenderController.signal.aborted ||\n                  initialServerRenderController.signal.aborted\n                ) {\n                  // The render aborted before this error was handled which indicates\n                  // the error is caused by unfinished components within the render\n                  return\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              signal: initialServerRenderController.signal,\n            }\n          )\n        } catch (err: unknown) {\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerStream) {\n          const [warmupStream, renderStream] = initialServerStream.tee()\n          initialServerStream = null\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const pendingInitialClientResult = workUnitAsyncStorage.run(\n            initialClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={renderStream}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={nonce}\n            />,\n            {\n              signal: initialClientController.signal,\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (initialClientController.signal.aborted) {\n                  // These are expected errors that might error the prerender. we ignore them.\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  // We don't normally log these errors because we are going to retry anyway but\n                  // it can be useful for debugging Next.js itself to get visibility here when needed\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              bootstrapScripts: [bootstrapScript],\n            }\n          )\n          pendingInitialClientResult.catch((err: unknown) => {\n            if (initialClientController.signal.aborted) {\n              // We aborted the render normally and can ignore this error\n            } else {\n              // We're going to retry to so we normally would suppress this error but\n              // when verbose logging is on we print it\n              if (process.env.__NEXT_VERBOSE_LOGGING) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            }\n          })\n        }\n\n        await cacheSignal.cacheReady()\n        // It is important that we abort the SSR render first to avoid\n        // connection closed errors from having an incomplete RSC stream\n        initialClientController.abort()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        // We've now filled caches and triggered any inadvertant sync bailouts\n        // due to lazy module initialization. We can restart our render to capture results\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        let clientIsDynamic = false\n        const finalClientController = new AbortController()\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const dynamicValidation = createDynamicValidationState()\n\n        const finalClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          hmrRefreshHash: undefined,\n        })\n\n        const finalServerPayload = await workUnitAsyncStorage.run(\n          finalServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const serverPrerenderStreamResult = (reactServerPrerenderResult =\n          await prerenderServerWithPhases(\n            finalServerController.signal,\n            () =>\n              workUnitAsyncStorage.run(\n                finalServerPrerenderStore,\n                ComponentMod.renderToReadableStream,\n                finalServerPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  onError: (err: unknown) => {\n                    if (finalServerController.signal.aborted) {\n                      serverIsDynamic = true\n                      if (isPrerenderInterruptedError(err)) {\n                        return err.digest\n                      }\n                      return getDigestForWellKnownError(err)\n                    }\n\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              ),\n            () => {\n              finalServerController.abort()\n            }\n          ))\n\n        let htmlStream\n        const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n        try {\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const result = await prerenderClientWithPhases(\n            () =>\n              workUnitAsyncStorage.run(\n                finalClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={serverPhasedStream}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={nonce}\n                />,\n                {\n                  signal: finalClientController.signal,\n                  onError: (err: unknown, errorInfo: ErrorInfo) => {\n                    if (\n                      isPrerenderInterruptedError(err) ||\n                      finalClientController.signal.aborted\n                    ) {\n                      clientIsDynamic = true\n\n                      const componentStack: string | undefined = (\n                        errorInfo as any\n                      ).componentStack\n                      if (typeof componentStack === 'string') {\n                        trackAllowedDynamicAccess(\n                          workStore.route,\n                          componentStack,\n                          dynamicValidation,\n                          serverDynamicTracking,\n                          clientDynamicTracking\n                        )\n                      }\n                      return\n                    }\n\n                    return htmlRendererErrorHandler(err, errorInfo)\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              finalClientController.abort()\n              serverPhasedStream.assertExhausted()\n            }\n          )\n          htmlStream = result.prelude\n        } catch (err) {\n          if (\n            isPrerenderInterruptedError(err) ||\n            finalClientController.signal.aborted\n          ) {\n            // we don't have a root because the abort errored in the root. We can just ignore this error\n          } else {\n            // This error is something else and should bubble up\n            throw err\n          }\n        }\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          const dynamicReason = serverIsDynamic\n            ? getFirstDynamicReason(serverDynamicTracking)\n            : getFirstDynamicReason(clientDynamicTracking)\n          if (dynamicReason) {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          } else {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically it accessed data without explicitly caching it. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          }\n        }\n\n        const flightData = await streamToBuffer(\n          serverPrerenderStreamResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalClientPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        const validateRootLayout = renderOpts.dev\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueFizzStream(htmlStream!, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              serverPrerenderStreamResult.asStream(),\n              nonce,\n              formState\n            ),\n            isStaticGeneration: true,\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            validateRootLayout,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n        }\n      }\n    } else if (renderOpts.experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(\n        renderOpts.isDebugDynamicAccesses\n      )\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = require('react-dom/static.edge')\n        .prerender as (typeof import('react-dom/static.edge'))['prerender']\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              appendHeader(key, value)\n            })\n          },\n          maxHeadersLength: renderOpts.reactMaxHeadersLength,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = require('react-dom/server.edge')\n            .resume as (typeof import('react-dom/server.edge'))['resume']\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = require('react-dom/server.edge')\n        .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      const fizzStream = await renderToInitialFizzStream({\n        ReactDOMServer: require('react-dom/server.edge'),\n        element: (\n          <ErrorApp\n            reactServerStream={errorServerStream}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            preinitScripts={errorPreinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            nonce={nonce}\n          />\n        ),\n        streamOptions: {\n          nonce,\n          // Include hydration scripts in the HTML\n          bootstrapScripts: [errorBootstrapScript],\n          formState,\n        },\n      })\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const validateRootLayout = renderOpts.dev\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath: renderOpts.basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst loadingChunks: Set<Promise<unknown>> = new Set()\nconst chunkListeners: Array<(x?: unknown) => void> = []\n\nfunction trackChunkLoading(load: Promise<unknown>) {\n  loadingChunks.add(load)\n  load.finally(() => {\n    if (loadingChunks.has(load)) {\n      loadingChunks.delete(load)\n      if (loadingChunks.size === 0) {\n        // We are not currently loading any chunks. We can notify all listeners\n        for (let i = 0; i < chunkListeners.length; i++) {\n          chunkListeners[i]()\n        }\n        chunkListeners.length = 0\n      }\n    }\n  })\n}\n\nexport async function warmFlightResponse(\n  flightStream: ReadableStream<Uint8Array>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n) {\n  const { createFromReadableStream } =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client.edge') as typeof import('react-server-dom-webpack/client.edge')\n\n  try {\n    createFromReadableStream(flightStream, {\n      serverConsumerManifest: {\n        moduleLoading: clientReferenceManifest.moduleLoading,\n        moduleMap: clientReferenceManifest.ssrModuleMapping,\n        serverModuleMap: null,\n      },\n    })\n  } catch {\n    // We don't want to handle errors here but we don't want it to\n    // interrupt the outer flow. We simply ignore it here and expect\n    // it will bubble up during a render\n  }\n\n  // We'll wait at least one task and then if no chunks have started to load\n  // we'll we can infer that there are none to load from this flight response\n  trackChunkLoading(waitAtLeastOneReactRenderTask())\n  return new Promise((r) => {\n    chunkListeners.push(r)\n  })\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<React.ReactNode | undefined> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n\n  return globalErrorStyles\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: null,\n  }\n\n  // When dynamicIO is enabled, missing data is encoded to an infinitely hanging\n  // promise, the absence of which we use to determine if a segment is fully\n  // static or partially static. However, when dynamicIO is not enabled, this\n  // trick doesn't work.\n  //\n  // So if PPR is enabled, and dynamicIO is not, we have to be conservative and\n  // assume all segments are partial.\n  //\n  // TODO: When PPR is on, we can at least optimize the case where the entire\n  // page is static. Either by passing that as an argument to this function, or\n  // by setting a header on the response like the we do for full page RSC\n  // prefetches today. The latter approach might be simpler since it requires\n  // less plumbing, and the client has to check the header regardless to see if\n  // PPR is enabled.\n  const shouldAssumePartialData =\n    renderOpts.experimental.isRoutePPREnabled === true && // PPR is enabled\n    !renderOpts.experimental.dynamicIO // dynamicIO is disabled\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    shouldAssumePartialData,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest,\n    fallbackRouteParams\n  )\n}\n"], "names": ["workAsyncStorage", "React", "RenderResult", "chainStreams", "renderToInitialFizzStream", "createDocumentClosingStream", "continueFizzStream", "continueDynamicPrerender", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "streamToBuffer", "streamToString", "stripInternalQueries", "NEXT_HMR_REFRESH_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_URL", "RSC_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "createTrackedMetadataContext", "createMetadataContext", "createRequestStoreForRender", "createWorkStore", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "getURLFromRedirectError", "getRedirectStatusCodeFromError", "isRedirectError", "getImplicitTags", "AppRenderSpan", "NextNodeServerSpan", "getTracer", "FlightRenderResult", "createFlightReactServerErrorHandler", "createHTMLReactServerErrorHandler", "createHTMLErrorHandler", "isUserLandError", "getDigestForWellKnownError", "getShortDynamicParamType", "dynamicParamTypes", "getSegmentParam", "getScriptNonceFromHeader", "parseAndValidateFlightRouterState", "createFlightRouterStateFromLoaderTree", "handleAction", "isBailoutToCSRError", "warn", "error", "appendMutableCookies", "createServerInsertedHTML", "getRequiredScripts", "addPathPrefix", "makeGetServerInsertedHTML", "walkTreeWithFlightRouterState", "createComponentTree", "getRootParams", "getAssetQueryString", "setReferenceManifestsSingleton", "DynamicState", "parsePostponedState", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "getPostponedFromState", "isDynamicServerError", "useFlightStream", "createInlinedDataReadableStream", "StaticGenBailoutError", "isStaticGenBailoutError", "getStackWithoutErrorMessage", "accessedDynamicData", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "isPrerenderInterruptedError", "createDynamicTrackingState", "createDynamicValidationState", "getFirstDynamicReason", "trackAllowedDynamicAccess", "throwIfDisallowedDynamic", "consumeDynamicAccess", "getClientComponentLoaderMetrics", "wrapClientComponentLoader", "createServerModuleMap", "isNodeNextRequest", "parseParameter", "parseRelativeUrl", "AppRouter", "getIsPossibleServerAction", "createInitialRouterState", "createMutableActionQueue", "getRevalidateReason", "PAGE_SEGMENT_KEY", "DynamicServerError", "ServerPrerenderStreamResult", "ReactServerResult", "createReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "prerenderAndAbortInSequentialTasks", "prerenderServerWithPhases", "prerenderClientWithPhases", "printDebugThrownValueForProspectiveRender", "scheduleInSequentialTasks", "waitAtLeastOneReactRenderTask", "workUnitAsyncStorage", "CacheSignal", "getTracedMetadata", "InvariantError", "INFINITE_CACHE", "createComponentStylesAndScripts", "parseLoaderTree", "createPrerenderResumeDataCache", "createRenderResumeDataCache", "isError", "isUseCacheTimeoutError", "createServerInsertedMetadata", "getPreviouslyRevalidatedTags", "executeRevalidates", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "toLowerCase", "undefined", "isHmrRefresh", "isRSCRequest", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "isRouteTreePrefetchRequest", "csp", "nonce", "previouslyRevalidatedTags", "previewModeId", "createNotFoundLoaderTree", "loaderTree", "components", "children", "page", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "treeSegment", "split", "slice", "flatMap", "pathSegment", "join", "NonIndex", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "meta", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "tree", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "parsed<PERSON><PERSON><PERSON>", "metadataContext", "pathname", "loaderTreeToFilter", "parentParams", "rscHead", "Fragment", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "dev", "RSCPayload", "run", "process", "env", "NODE_ENV", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "route", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "fetchMetrics", "warmupDevRender", "implicitTags", "rootParams", "prerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "prerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "expire", "stale", "tags", "hmrRefreshHash", "cookies", "rscPayload", "cacheReady", "abort", "devRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "GlobalError", "initialTree", "errorType", "seedData", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "initialHead", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "metadata", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "digest", "data-next-error-stack", "stack", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "ServerInsertedMetadataProvider", "response", "use", "initialState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "HeadManagerContext", "require", "Provider", "appDir", "globalErrorComponentAndStyles", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "ComponentMod", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "globalThis", "__next_require__", "__next_chunk_load__", "args", "loadingChunk", "loadChunk", "trackChunkLoading", "URL", "setIsrStatus", "NEXT_RUNTIME", "originalRequest", "on", "ended", "metrics", "reset", "startSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "appUsingSizeAdjust", "serverModuleMap", "patchFetch", "taintObjectReference", "crypto", "randomUUID", "nanoid", "isPossibleActionRequest", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "isDebugDynamicAccesses", "access", "invalidUsageError", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "forceStatic", "collectedRevalidate", "cacheControl", "collectedExpire", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "stream", "renderResumeDataCache", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "renderToHTMLOrFlight", "routeModule", "definition", "renderServerInsertedHTML", "getServerInsertedMetadata", "tracingMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "buildManifest", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "integrity", "subresourceIntegrityManifest", "crossOrigin", "noModule", "bootstrapScript", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "nextExport", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "prerenderPhase", "environmentName", "filterStackFrame", "_functionName", "startsWith", "DATA", "inlinedReactServerDataStream", "tee", "resume", "htmlStream", "getServerInsertedHTML", "serverCapturedErrors", "basePath", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactMaxHeadersLength", "bootstrapScripts", "generateStaticHTML", "supportsDynamicResponse", "shouldWaitOnAllReady", "validateRootLayout", "shouldBailoutToCSR", "reason", "redirectUrl", "Headers", "mutableCookies", "from", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "Promise", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "initialServerPrerenderStore", "initialClientController", "initialClientPrerenderStore", "firstAttemptRSCPayload", "initialServerStream", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "warmupStream", "renderStream", "warmFlightResponse", "prerender", "pendingInitialClientResult", "catch", "finalServerController", "serverDynamicTracking", "finalServerPrerenderStore", "finalClientController", "clientDynamicTracking", "dynamicValidation", "finalClientPrerenderStore", "finalServerPayload", "serverPrerenderStreamResult", "rootDidError", "serverPhasedStream", "asPhasedStream", "errorInfo", "dynamicErrors", "push", "componentStack", "assertExhausted", "LogDynamicValidation", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "selectStaleTime", "staleTimes", "static", "initialServerPayload", "pendingInitialServerResult", "onPostpone", "initialServerResult", "asStream", "asUnclosingStream", "serverIsDynamic", "finalRenderPrerenderStore", "finalAttemptRSCPayload", "prerenderIsPending", "prerenderResult", "clientIsDynamic", "prelude", "segmentData", "collectSegmentData", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "consumeAsStream", "cache", "incrementalCache", "dynamicReason", "reactServerPrerenderStore", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "flightStream", "loadingChunks", "chunkListeners", "load", "add", "delete", "createFromReadableStream", "serverConsumerManifest", "moduleLoading", "moduleMap", "ssrModuleMapping", "r", "modules", "globalErrorModule", "styles", "filePath", "getComponent", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "edgeRscModuleMapping", "rscModuleMapping", "shouldAssumePartialData", "staleTime"], "mappings": ";AAaA,SACEA,gBAAgB,QAEX,4CAA2C;AAalD,OAAOC,WAAyC,QAAO;AAEvD,OAAOC,kBAGA,mBAAkB;AACzB,SACEC,YAAY,EACZC,yBAAyB,EACzBC,2BAA2B,EAC3BC,kBAAkB,EAClBC,wBAAwB,EACxBC,uBAAuB,EACvBC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,QACT,0CAAyC;AAChD,SAASC,oBAAoB,QAAQ,oBAAmB;AACxD,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,QAAQ,EACRC,UAAU,EACVC,mCAAmC,EACnCC,4BAA4B,QACvB,6CAA4C;AACnD,SACEC,4BAA4B,EAC5BC,qBAAqB,QAChB,sCAAqC;AAC5C,SAASC,2BAA2B,QAAQ,iCAAgC;AAC5E,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SACEC,kCAAkC,EAClCC,2BAA2B,EAC3BC,yBAAyB,QACpB,oEAAmE;AAC1E,SACEC,uBAAuB,EACvBC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,eAAe,QAA2B,uBAAsB;AACzE,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,yBAAwB;AAC1E,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,mCAAmC,EACnCC,iCAAiC,EACjCC,sBAAsB,EAEtBC,eAAe,EACfC,0BAA0B,QACrB,yBAAwB;AAC/B,SACEC,wBAAwB,EACxBC,iBAAiB,QACZ,iCAAgC;AACvC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qCAAqC,QAAQ,gDAA+C;AACrG,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,IAAI,EAAEC,KAAK,QAAQ,yBAAwB;AACpD,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,6BAA6B,QAAQ,uCAAsC;AACpF,SAASC,mBAAmB,EAAEC,aAAa,QAAQ,0BAAyB;AAC5E,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,8BAA8B,QAAQ,qBAAoB;AACnE,SACEC,YAAY,EAEZC,mBAAmB,QACd,oBAAmB;AAC1B,SACEC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,qBAAqB,QAChB,oBAAmB;AAC1B,SAASC,oBAAoB,QAAQ,+CAA8C;AACnF,SACEC,eAAe,EACfC,+BAA+B,QAC1B,wBAAuB;AAC9B,SACEC,qBAAqB,EACrBC,uBAAuB,QAClB,oDAAmD;AAC1D,SAASC,2BAA2B,QAAQ,gCAA+B;AAC3E,SACEC,mBAAmB,EACnBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QAEf,sBAAqB;AAC5B,SACEC,+BAA+B,EAC/BC,yBAAyB,QACpB,sCAAqC;AAC5C,SAASC,qBAAqB,QAAQ,iBAAgB;AACtD,SAASC,iBAAiB,QAAQ,uBAAsB;AACxD,SAASC,cAAc,QAAQ,4CAA2C;AAC1E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,eAAe,qCAAoC;AAG1D,SAASC,yBAAyB,QAAQ,oCAAmC;AAC7E,SAASC,wBAAwB,QAAQ,qEAAoE;AAC7G,SAASC,wBAAwB,QAAQ,8CAA6C;AACtF,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA0B;AAE3D,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,2BAA2B,QAAQ,+BAA8B;AAC1E,SAEEC,iBAAiB,EACjBC,gCAAgC,EAChCC,0CAA0C,EAC1CC,kCAAkC,EAClCC,yBAAyB,EACzBC,yBAAyB,QACpB,+BAA8B;AACrC,SAASC,yCAAyC,QAAQ,6BAA4B;AACtF,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,SACEC,oBAAoB,QAEf,qCAAoC;AAC3C,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,OAAO,kCAAiC;AACxC,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SACEC,8BAA8B,EAC9BC,2BAA2B,QACtB,yCAAwC;AAE/C,OAAOC,aAAa,qBAAoB;AACxC,SAASC,sBAAsB,QAAQ,gCAA+B;AACtE,SAASC,4BAA4B,QAAQ,uDAAsD;AACnG,SAASC,4BAA4B,QAAQ,kBAAiB;AAC9D,SAASC,kBAAkB,QAAQ,wBAAuB;AAqD1D,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAmBhE,SAASE,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAAC9G,4BAA4BmH,WAAW,GAAG,KAAKC;IAEzD,MAAMC,eACJP,OAAO,CAAC/G,wBAAwBoH,WAAW,GAAG,KAAKC;IAErD,2DAA2D;IAC3D,MAAME,eACJN,sBAAsBF,OAAO,CAAC1G,WAAW+G,WAAW,GAAG,KAAKC;IAE9D,MAAMG,iCACJD,gBAAiB,CAAA,CAACJ,qBAAqB,CAACH,QAAQS,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBxF,kCACE+E,OAAO,CAAC7G,8BAA8BkH,WAAW,GAAG,IAEtDC;IAEJ,sEAAsE;IACtE,MAAMM,6BACJZ,OAAO,CAACzG,oCAAoC8G,WAAW,GAAG,KAAK;IAEjE,MAAMQ,MACJb,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMc,QACJ,OAAOD,QAAQ,WAAW7F,yBAAyB6F,OAAOP;IAE5D,MAAMS,4BAA4BtB,6BAChCO,SACAC,QAAQe,aAAa;IAGvB,OAAO;QACLL;QACAP;QACAQ;QACAL;QACAC;QACAN;QACAY;QACAC;IACF;AACF;AAEA,SAASE,yBAAyBC,UAAsB;IACtD,mEAAmE;IACnE,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,OAAO;QACL;QACA;YACEE,UAAU;gBACRnD;gBACA,CAAC;gBACD;oBACEoD,MAAMF,UAAU,CAAC,YAAY;gBAC/B;aACD;QACH;QACAA;KACD;AACH;AAEA;;CAEC,GACD,SAASG,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAe7G,gBAAgB4G;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAMC,MAAMD,aAAaE,KAAK;QAE9B,IAAIC,QAAQR,MAAM,CAACM,IAAI;QAEvB,IAAIJ,uBAAuBA,oBAAoBO,GAAG,CAACJ,aAAaE,KAAK,GAAG;YACtEC,QAAQN,oBAAoBQ,GAAG,CAACL,aAAaE,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaX,aAAaY,IAAI,KAAK;YACzC,MAAMC,qBAAqBb,aAAaY,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmB5H,iBAAiB,CAAC8G,aAAaY,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNC,aAAa;4BAACd;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQP,SACLoB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMjB,QAAQpE,eAAeqF;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAOxB,MAAM,CAACO,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCC,aAAa;wBAACd;wBAAKE,MAAMiB,IAAI,CAAC;wBAAMN;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAO3H,yBAAyB+G,aAAaY,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDY,aAAa;gBAACd;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMiB,IAAI,CAAC,OAAOjB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASS,SAAS,EAChBzB,QAAQ,EACR0B,UAAU,EACVC,sBAAsB,EAKvB;IACC,MAAMC,YAAY5B,aAAa;IAC/B,MAAM6B,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,qBAAO,KAACC;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBzD,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAI0D,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAM3C,UAAU,EAChB4C,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDtC,0BAA0B,EAC1BuC,sBAAsB,EACtBC,KAAK,EACLrE,SAAS,EACTc,iBAAiB,EACjBwD,SAAS,EACTC,GAAG,EACJ,GAAGV;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAACpE,2BAAAA,QAASsE,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EACJC,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;YAC3BD,MAAM3C;YACN4D,aAAaZ;YACba,iBAAiBtL,6BACf2K,IAAIY,QAAQ,EACZtB,IAAIY,UAAU,EACdH;YAEFzC;YACAuC;YACAE;YACAJ;YACAC;YACAK;QACF;QAEAV,aAAa,AACX,CAAA,MAAM/H,8BAA8B;YAClC8H;YACAuB,oBAAoB/D;YACpBgE,cAAc,CAAC;YACfvE;YACA,+CAA+C;YAC/CwE,uBACE,MAAC9M,MAAM+M,QAAQ;;kCAEb,KAACnC;wBACCzB,UAAUkC,IAAIlC,QAAQ;wBACtB0B,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;wBAC9BC,wBAAwBO,IAAIP,sBAAsB;;kCAGpD,KAACsB,kBAAkB7E,qBAAqBC;kCAExC,KAAC6E,kBAAkB5E,qBAAqBD;;eAVrBF;YAavB2F,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBf;YACAC;YACAJ;YACAK;QACF,EAAC,EACDzC,GAAG,CAAC,CAACuD,OAASA,KAAK9C,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAI5C,2BAAAA,QAAS2F,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG5F,QAAQ2F,YAAY;YACvBE,GAAGnC;YACHoC,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC5BH,GAAGnC;QACHuC,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP1C,GAAqB,EACrB2C,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAW7C,IAAIlC,QAAQ;QACvB,yEAAyE;QACzEgF,WAAW9C,IAAIP,sBAAsB,GAAG,WAAW;QACnDkD;QACAI,kBAAkBzI,oBAAoB0F,IAAIS,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAeuC,kCACbC,GAAoB,EACpBjD,GAAqB,EACrBkD,YAA0B,EAC1B3G,OAMC;IAED,MAAMqE,aAAaZ,IAAIY,UAAU;IAEjC,SAASuC,wBAAwBC,GAAkB;QACjD,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMsD,UAAUxM,oCACd,CAAC,CAAC8J,WAAW2C,GAAG,EAChBJ;IAGF,MAAMK,aAGF,MAAMrI,qBAAqBsI,GAAG,CAChCP,cACAnD,2BACAC,KACAzD;IAGF,IACE,qDAAqD;IACrDqE,WAAW2C,GAAG,IACd,uEAAuE;IACvEG,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzEhD,WAAWiD,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CT,WAAWU,WAAW,GAAGF;QAEzBG,4BACEJ,mBACA/D,IAAIE,YAAY,CAACC,IAAI,EACrBH,KACA,OACAA,IAAIoE,uBAAuB,EAC3BpE,IAAIS,SAAS,CAAC4D,KAAK,EACnBnB;IAEJ;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMoB,uBAAuBnJ,qBAAqBsI,GAAG,CACnDP,cACAlD,IAAIE,YAAY,CAACqE,sBAAsB,EACvCf,YACAxD,IAAIoE,uBAAuB,CAACI,aAAa,EACzC;QACElB;QACAmB,mBAAmB,EAAElI,2BAAAA,QAASkI,mBAAmB;IACnD;IAGF,OAAO,IAAI5N,mBAAmByN,sBAAsB;QAClDI,cAAc1E,IAAIS,SAAS,CAACiE,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACb1B,GAAoB,EACpBjD,GAAqB;IAErB,MAAM,EACJoE,uBAAuB,EACvBlE,YAAY,EACZlC,0BAA0B,EAC1B4G,YAAY,EACZhE,UAAU,EACVH,SAAS,EACV,GAAGT;IAEJ,IAAI,CAACY,WAAW2C,GAAG,EAAE;QACnB,MAAM,qBAEL,CAFK,IAAIjI,eACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMuJ,aAAazM,cACjB8H,aAAaC,IAAI,EACjBnC;IAGF,SAASmF,wBAAwBC,GAAkB;QACjD,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMsD,UAAUxM,oCACd,MACAqM;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAM2B,2BAA2BpJ;IAEjC,MAAMqJ,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAI9J;IAExB,MAAM+J,iBAAiC;QACrCrG,MAAM;QACNsG,OAAO;QACPP;QACAD;QACAS,cAAcN,iBAAiBO,MAAM;QACrCC,YAAYN;QACZC;QACAM,iBAAiB;QACjBC,YAAYlK;QACZmK,QAAQnK;QACRoK,OAAOpK;QACPqK,MAAM,EAAE;QACRd;QACAe,gBAAgB5C,IAAI6C,OAAO,CAAChQ,6BAA6B;IAC3D;IAEA,MAAMiQ,aAAa,MAAM5K,qBAAqBsI,GAAG,CAC/C0B,gBACApF,2BACAC;IAGF,0FAA0F;IAC1F,mCAAmC;IACnC7E,qBAAqBsI,GAAG,CACtB0B,gBACAjF,aAAaqE,sBAAsB,EACnCwB,YACA3B,wBAAwBI,aAAa,EACrC;QACElB;QACAgC,QAAQP,iBAAiBO,MAAM;IACjC;IAGF,6CAA6C;IAC7C,MAAMJ,YAAYc,UAAU;IAC5B,uFAAuF;IACvFb,eAAeL,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBC,iBAAiBkB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAIpP,mBAAmB,IAAI;QAChC6N,cAAcjE,UAAUiE,YAAY;QACpCwB,0BAA0BvK,4BACxBmJ;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASqB,2BAA2BzF,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIY,QAAQ,GAAGZ,IAAI0F,MAAM,AAAD,EAAGlH,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAemH,cACblG,IAAgB,EAChBH,GAAqB,EACrBsG,KAAc;IAEd,MAAM1E,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAI0E;IAEJ,sDAAsD;IACtD,IAAI7C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C2C,eAAe,IAAI1E;IACrB;IAEA,MAAM,EACJ7D,0BAA0B,EAC1BwC,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZsG,WAAW,EACXpG,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHD,SAAS,EACV,GAAGT;IAEJ,MAAMyG,cAAcjP,sCAClB2I,MACAnC,4BACAwC;IAEF,MAAMG,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IAEtE,MAAM,EACJI,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGf,yBAAyB;QAC3BD;QACAuG,WAAWJ,QAAQ,cAAc1J;QACjCwE,aAAaZ;QACba,iBAAiBtL,6BACf2K,IAAIY,QAAQ,EACZtB,IAAIY,UAAU,EACdH;QAEFzC;QACAuC;QACAE;QACAJ;QACAC;QACAK;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAM6F,WAAW,MAAMxO,oBAAoB;QACzC6H;QACAxC,YAAY2C;QACZqB,cAAc,CAAC;QACfI;QACAE;QACAC;QACAC,oBAAoB;QACpBf;QACAC;QACAqF;QACAzF;QACA8F,gBAAgB5G,IAAIY,UAAU,CAACiD,YAAY,CAAC+C,cAAc;QAC1DzF;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAM0F,aAAa7G,IAAI2B,GAAG,CAACmF,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACrR;IAExD,MAAMsR,4BACJ,MAACtS,MAAM+M,QAAQ;;0BACb,KAACnC;gBACCzB,UAAUkC,IAAIlC,QAAQ;gBACtB0B,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAEpD,KAACsB,kBAAkB7E,qBAAqB8D,IAAI7D,SAAS;0BAErD,KAAC6E;;OARkB/E;IAYvB,MAAMiL,oBAAoB,MAAMC,qBAAqBhH,MAAMH;IAE3D,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAMoH,wBACJ3G,UAAUgC,kBAAkB,IAC5BzC,IAAIY,UAAU,CAACiD,YAAY,CAAC7G,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FqK,iBAAG,KAACC;YAASxG,kBAAkBA;;QAC/BuB,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC5BgF,GAAGvH,IAAIwH,WAAW;QAClBC,GAAGtB,2BAA2BzF;QAC9B/B,GAAG,CAAC,CAACoI;QACL3E,GAAG;YACD;gBACEqE;gBACAE;gBACAM;gBACAG;aACD;SACF;QACDM,GAAGnB;QACHoB,GAAG;YAACnB;YAAaU;SAAkB;QACnCU,GAAG,OAAO5H,IAAIY,UAAU,CAACiH,SAAS,KAAK;QACvCrF,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS6E,SAAS,EAAExG,gBAAgB,EAAoC;IACtEA,iBAAiBgH,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb7H,IAAgB,EAChBH,GAAqB,EACrBiI,QAAiB,EACjBvB,SAAqD;IAErD,MAAM,EACJ1I,0BAA0B,EAC1BwC,KAAK,EACLD,sBAAsB,EACtBL,cAAc,EACZsG,WAAW,EACXpG,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDI,GAAG,EACHvE,SAAS,EACTsE,SAAS,EACV,GAAGT;IAEJ,MAAMW,yBAAyB,CAAC,CAACX,IAAIY,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEK,YAAY,EAAED,YAAY,EAAE,GAAGX,yBAAyB;QAC9DD;QACAiB,aAAaZ;QACb,yEAAyE;QACzE,iCAAiC;QACjCa,iBAAiBrL,sBAAsB0K,IAAIY,QAAQ,EAAEtB,IAAIY,UAAU;QACnE8F;QACA1I;QACAuC;QACAE;QACAJ;QACAC;QACAK,wBAAwBA;IAC1B;IAEA,iFAAiF;IACjF,MAAMuH,yBAAW,KAAClH,kBAAkB5E,qBAAqBD;IAEzD,MAAM8K,4BACJ,MAACtS,MAAM+M,QAAQ;;0BACb,KAACnC;gBACCzB,UAAUkC,IAAIlC,QAAQ;gBACtB0B,YAAYQ,IAAI2B,GAAG,CAACnC,UAAU;gBAC9BC,wBAAwBO,IAAIP,sBAAsB;;0BAGpD,KAACsB,kBAAkB7E,qBAAqBC;YACvCuH,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,KAAChE;gBAAKC,MAAK;gBAAaC,SAAQ;;YAEjCoI;;OAXkBjM;IAevB,MAAMwK,cAAcjP,sCAClB2I,MACAnC,4BACAwC;IAGF,IAAI4C,MAAyBxG;IAC7B,IAAIqL,UAAU;QACZ7E,MAAMxH,QAAQqM,YAAYA,WAAW,qBAAwB,CAAxB,IAAIE,MAAMF,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMtB,WAA8B;QAClCF,WAAW,CAAC,EAAE;sBACd,MAAC2B;YAAKC,IAAG;;8BACP,KAACC;8BAAMJ;;8BACP,KAACK;8BACE7E,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBR,oBACxC,KAACoF;wBACCC,2BAAyBrF,IAAIsF,OAAO;wBACpCC,0BAAwB,YAAYvF,MAAMA,IAAIwF,MAAM,GAAG;wBACvDC,yBAAuBzF,IAAI0F,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM5B,oBAAoB,MAAMC,qBAAqBhH,MAAMH;IAE3D,MAAMoH,wBACJ3G,UAAUgC,kBAAkB,IAC5BzC,IAAIY,UAAU,CAACiD,YAAY,CAAC7G,iBAAiB,KAAK;IAEpD,OAAO;QACLqF,GAAGrC,IAAIsC,aAAa,CAACC,OAAO;QAC5BgF,GAAGvH,IAAIwH,WAAW;QAClBC,GAAGtB,2BAA2BzF;QAC9BgH,GAAG9K;QACH+B,GAAG;QACHyD,GAAG;YACD;gBACEqE;gBACAE;gBACAM;gBACAG;aACD;SACF;QACDO,GAAG;YAACnB;YAAaU;SAAkB;QACnCU,GAAG,OAAO5H,IAAIY,UAAU,CAACiH,SAAS,KAAK;QACvCrF,GAAG/B,UAAUgC,kBAAkB;IACjC;AACF;AAEA,mFAAmF;AACnF,SAASsG,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACd7E,uBAAuB,EACvBhH,KAAK,EACL8L,0BAA0B,EAC1BC,8BAA8B,EAQ/B;IACCF;IACA,MAAMG,WAAWzU,MAAM0U,GAAG,CACxBxQ,gBACEmQ,mBACA5E,yBACAhH;IAIJ,MAAMkM,eAAelP,yBAAyB;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBmP,aAAa,CAAC;QACdC,mBAAmBJ,SAAShH,CAAC;QAC7BqH,0BAA0BL,SAAS3B,CAAC;QACpCiC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV7C,oBAAoBqC,SAASzK,CAAC;QAC9BkJ,WAAWuB,SAASxB,CAAC;QACrBiC,aAAaT,SAAS5G,CAAC;IACzB;IAEA,MAAMsH,cAAczP,yBAAyBiP,cAAc;IAE3D,MAAM,EAAES,kBAAkB,EAAE,GAC1BC,QAAQ;IAEV,qBACE,KAACD,mBAAmBE,QAAQ;QAC1B5L,OAAO;YACL6L,QAAQ;YACR9M;QACF;kBAEA,cAAA,KAAC+L;sBACC,cAAA,KAACD;0BACC,cAAA,KAAChP;oBACC4P,aAAaA;oBACbK,+BAA+Bf,SAASzB,CAAC;oBACzCH,aAAa4B,SAAS7B,CAAC;;;;;AAMnC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAAS6C,SAAY,EACnBpB,iBAAiB,EACjBC,cAAc,EACd7E,uBAAuB,EACvB+E,8BAA8B,EAC9BD,0BAA0B,EAC1B9L,KAAK,EAQN;IACC6L;IACA,MAAMG,WAAWzU,MAAM0U,GAAG,CACxBxQ,gBACEmQ,mBACA5E,yBACAhH;IAIJ,MAAMkM,eAAelP,yBAAyB;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBmP,aAAa,CAAC;QACdC,mBAAmBJ,SAAShH,CAAC;QAC7BqH,0BAA0BL,SAAS3B,CAAC;QACpCiC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV7C,oBAAoBqC,SAASzK,CAAC;QAC9BkJ,WAAWuB,SAASxB,CAAC;QACrBiC,aAAaT,SAAS5G,CAAC;IACzB;IAEA,MAAMsH,cAAczP,yBAAyBiP,cAAc;IAE3D,qBACE,KAACH;kBACC,cAAA,KAACD;sBACC,cAAA,KAAChP;gBACC4P,aAAaA;gBACbK,+BAA+Bf,SAASzB,CAAC;gBACzCH,aAAa4B,SAAS7B,CAAC;;;;AAKjC;AASA,eAAe8C,yBACbpH,GAAoB,EACpBtB,GAAqB,EACrBjB,GAAwC,EACxC5C,QAAgB,EAChB0C,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpB6J,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrCC,wBAA8D,EAC9DnI,aAA+B;IAE/B,MAAMoI,iBAAiB5M,aAAa;IACpC,IAAI4M,gBAAgB;QAClB/I,IAAInC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAMmL,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbzD,cAAc,EAAE,EAChB0D,cAAc,EACf,GAAGtK;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAImK,aAAaI,YAAY,EAAE;QAC7B,MAAMC,eAAevR,0BAA0BkR;QAC/C,aAAa;QACbM,WAAWC,gBAAgB,GAAGF,aAAapB,OAAO;QAClD,kEAAkE;QAClE,qEAAqE;QACrE,wEAAwE;QACxE,oEAAoE;QACpE,MAAMuB,sBAAqD,CAAC,GAAGC;YAC7D,MAAMC,eAAeL,aAAaM,SAAS,IAAIF;YAC/CG,kBAAkBF;YAClB,OAAOA;QACT;QACA,mBAAmB;QACnBJ,WAAWE,mBAAmB,GAAGA;IACnC;IAEA,IAAI7H,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAEtC,QAAQ,EAAE,GAAG,IAAIsK,IAAI3I,IAAIvC,GAAG,IAAI,KAAK;QAC7CE,WAAWiL,YAAY,oBAAvBjL,WAAWiL,YAAY,MAAvBjL,YAA0BU,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DoC,QAAQC,GAAG,CAACmI,YAAY,KAAK,UAC7B/R,kBAAkBkJ,MAClB;QACAA,IAAI8I,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5BzB,kBAAkB0B,KAAK,GAAG;YAE1B,IAAI,iBAAiBZ,YAAY;gBAC/B,MAAMa,UAAUtS,gCAAgC;oBAAEuS,OAAO;gBAAK;gBAC9D,IAAID,SAAS;oBACXtV,YACGwV,SAAS,CAACzV,mBAAmB0V,sBAAsB,EAAE;wBACpDC,WAAWJ,QAAQK,wBAAwB;wBAC3CC,YAAY;4BACV,iCACEN,QAAQO,wBAAwB;4BAClC,kBAAkB9V,mBAAmB0V,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFR,QAAQK,wBAAwB,GAC9BL,QAAQS,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMzE,WAAwC,CAAC;IAE/C,MAAM3H,yBAAyB,CAAC,EAACyK,oCAAAA,iBAAkB4B,kBAAkB;IAErE,4BAA4B;IAC5B,MAAMxI,0BAA0BxD,WAAWwD,uBAAuB;IAElE,MAAMyI,kBAAkB/S,sBAAsB;QAAEgR;IAAsB;IAEtExS,+BAA+B;QAC7BqF,MAAM8C,UAAU9C,IAAI;QACpByG;QACA0G;QACA+B;IACF;IAEA9B,aAAa+B,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAE3M,MAAM3C,UAAU,EAAEuP,oBAAoB,EAAE,GAAGhC;IAEnD,IAAIG,gBAAgB;QAClB6B,qBACE,kFACArJ,QAAQC,GAAG;IAEf;IAEAlD,UAAUiE,YAAY,GAAG,EAAE;IAC3BwD,SAASxD,YAAY,GAAGjE,UAAUiE,YAAY;IAE9C,qCAAqC;IACrClE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBlL,qBAAqBkL;IAErB,MAAM,EACJvD,iBAAiB,EACjBP,iBAAiB,EACjBI,YAAY,EACZN,kBAAkB,EAClBK,YAAY,EACZO,KAAK,EACN,GAAGkN;IAEJ;;;GAGC,GACD,IAAInO;IAEJ,IAAIuH,QAAQC,GAAG,CAACmI,YAAY,KAAK,QAAQ;QACvC3P,YAAY6Q,OAAOC,UAAU;IAC/B,OAAO;QACL9Q,YAAY6N,QAAQ,6BAA6BkD,MAAM;IACzD;IAEA;;GAEC,GACD,MAAMrP,SAAS+C,WAAW/C,MAAM,IAAI,CAAC;IAErC,MAAM,EAAE4E,kBAAkB,EAAE1E,mBAAmB,EAAE,GAAG0C;IAEpD,MAAMzC,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMoP,0BAA0BhT,0BAA0B8I;IAE1D,MAAM2B,eAAe,MAAMnO,gBACzBgK,UAAU9C,IAAI,EACd+C,KACA3C;IAGF,MAAMiC,MAAwB;QAC5BE,cAAc6K;QACdrK;QACAE;QACAH;QACA6J;QACAtM;QACAwC;QACA4M,YAAY1Q;QACZ+C,wBAAwB0N;QACxBxC;QACApK;QACAtD;QACAd;QACA2B;QACAsG;QACAoD;QACAkD;QACAtN;QACAuE;QACAW;QACAsC;IACF;IAEAhO,YAAYyW,oBAAoB,CAAC,cAAcvP;IAE/C,IAAI2E,oBAAoB;YAyGlByF;QAxGJ,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMoF,+BAA+B1W,YAAY2W,IAAI,CACnD7W,cAAc8W,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAE3P,UAAU;YAC7C0O,YAAY;gBACV,cAAc1O;YAChB;QACF,GACA4P;QAGF,MAAMtE,WAAW,MAAMkE,6BACrBrK,KACAtB,KACA3B,KACAkI,UACAzH,WACAjD;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACE4L,SAASuE,aAAa,IACtBzU,oBAAoBkQ,SAASuE,aAAa,KAC1C/M,WAAWgN,sBAAsB,EACjC;YACAjW,KAAK;YACL,KAAK,MAAMkW,UAAUzU,yBAAyBgQ,SAASuE,aAAa,EAAG;gBACrEhW,KAAKkW;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAIpN,UAAUqN,iBAAiB,EAAE;YAC/B,MAAMrN,UAAUqN,iBAAiB;QACnC;QACA,IAAI1E,SAAS2E,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoB7E,SAAS2E,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAG9P,KAAK;YACxE,IAAI4P,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAI7E,SAASgF,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoB7E,SAASgF,SAAS,CAACE,IAAI,CAAC,CAAClL,MACjDnM,gBAAgBmM;YAElB,IAAI6K,mBAAmB,MAAMA;QAC/B;QAEA,MAAM1R,UAA+B;YACnC2L;QACF;QACA,oEAAoE;QACpE,IACEzH,UAAU8N,kBAAkB,IAC5B9N,UAAU+N,uBAAuB,IACjC/N,UAAUgO,sBAAsB,EAChC;YACA,MAAMC,iBAAiB1S,mBAAmByE,WAAWkO,OAAO,CAAC;gBAC3D,IAAIjL,QAAQC,GAAG,CAACiL,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CpO;gBAC3D;YACF;YAEA,IAAIE,WAAWmO,SAAS,EAAE;gBACxBnO,WAAWmO,SAAS,CAACL;YACvB,OAAO;gBACLnS,QAAQwS,SAAS,GAAGL;YACtB;QACF;QAEA,IAAItF,SAAS4F,aAAa,EAAE;YAC1B9G,SAAS+G,SAAS,GAAG7F,SAAS4F,aAAa,CAAC1P,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAM4P,cAAcC,OAAO/F,SAASgG,cAAc;QAClDzN,IAAI0N,SAAS,CAAC3Z,+BAA+BwZ;QAC7ChH,SAAS5L,OAAO,KAAK,CAAC;QACtB4L,SAAS5L,OAAO,CAAC5G,8BAA8B,GAAGwZ;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAIzO,UAAU6O,WAAW,KAAK,SAASlG,SAASmG,mBAAmB,KAAK,GAAG;YACzErH,SAASsH,YAAY,GAAG;gBAAE/J,YAAY;gBAAGC,QAAQ9I;YAAU;QAC7D,OAAO;YACL,gEAAgE;YAChEsL,SAASsH,YAAY,GAAG;gBACtB/J,YACE2D,SAASmG,mBAAmB,IAAIhU,iBAC5B,QACA6N,SAASmG,mBAAmB;gBAClC7J,QACE0D,SAASqG,eAAe,IAAIlU,iBACxBqB,YACAwM,SAASqG,eAAe;YAChC;QACF;QAEA,qCAAqC;QACrC,IAAIvH,EAAAA,yBAAAA,SAASsH,YAAY,qBAArBtH,uBAAuBzC,UAAU,MAAK,GAAG;YAC3CyC,SAASwH,iBAAiB,GAAG;gBAC3BC,aAAalP,UAAUmP,uBAAuB;gBAC9C9G,OAAOrI,UAAUoP,iBAAiB;YACpC;QACF;QAEA,OAAO,IAAIjb,aAAa,MAAMS,eAAe+T,SAAS0G,MAAM,GAAGvT;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMwT,wBACJnP,WAAWsF,wBAAwB,KACnCsE,kCAAAA,eAAgBuF,qBAAqB;QAEvC,MAAMlL,aAAazM,cAAcoF,YAAYwC,IAAIhC,0BAA0B;QAC3E,MAAMkF,eAAejN,4BACnBgN,KACAtB,KACAjB,KACAmE,YACAD,cACAhE,WAAWoP,eAAe,EAC1BpP,WAAWqP,YAAY,EACvBpT,cACA4N,0BACAsF;QAGF,IACErM,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBhD,WAAWiL,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7DnI,QAAQC,GAAG,CAACmI,YAAY,KAAK,UAC7B/R,kBAAkBkJ,QAClB,CAACzG,oBACD;YACA,MAAMqP,eAAejL,WAAWiL,YAAY;YAC5C5I,IAAI8I,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAAC9I,aAAagN,WAAW,IAAI,CAACzP,UAAU0P,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAE7O,QAAQ,EAAE,GAAG,IAAIsK,IAAI3I,IAAIvC,GAAG,IAAI,KAAK;oBAC7CmL,aAAavK,UAAU;gBACzB;YACF;QACF;QAEA,IAAI9E,oBAAoB;YACtB,OAAOmI,gBAAgB1B,KAAKjD;QAC9B,OAAO,IAAIlD,cAAc;YACvB,OAAOkG,kCAAkCC,KAAKjD,KAAKkD;QACrD;QAEA,MAAMkN,4BAA4BxZ,YAAY2W,IAAI,CAChD7W,cAAc8W,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAE3P,UAAU;YAC1C0O,YAAY;gBACV,cAAc1O;YAChB;QACF,GACAuS;QAGF,IAAIC,YAAwB;QAC5B,IAAInD,yBAAyB;YAC3B,gFAAgF;YAChF,MAAMoD,sBAAsB,MAAM9Y,aAAa;gBAC7CwL;gBACAtB;gBACAoJ;gBACA8B;gBACA2D,gBAAgBxN;gBAChBvC;gBACAyC;gBACA+H;gBACAjL;YACF;YAEA,IAAIuQ,qBAAqB;gBACvB,IAAIA,oBAAoBzR,IAAI,KAAK,aAAa;oBAC5C,MAAM2R,qBAAqBlT,yBAAyBC;oBACpDmE,IAAInC,UAAU,GAAG;oBACjB,MAAMsQ,SAAS,MAAMM,0BACnBlN,cACAD,KACAtB,KACA3B,KACAS,WACAgQ,oBACAH,WACA9F;oBAGF,OAAO,IAAI5V,aAAakb,QAAQ;wBAAE5H;oBAAS;gBAC7C,OAAO,IAAIqI,oBAAoBzR,IAAI,KAAK,QAAQ;oBAC9C,IAAIyR,oBAAoBG,MAAM,EAAE;wBAC9BH,oBAAoBG,MAAM,CAACC,cAAc,CAACzI;wBAC1C,OAAOqI,oBAAoBG,MAAM;oBACnC,OAAO,IAAIH,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAM/T,UAA+B;YACnC2L;QACF;QAEA,MAAM4H,SAAS,MAAMM,0BACnBlN,cACAD,KACAtB,KACA3B,KACAS,WACAjD,YACA8S,WACA9F;QAGF,IAAI/J,UAAUqN,iBAAiB,EAAE;YAC/B,MAAMrN,UAAUqN,iBAAiB;QACnC;QAEA,oEAAoE;QACpE,IACErN,UAAU8N,kBAAkB,IAC5B9N,UAAU+N,uBAAuB,IACjC/N,UAAUgO,sBAAsB,EAChC;YACA,MAAMC,iBAAiB1S,mBAAmByE,WAAWkO,OAAO,CAAC;gBAC3D,IAAIjL,QAAQC,GAAG,CAACiL,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CpO;gBAC3D;YACF;YAEA,IAAIE,WAAWmO,SAAS,EAAE;gBACxBnO,WAAWmO,SAAS,CAACL;YACvB,OAAO;gBACLnS,QAAQwS,SAAS,GAAGL;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAI9Z,aAAakb,QAAQvT;IAClC;AACF;AAcA,OAAO,MAAMqU,uBAAsC,CACjD3N,KACAtB,KACA7D,UACA0C,OACAzC,qBACA6C,YACA6J,0BACAhO,aACA6F;QAaiB1B;IAXjB,IAAI,CAACqC,IAAIvC,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAIyH,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAMzH,MAAMzG,iBAAiBgJ,IAAIvC,GAAG,EAAE9D,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAM0N,uBAAuBjO,oBAAoB4G,IAAI3G,OAAO,EAAE;QAC5DG;QACAO,mBAAmB4D,WAAWiD,YAAY,CAAC7G,iBAAiB,KAAK;QACjEM,aAAa,GAAEsD,2BAAAA,WAAWqP,YAAY,qBAAvBrP,yBAAyBtD,aAAa;IACvD;IAEA,MAAM,EAAEZ,iBAAiB,EAAEW,yBAAyB,EAAE,GAAGiN;IAEzD,MAAMC,oBAAoB;QAAE0B,OAAO;IAAM;IACzC,IAAIzB,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAO5J,WAAWiH,SAAS,KAAK,UAAU;QAC5C,IAAI9J,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIzC,eACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAkP,iBAAiBhS,oBACfoI,WAAWiH,SAAS,EACpBjH,WAAW/C,MAAM;IAErB;IAEA,IACE2M,CAAAA,kCAAAA,eAAgBuF,qBAAqB,KACrCnP,WAAWsF,wBAAwB,EACnC;QACA,MAAM,qBAEL,CAFK,IAAI5K,eACR,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMmF,YAAYvK,gBAAgB;QAChCyH,MAAMiD,WAAWiQ,WAAW,CAACC,UAAU,CAACnT,IAAI;QAC5CI;QACA6C;QACA2J;QACA,8CAA8C;QAC9C7N;QACA6F,SAASD,cAAcC,OAAO;QAC9BlF;IACF;IAEA,OAAO3I,iBAAiB+O,GAAG,CACzBhD,WACA,sBAAsB;IACtB4J,0BACA,mBAAmB;IACnBpH,KACAtB,KACAjB,KACA5C,UACA0C,OACAI,YACAH,WACA6J,sBACAC,mBACAC,gBACAC,0BACAnI;AAEJ,EAAC;AAED,eAAe+N,eACbnN,YAA0B,EAC1BD,GAAoB,EACpBtB,GAAqB,EACrB3B,GAAqB,EACrBS,SAAoB,EACpBN,IAAgB,EAChBmQ,SAAc,EACd9F,cAAqC;IAErC,MAAM5J,aAAaZ,IAAIY,UAAU;IACjC,MAAMmK,eAAenK,WAAWmK,YAAY;IAC5C,4BAA4B;IAC5B,MAAM3G,0BAA0BxD,WAAWwD,uBAAuB;IAElE,MAAM,EAAE8E,0BAA0B,EAAE6H,wBAAwB,EAAE,GAC5DjZ;IACF,MAAM,EAAEqR,8BAA8B,EAAE6H,yBAAyB,EAAE,GACjElV,6BAA6BkE,IAAI5C,KAAK;IAExC,MAAM6T,kBAAkB5V,kBACtBzE,YAAYsa,uBAAuB,IACnCtQ,WAAWiD,YAAY,CAACsN,mBAAmB;IAG7C,MAAMC,YACJxQ,WAAWyQ,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElD/S,GAAG,CAAC,CAAC8S;YAKO5Q;eALO;YAClB8Q,KAAK,GAAG1R,IAAIwH,WAAW,CAAC,OAAO,EAAEgK,WAAWnZ,oBAC1C2H,KACA,QACC;YACH2R,SAAS,GAAE/Q,2CAAAA,WAAWgR,4BAA4B,qBAAvChR,wCAAyC,CAAC4Q,SAAS;YAC9DK,aAAajR,WAAWiR,WAAW;YACnCC,UAAU;YACV1U,OAAO4C,IAAI5C,KAAK;QAClB;;IAEJ,MAAM,CAAC6L,gBAAgB8I,gBAAgB,GAAGha,mBACxC6I,WAAWyQ,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9ErR,IAAIwH,WAAW,EACf5G,WAAWiR,WAAW,EACtBjR,WAAWgR,4BAA4B,EACvCvZ,oBAAoB2H,KAAK,OACzBA,IAAI5C,KAAK,EACTwD,WAAWjD,IAAI;IAGjB,MAAMqU,4BAAwD,IAAIrI;IAClE,MAAMsI,gBAAgB;IACtB,SAASC,qBAAqB9O,GAAkB;QAC9C,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMmS,+BAA+Bpb,kCACnC,CAAC,CAAC6J,WAAW2C,GAAG,EAChB,CAAC,CAAC3C,WAAWwR,UAAU,EACvBJ,2BACAC,eACAC;IAGF,SAASG,qBAAqBjP,GAAkB;QAC9C,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IAEA,MAAMsS,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2Bvb,uBAC/B,CAAC,CAAC4J,WAAW2C,GAAG,EAChB,CAAC,CAAC3C,WAAWwR,UAAU,EACvBJ,2BACAM,mBACAL,eACAI;IAGF,IAAIG,oBAA8C;IAElD,MAAMnD,YAAY1N,IAAI0N,SAAS,CAACoD,IAAI,CAAC9Q;IACrC,MAAM+Q,eAAe/Q,IAAI+Q,YAAY,CAACD,IAAI,CAAC9Q;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrDf,WAAW2C,GAAG,IACd,uEAAuE;QACvEG,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACmI,YAAY,KAAK,UAC7B,yEAAyE;QACzElL,WAAWiD,YAAY,CAACC,SAAS,EACjC;YACA,wFAAwF;YACxF,MAAMN,aAGF,MAAMrI,qBAAqBsI,GAAG,CAChCP,cACAmD,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAM,CAACuE,mBAAmBC,iBAAiB,GAAGC;YAC9CT,WAAWU,WAAW,GAAGF;YAEzB,MAAMgF,oBAAoB,MAAM7N,qBAAqBsI,GAAG,CACtDP,cACAjI,2BACA;gBACEiI,aAAayP,cAAc,GAAG;gBAC9B,OAAO5H,aAAaxG,sBAAsB,CACxCf,YACAY,wBAAwBI,aAAa,EACrC;oBACElB,SAAS6O;oBACTS,iBAAiB,IACf1P,aAAayP,cAAc,KAAK,OAAO,cAAc;oBACvDE,kBAAiBnS,GAAW,EAAEoS,aAAqB;wBACjD,kEAAkE;wBAClE,mEAAmE;wBACnE,mEAAmE;wBACnE,OAAO,CAACpS,IAAIqS,UAAU,CAAC,YAAY,CAACrS,IAAIsG,QAAQ,CAAC;oBACnD;gBACF;YAEJ,GACA;gBACE9D,aAAayP,cAAc,GAAG;YAChC;YAGFxO,4BACEJ,mBACA5D,MACAH,KACA2B,IAAInC,UAAU,KAAK,KACnB4E,yBACA3D,UAAU4D,KAAK,EACfnB;YAGFsP,oBAAoB,IAAI9X,kBAAkBsO;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMxF,aAAa,MAAMrI,qBAAqBsI,GAAG,CAC/CP,cACAmD,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAGrBgT,oBAAoB,IAAI9X,kBACtBS,qBAAqBsI,GAAG,CACtBP,cACA6H,aAAaxG,sBAAsB,EACnCf,YACAY,wBAAwBI,aAAa,EACrC;gBACElB,SAAS6O;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMjX;QAEN,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAO0F,WAAWiH,SAAS,KAAK,UAAU;YAC5C,IAAI2C,CAAAA,kCAAAA,eAAgB1L,IAAI,MAAKvG,aAAaya,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+Bna,gCACnC0Z,kBAAkBU,GAAG,IACrBlT,IAAI5C,KAAK,EACTkT;gBAGF,OAAOzb,aACLoe,8BACAle;YAEJ,OAAO,IAAIyV,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM3C,YAAYlP,sBAAsB6R;gBAExC,MAAM2I,SAASnJ,QAAQ,yBACpBmJ,MAAM;gBAET,MAAMC,aAAa,MAAMjY,qBAAqBsI,GAAG,CAC/CP,cACAiQ,sBACA,KAACpK;oBACCC,mBAAmBwJ,kBAAkBU,GAAG;oBACxCjK,gBAAgBA;oBAChB7E,yBAAyBA;oBACzB8E,4BAA4BA;oBAC5BC,gCAAgCA;oBAChC/L,OAAO4C,IAAI5C,KAAK;oBAElByK,WACA;oBACEvE,SAASiP;oBACTnV,OAAO4C,IAAI5C,KAAK;gBAClB;gBAGF,MAAMiW,wBAAwBpb,0BAA0B;oBACtDmZ;oBACAL;oBACAuC,sBAAsBhB;oBACtBiB,UAAU3S,WAAW2S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBACA,OAAO,MAAM9b,0BAA0Bie,YAAY;oBACjDI,mBAAmB1a,gCACjB0Z,kBAAkBiB,OAAO,IACzBzT,IAAI5C,KAAK,EACTkT;oBAEF+C;oBACArC;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMzM,yBAAyByF,QAAQ,yBACpCzF,sBAAsB;QAEzB,MAAM6O,aAAa,MAAMjY,qBAAqBsI,GAAG,CAC/CP,cACAqB,sCACA,KAACwE;YACCC,mBAAmBwJ,kBAAkBU,GAAG;YACxCjK,gBAAgBA;YAChB7E,yBAAyBA;YACzB8E,4BAA4BA;YAC5BC,gCAAgCA;YAChC/L,OAAO4C,IAAI5C,KAAK;YAElB;YACEkG,SAASiP;YACTnV,OAAO4C,IAAI5C,KAAK;YAChBsW,WAAW,CAACpX;gBACVA,QAAQwL,OAAO,CAAC,CAACzJ,OAAOF;oBACtBuU,aAAavU,KAAKE;gBACpB;YACF;YACAsV,kBAAkB/S,WAAWgT,qBAAqB;YAClDC,kBAAkB;gBAAC9B;aAAgB;YACnCzB;QACF;QAGF,MAAM+C,wBAAwBpb,0BAA0B;YACtDmZ;YACAL;YACAuC,sBAAsBhB;YACtBiB,UAAU3S,WAAW2S,QAAQ;YAC7BtC,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAM6C,qBACJlT,WAAWmT,uBAAuB,KAAK,QACvC,CAAC,CAACnT,WAAWoT,oBAAoB;QAEnC,MAAMC,qBAAqBrT,WAAW2C,GAAG;QACzC,OAAO,MAAMvO,mBAAmBoe,YAAY;YAC1CI,mBAAmB1a,gCACjB0Z,kBAAkBiB,OAAO,IACzBzT,IAAI5C,KAAK,EACTkT;YAEF7N,oBAAoBqR;YACpBT;YACArC;YACAiD;QACF;IACF,EAAE,OAAO7Q,KAAK;QACZ,IACEpK,wBAAwBoK,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIsF,OAAO,KAAK,YACvBtF,IAAIsF,OAAO,CAAC1B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAM5D;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM8Q,qBAAqBxc,oBAAoB0L;QAC/C,IAAI8Q,oBAAoB;YACtB,MAAMpL,QAAQ7P,4BAA4BmK;YAC1CxL,MACE,GAAGwL,IAAI+Q,MAAM,CAAC,mDAAmD,EAAEnU,IAAIlC,QAAQ,CAAC,kFAAkF,EAAEgL,OAAO;YAG7K,MAAM1F;QACR;QAEA,IAAIsD;QAEJ,IAAIrQ,0BAA0B+M,MAAM;YAClCzB,IAAInC,UAAU,GAAGpJ,4BAA4BgN;YAC7CsD,YAAYvQ,mCAAmCwL,IAAInC,UAAU;QAC/D,OAAO,IAAIhJ,gBAAgB4M,MAAM;YAC/BsD,YAAY;YACZ/E,IAAInC,UAAU,GAAGjJ,+BAA+B6M;YAEhD,MAAMgR,cAAcpc,cAClB1B,wBAAwB8M,MACxBxC,WAAW2S,QAAQ;YAGrB,gEAAgE;YAChE,YAAY;YACZ,MAAMjX,UAAU,IAAI+X;YACpB,IAAIxc,qBAAqByE,SAAS4G,aAAaoR,cAAc,GAAG;gBAC9DjF,UAAU,cAAc7Q,MAAM+V,IAAI,CAACjY,QAAQ4R,MAAM;YACnD;YAEAmB,UAAU,YAAY+E;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9BvS,IAAInC,UAAU,GAAG;QACnB;QAEA,MAAM,CAACgV,qBAAqBC,qBAAqB,GAAG1c,mBAClD6I,WAAWyQ,aAAa,EACxBrR,IAAIwH,WAAW,EACf5G,WAAWiR,WAAW,EACtBjR,WAAWgR,4BAA4B,EACvCvZ,oBAAoB2H,KAAK,QACzBA,IAAI5C,KAAK,EACT;QAGF,MAAMsX,kBAAkB,MAAMvZ,qBAAqBsI,GAAG,CACpDP,cACA8E,oBACA7H,MACAH,KACAgS,0BAA0B1T,GAAG,CAAC,AAAC8E,IAAYwF,MAAM,IAAI,OAAOxF,KAC5DsD;QAGF,MAAMiO,oBAAoBxZ,qBAAqBsI,GAAG,CAChDP,cACA6H,aAAaxG,sBAAsB,EACnCmQ,iBACAtQ,wBAAwBI,aAAa,EACrC;YACElB,SAAS6O;QACX;QAGF,IAAIK,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAMpP;QACR;QAEA,IAAI;YACF,MAAMwR,aAAa,MAAMzZ,qBAAqBsI,GAAG,CAC/CP,cACApO,2BACA;gBACE+f,gBAAgB7K,QAAQ;gBACxB8K,uBACE,KAAC1K;oBACCpB,mBAAmB2L;oBACnBxL,gCAAgCA;oBAChCD,4BAA4BA;oBAC5BD,gBAAgBuL;oBAChBpQ,yBAAyBA;oBACzBhH,OAAO4C,IAAI5C,KAAK;;gBAGpB2X,eAAe;oBACb3X,OAAO4C,IAAI5C,KAAK;oBAChB,wCAAwC;oBACxCyW,kBAAkB;wBAACY;qBAAqB;oBACxCnE;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAMwD,qBACJlT,WAAWmT,uBAAuB,KAAK,QACvC,CAAC,CAACnT,WAAWoT,oBAAoB;YACnC,MAAMC,qBAAqBrT,WAAW2C,GAAG;YACzC,OAAO,MAAMvO,mBAAmB4f,YAAY;gBAC1CpB,mBAAmB1a,gCACjB,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACT0Z,kBAAkBiB,OAAO,IACzBzT,IAAI5C,KAAK,EACTkT;gBAEF7N,oBAAoBqR;gBACpBT,uBAAuBpb,0BAA0B;oBAC/CmZ;oBACAL;oBACAuC,sBAAsB,EAAE;oBACxBC,UAAU3S,WAAW2S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBACAD;gBACAiD;YACF;QACF,EAAE,OAAOe,UAAe;YACtB,IACEtR,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBvN,0BAA0B2e,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BjL,QAAQ;gBACViL;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAAS/Q;IACP,IAAIF;IACJ,IAAImR,SAAS,IAAIC,QAAyB,CAACC;QACzCrR,oBAAoBqR;IACtB;IACA,OAAO;QAACrR;QAAoBmR;KAAO;AACrC;AAEA,eAAe/Q,4BACbJ,iBAA+D,EAC/D5D,IAAgB,EAChBH,GAAqB,EACrBqV,UAAmB,EACnBjR,uBAA2E,EAC3EC,KAAa,EACbnB,YAA0B;QAQHA;IANvB,MAAM,EAAEhD,cAAc6K,YAAY,EAAEnG,YAAY,EAAE,GAAG5E;IACrD,MAAM6E,aAAazM,cACjB2S,aAAa5K,IAAI,EACjBH,IAAIhC,0BAA0B;IAGhC,MAAM6H,kBAAiB3C,4BAAAA,aAAa4C,OAAO,CAACvH,GAAG,CAC7CzI,kDADqBoN,0BAEpB7E,KAAK;IAER,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMiX,mCAAmC,IAAItQ;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAMuQ,gCAAgC,IAAIvQ;IAE1C,MAAME,cAAc,IAAI9J;IACxB,MAAM0J,2BAA2BpJ;IACjC,MAAM8Z,8BAA8C;QAClD1W,MAAM;QACNsG,OAAO;QACPP;QACAD;QACAS,cAAckQ,8BAA8BjQ,MAAM;QAClDC,YAAY+P;QACZpQ;QACAM,iBAAiB;QACjBC,YAAYlK;QACZmK,QAAQnK;QACRoK,OAAOpK;QACPqK,MAAM,EAAE;QACRd;QACAe;IACF;IAEA,MAAM4P,0BAA0B,IAAIzQ;IACpC,MAAM0Q,8BAA8C;QAClD5W,MAAM;QACNsG,OAAO;QACPP;QACAD;QACAS,cAAcoQ,wBAAwBnQ,MAAM;QAC5CC,YAAYkQ;QACZvQ;QACAM,iBAAiB;QACjBC,YAAYlK;QACZmK,QAAQnK;QACRoK,OAAOpK;QACPqK,MAAM,EAAE;QACRd;QACAe;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAM8P,yBAAyB,MAAMxa,qBAAqBsI,GAAG,CAC3D+R,6BACAnP,eACAlG,MACAH,KACAqV;IAGF,IAAIO;IACJ,IAAI;QACFA,sBAAsBza,qBAAqBsI,GAAG,CAC5C+R,6BACAzK,aAAaxG,sBAAsB,EACnCoR,wBACAvR,wBAAwBI,aAAa,EACrC;YACElB,SAAS,CAACF;gBACR,MAAMwF,SAAS1R,2BAA2BkM;gBAE1C,IAAIwF,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IACE0M,iCAAiChQ,MAAM,CAACuQ,OAAO,IAC/CN,8BAA8BjQ,MAAM,CAACuQ,OAAO,EAC5C;oBACA,mEAAmE;oBACnE,iEAAiE;oBACjE;gBACF,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;oBACA/a,0CAA0CoI,KAAKiB;gBACjD;YACF;YACAiB,QAAQiQ,8BAA8BjQ,MAAM;QAC9C;IAEJ,EAAE,OAAOlC,KAAc;QACrB,IACEkS,iCAAiChQ,MAAM,CAACuQ,OAAO,IAC/CN,8BAA8BjQ,MAAM,CAACuQ,OAAO,EAC5C;QACA,4EAA4E;QAC9E,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnF/a,0CAA0CoI,KAAKiB;QACjD;IACF;IAEA,MAAMjH,QAAQ;IACd,MAAM,EAAE8L,0BAA0B,EAAE,GAAGpR;IACvC,MAAM,EAAEqR,8BAA8B,EAAE,GAAGrN,6BAA6BsB;IAExE,IAAIwY,qBAAqB;QACvB,MAAM,CAACI,cAAcC,aAAa,GAAGL,oBAAoB1C,GAAG;QAC5D0C,sBAAsB;QACtB,gFAAgF;QAChF,sBAAsB;QACtB,MAAMM,mBAAmBF,cAAc5R;QAEvC,MAAM+R,YAAYnM,QAAQ,yBACvBmM,SAAS;QACZ,MAAMC,6BAA6Bjb,qBAAqBsI,GAAG,CACzDiS,6BACAS,yBACA,KAACpN;YACCC,mBAAmBiN;YACnBhN,gBAAgB,KAAO;YACvB7E,yBAAyBA;YACzB8E,4BAA4BA;YAC5BC,gCAAgCA;YAChC/L,OAAOA;YAET;YACEkI,QAAQmQ,wBAAwBnQ,MAAM;YACtChC,SAAS,CAACF;gBACR,MAAMwF,SAAS1R,2BAA2BkM;gBAE1C,IAAIwF,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAI6M,wBAAwBnQ,MAAM,CAACuQ,OAAO,EAAE;gBAC1C,4EAA4E;gBAC9E,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnF/a,0CAA0CoI,KAAKiB;gBACjD;YACF;QACF;QAEF+R,2BAA2BC,KAAK,CAAC,CAACjT;YAChC,IAAIqS,wBAAwBnQ,MAAM,CAACuQ,OAAO,EAAE;YAC1C,2DAA2D;YAC7D,OAAO;gBACL,uEAAuE;gBACvE,yCAAyC;gBACzC,IAAInS,QAAQC,GAAG,CAACoS,sBAAsB,EAAE;oBACtC/a,0CAA0CoI,KAAKiB;gBACjD;YACF;QACF;IACF;IAEA,MAAMa,YAAYc,UAAU;IAC5B,8DAA8D;IAC9D,gEAAgE;IAChEyP,wBAAwBxP,KAAK;IAC7BsP,8BAA8BtP,KAAK;IACnCqP,iCAAiCrP,KAAK;IAEtC,sEAAsE;IACtE,kFAAkF;IAElF,MAAMqQ,wBAAwB,IAAItR;IAClC,MAAMuR,wBAAwBjd,2BAA2B;IAEzD,MAAMkd,4BAA4C;QAChD1X,MAAM;QACNsG,OAAO;QACPP;QACAD;QACAS,cAAciR,sBAAsBhR,MAAM;QAC1CC,YAAY+Q;QACZ,uFAAuF;QACvFpR,aAAa;QACbM,iBAAiB+Q;QACjB9Q,YAAYlK;QACZmK,QAAQnK;QACRoK,OAAOpK;QACPqK,MAAM,EAAE;QACRd;QACAe;IACF;IAEA,MAAM4Q,wBAAwB,IAAIzR;IAClC,MAAM0R,wBAAwBpd,2BAA2B;IACzD,MAAMqd,oBAAoBpd;IAE1B,MAAMqd,4BAA4C;QAChD9X,MAAM;QACNsG,OAAO;QACPP;QACAD;QACAS,cAAcoR,sBAAsBnR,MAAM;QAC1CC,YAAYkR;QACZ,uFAAuF;QACvFvR,aAAa;QACbM,iBAAiBkR;QACjBjR,YAAYlK;QACZmK,QAAQnK;QACRoK,OAAOpK;QACPqK,MAAM,EAAE;QACRd;QACAe;IACF;IAEA,MAAMgR,qBAAqB,MAAM1b,qBAAqBsI,GAAG,CACvD+S,2BACAnQ,eACAlG,MACAH,KACAqV;IAGF,MAAMyB,8BAA8B,MAAMhc,0BACxCwb,sBAAsBhR,MAAM,EAC5B,IACEnK,qBAAqBsI,GAAG,CACtB+S,2BACAzL,aAAaxG,sBAAsB,EACnCsS,oBACAzS,wBAAwBI,aAAa,EACrC;YACElB,SAAS,CAACF;gBACR,IAAIvH,uBAAuBuH,MAAM;oBAC/B,OAAOA,IAAIwF,MAAM;gBACnB;gBAEA,IACE0N,sBAAsBhR,MAAM,CAACuQ,OAAO,IACpCxc,4BAA4B+J,MAC5B;oBACA,OAAOA,IAAIwF,MAAM;gBACnB;gBAEA,OAAO1R,2BAA2BkM;YACpC;YACAkC,QAAQgR,sBAAsBhR,MAAM;QACtC,IAEJ;QACEgR,sBAAsBrQ,KAAK;IAC7B;IAGF,IAAI8Q,eAAe;IACnB,MAAMC,qBAAqBF,4BAA4BG,cAAc;IACrE,IAAI;QACF,MAAMd,YAAYnM,QAAQ,yBACvBmM,SAAS;QACZ,MAAMpb,0BACJ,IACEI,qBAAqBsI,GAAG,CACtBmT,2BACAT,yBACA,KAACpN;gBACCC,mBAAmBgO;gBACnB/N,gBAAgB,KAAO;gBACvB7E,yBAAyBA;gBACzB8E,4BAA4BA;gBAC5BC,gCAAgCA;gBAChC/L,OAAO4C,IAAI5C,KAAK;gBAElB;gBACEkI,QAAQmR,sBAAsBnR,MAAM;gBACpChC,SAAS,CAACF,KAAK8T;oBACb,IAAIrb,uBAAuBuH,MAAM;wBAC/BuT,kBAAkBQ,aAAa,CAACC,IAAI,CAAChU;wBAErC;oBACF;oBAEA,IACE/J,4BAA4B+J,QAC5BqT,sBAAsBnR,MAAM,CAACuQ,OAAO,EACpC;wBACA,IAAI,CAACkB,cAAc;4BACjB,+FAA+F;4BAC/F,wGAAwG;4BACxG,+BAA+B;4BAC/B7T,aAAagN,WAAW,GAAG;wBAC7B;wBAEA,MAAMmH,iBAAiBH,UAAUG,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtC5d,0BACE4K,OACAgT,gBACAV,mBACAJ,uBACAG;wBAEJ;wBACA;oBACF;oBAEA,OAAOxf,2BAA2BkM;gBACpC;YACF,IAEJ;YACEqT,sBAAsBxQ,KAAK;YAC3B+Q,mBAAmBM,eAAe;QACpC;IAEJ,EAAE,OAAOlU,KAAK;QACZ2T,eAAe;QACf,IACE1d,4BAA4B+J,QAC5BqT,sBAAsBnR,MAAM,CAACuQ,OAAO,EACpC;QACA,4FAA4F;QAC9F,OAAO;QACL,uEAAuE;QACvE,wEAAwE;QACxE,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACxE;IACF;IAEA,SAAS0B;QACP,IAAI;YACF7d,yBACE2K,OACAsS,mBACAJ,uBACAG;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;IACT;IAEA3S,gCAAkB,KAACwT;AACrB;AAaA;;CAEC,GACD,SAASC,+BAA+B/W,SAAoB;IAC1D,MAAM,EAAEgC,kBAAkB,EAAE,GAAGhC;IAC/B,IAAI,CAACgC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAeiL,kBACbzK,GAAoB,EACpBtB,GAAqB,EACrB3B,GAAqB,EACrBkI,QAAqC,EACrCzH,SAAoB,EACpBN,IAAgB;IAEhB,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMmQ,YAAY;IAElB,MAAM,EACJ9I,WAAW,EACXxJ,0BAA0B,EAC1B4G,YAAY,EACZxH,KAAK,EACLU,QAAQ,EACR8C,UAAU,EACX,GAAGZ;IAEJ,MAAM6E,aAAazM,cAAc+H,MAAMnC;IACvC,MAAM+M,eAAenK,WAAWmK,YAAY;IAC5C,4BAA4B;IAC5B,MAAM3G,0BAA0BxD,WAAWwD,uBAAuB;IAClE,MAAMrG,sBAAsB0C,UAAU1C,mBAAmB;IAEzD,MAAM,EAAEmL,0BAA0B,EAAE6H,wBAAwB,EAAE,GAC5DjZ;IACF,MAAM,EAAEqR,8BAA8B,EAAE6H,yBAAyB,EAAE,GACjElV,6BAA6BsB;IAE/B,MAAM6T,kBAAkB5V,kBACtBzE,YAAYsa,uBAAuB,IACnCtQ,WAAWiD,YAAY,CAACsN,mBAAmB;IAG7C,MAAMC,YACJxQ,WAAWyQ,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElD/S,GAAG,CAAC,CAAC8S;YAKO5Q;eALO;YAClB8Q,KAAK,GAAGlK,YAAY,OAAO,EAAEgK,WAAWnZ,oBACtC2H,KACA,QACC;YACH2R,SAAS,GAAE/Q,2CAAAA,WAAWgR,4BAA4B,qBAAvChR,wCAAyC,CAAC4Q,SAAS;YAC9DK,aAAajR,WAAWiR,WAAW;YACnCC,UAAU;YACV1U,OAAOA;QACT;;IAEJ,MAAM,CAAC6L,gBAAgB8I,gBAAgB,GAAGha,mBACxC6I,WAAWyQ,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9E7J,aACA5G,WAAWiR,WAAW,EACtBjR,WAAWgR,4BAA4B,EACvCvZ,oBAAoB2H,KAAK,OACzB5C,OACAwD,WAAWjD,IAAI;IAGjB,MAAMqU,4BAAwD,IAAIrI;IAClE,+EAA+E;IAC/E,MAAMsI,gBAAgB,CAAC,CAACrR,WAAWiD,YAAY,CAAC7G,iBAAiB;IACjE,SAASkV,qBAAqB9O,GAAkB;QAC9C,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMmS,+BAA+Bpb,kCACnC,CAAC,CAAC6J,WAAW2C,GAAG,EAChB,CAAC,CAAC3C,WAAWwR,UAAU,EACvBJ,2BACAC,eACAC;IAGF,SAASG,qBAAqBjP,GAAkB;QAC9C,OAAOxC,WAAWyC,6BAA6B,oBAAxCzC,WAAWyC,6BAA6B,MAAxCzC,YACLwC,KACAH,KACAP,mBAAmB1C,KAAK;IAE5B;IACA,MAAMsS,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2Bvb,uBAC/B,CAAC,CAAC4J,WAAW2C,GAAG,EAChB,CAAC,CAAC3C,WAAWwR,UAAU,EACvBJ,2BACAM,mBACAL,eACAI;IAGF,IAAIoF,6BAG8B;IAClC,MAAMC,oBAAoB,CAAC7X;QACzBqI,SAAS5L,OAAO,KAAK,CAAC;QACtB4L,SAAS5L,OAAO,CAACuD,KAAK,GAAG8B,IAAImF,SAAS,CAACjH;IACzC;IACA,MAAMwP,YAAY,CAACxP,MAAcxB;QAC/BsD,IAAI0N,SAAS,CAACxP,MAAMxB;QACpBqZ,kBAAkB7X;QAClB,OAAO8B;IACT;IACA,MAAM+Q,eAAe,CAAC7S,MAAcxB;QAClC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxBA,MAAMyJ,OAAO,CAAC,CAAC6P;gBACbhW,IAAI+Q,YAAY,CAAC7S,MAAM8X;YACzB;QACF,OAAO;YACLhW,IAAI+Q,YAAY,CAAC7S,MAAMxB;QACzB;QACAqZ,kBAAkB7X;IACpB;IAEA,MAAM+X,kBAAkB,CAACjS;YAEhB/E;eADP+E,UAAUpK,kBACV,SAAOqF,sCAAAA,WAAWiD,YAAY,CAACgU,UAAU,qBAAlCjX,oCAAoCkX,MAAM,MAAK,WAClDlX,WAAWiD,YAAY,CAACgU,UAAU,CAACC,MAAM,GACzCnS;;IAEN,IAAIR,iBAAwC;IAE5C,IAAI;QACF,IAAIvE,WAAWiD,YAAY,CAACC,SAAS,EAAE;YACrC,IAAIlD,WAAWiD,YAAY,CAAC7G,iBAAiB,EAAE;gBAC7C;;;;;;;;;;;;SAYC,GAED,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAMsY,mCAAmC,IAAItQ;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAMuQ,gCAAgC,IAAIvQ;gBAE1C,kFAAkF;gBAClF,yBAAyB;gBACzB,MAAME,cAAc,IAAI9J;gBAExB,iEAAiE;gBACjE,8DAA8D;gBAC9D,wEAAwE;gBACxE,6BAA6B;gBAC7B,MAAM0J,2BAA2BpJ;gBAEjC,MAAM8Z,8BAA+CrQ,iBAAiB;oBACpErG,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAckQ,8BAA8BjQ,MAAM;oBAClDC,YAAY+P;oBACZpQ;oBACAM,iBAAiB;oBACjBC,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMmb,uBAAuB,MAAM5c,qBAAqBsI,GAAG,CACzD+R,6BACAnP,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;gBAGrB,MAAMwY,6BAA6B7c,qBAAqBsI,GAAG,CACzD+R,6BACAzK,aAAaoL,SAAS,EACtB4B,sBACA3T,wBAAwBI,aAAa,EACrC;oBACElB,SAAS,CAACF;wBACR,MAAMwF,SAAS1R,2BAA2BkM;wBAE1C,IAAIwF,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAI0M,iCAAiChQ,MAAM,CAACuQ,OAAO,EAAE;4BACnD,mEAAmE;4BACnE,iEAAiE;4BACjE;wBACF,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;4BACA/a,0CAA0CoI,KAAK3C,UAAU4D,KAAK;wBAChE;oBACF;oBACA,iFAAiF;oBACjF,qCAAqC;oBACrC4T,YAAYrb;oBACZ,+EAA+E;oBAC/E,iFAAiF;oBACjF,iDAAiD;oBACjD0I,QAAQiQ,8BAA8BjQ,MAAM;gBAC9C;gBAGF,MAAMJ,YAAYc,UAAU;gBAC5BuP,8BAA8BtP,KAAK;gBACnCqP,iCAAiCrP,KAAK;gBAEtC,IAAIiS;gBACJ,IAAI;oBACFA,sBAAsB,MAAMvd,iCAC1Bqd;gBAEJ,EAAE,OAAO5U,KAAK;oBACZ,IACEmS,8BAA8BjQ,MAAM,CAACuQ,OAAO,IAC5CP,iCAAiChQ,MAAM,CAACuQ,OAAO,EAC/C;oBACA,4EAA4E;oBAC9E,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnF/a,0CAA0CoI,KAAK3C,UAAU4D,KAAK;oBAChE;gBACF;gBAEA,IAAI6T,qBAAqB;oBACvB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMhC,mBACJgC,oBAAoBC,QAAQ,IAC5B/T;oBAGF,MAAMqR,0BAA0B,IAAIzQ;oBACpC,MAAM0Q,8BAA8C;wBAClD5W,MAAM;wBACNsG,OAAO;wBACPP;wBACAD;wBACAS,cAAcoQ,wBAAwBnQ,MAAM;wBAC5CC,YAAYkQ;wBACZvQ,aAAa;wBACbM,iBAAiB;wBACjBC,YAAYlK;wBACZmK,QAAQnK;wBACRoK,OAAOpK;wBACPqK,MAAM;+BAAIhB,aAAagB,IAAI;yBAAC;wBAC5Bd;wBACAe,gBAAgBjJ;oBAClB;oBAEA,MAAMuZ,YAAYnM,QAAQ,yBACvBmM,SAAS;oBACZ,MAAMtb,mCACJ,IACEM,qBAAqBsI,GAAG,CACtBiS,6BACAS,yBACA,KAACpN;4BACCC,mBAAmBkP,oBAAoBE,iBAAiB;4BACxDnP,gBAAgBA;4BAChB7E,yBAAyBA;4BACzB8E,4BAA4BA;4BAC5BC,gCACEA;4BAEF/L,OAAOA;4BAET;4BACEkI,QAAQmQ,wBAAwBnQ,MAAM;4BACtChC,SAAS,CAACF;gCACR,MAAMwF,SAAS1R,2BAA2BkM;gCAE1C,IAAIwF,QAAQ;oCACV,OAAOA;gCACT;gCAEA,IAAI6M,wBAAwBnQ,MAAM,CAACuQ,OAAO,EAAE;gCAC1C,4EAA4E;gCAC9E,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;oCACA,8EAA8E;oCAC9E,mFAAmF;oCACnF/a,0CACEoI,KACA3C,UAAU4D,KAAK;gCAEnB;4BACF;4BACAwP,kBAAkB;gCAAC9B;6BAAgB;wBACrC,IAEJ;wBACE0D,wBAAwBxP,KAAK;oBAC/B,GACAoQ,KAAK,CAAC,CAACjT;wBACP,IACEmS,8BAA8BjQ,MAAM,CAACuQ,OAAO,IAC5Cxc,4BAA4B+J,MAC5B;wBACA,4EAA4E;wBAC9E,OAAO,IACLM,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnF/a,0CAA0CoI,KAAK3C,UAAU4D,KAAK;wBAChE;oBACF;gBACF;gBAEA,IAAIgU,kBAAkB;gBACtB,MAAM/B,wBAAwB,IAAItR;gBAClC,MAAMuR,wBAAwBjd,2BAC5BsH,WAAWgN,sBAAsB;gBAGnC,MAAM0K,4BAA6CnT,iBAAiB;oBAClErG,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAciR,sBAAsBhR,MAAM;oBAC1CC,YAAY+Q;oBACZ,uFAAuF;oBACvFpR,aAAa;oBACbM,iBAAiB+Q;oBACjB9Q,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,MAAM2b,yBAAyB,MAAMpd,qBAAqBsI,GAAG,CAC3D6U,2BACAjS,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;gBAErB,IAAIgZ,qBAAqB;gBACzB,MAAMhG,oBAAqBiF,6BACzB,MAAM9c,iCACJE,mCACE;oBACE,MAAM4d,kBAAkB,MAAMtd,qBAAqBsI,GAAG,CACpD,qBAAqB;oBACrB6U,2BACA,sBAAsB;oBACtBvN,aAAaoL,SAAS,EACtB,4CAA4C;oBAC5CoC,wBACAnU,wBAAwBI,aAAa,EACrC;wBACElB,SAAS,CAACF;4BACR,OAAO+O,6BAA6B/O;wBACtC;wBACAkC,QAAQgR,sBAAsBhR,MAAM;oBACtC;oBAEFkT,qBAAqB;oBACrB,OAAOC;gBACT,GACA;oBACE,IAAInC,sBAAsBhR,MAAM,CAACuQ,OAAO,EAAE;wBACxC,4EAA4E;wBAC5E,6EAA6E;wBAC7EwC,kBAAkB;wBAClB;oBACF;oBAEA,IAAIG,oBAAoB;wBACtB,kFAAkF;wBAClF,iCAAiC;wBACjCH,kBAAkB;oBACpB;oBACA/B,sBAAsBrQ,KAAK;gBAC7B;gBAIN,MAAMyQ,wBAAwBpd,2BAC5BsH,WAAWgN,sBAAsB;gBAEnC,MAAM6I,wBAAwB,IAAIzR;gBAClC,MAAM4R,4BAA4C;oBAChD9X,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAcoR,sBAAsBnR,MAAM;oBAC1CC,YAAYkR;oBACZ,oEAAoE;oBACpEvR,aAAa;oBACbM,iBAAiBkR;oBACjBjR,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,IAAI8b,kBAAkB;gBACtB,IAAI/B,oBAAoBpd;gBAExB,MAAM4c,YAAYnM,QAAQ,yBACvBmM,SAAS;gBACZ,IAAI,EAAEwC,OAAO,EAAE9Q,SAAS,EAAE,GAAG,MAAMhN,mCACjC,IACEM,qBAAqBsI,GAAG,CACtBmT,2BACAT,yBACA,KAACpN;wBACCC,mBAAmBwJ,kBAAkB4F,iBAAiB;wBACtDnP,gBAAgBA;wBAChB7E,yBAAyBA;wBACzB8E,4BAA4BA;wBAC5BC,gCAAgCA;wBAChC/L,OAAOA;wBAET;wBACEkI,QAAQmR,sBAAsBnR,MAAM;wBACpChC,SAAS,CAACF,KAAc8T;4BACtB,IACE7d,4BAA4B+J,QAC5BqT,sBAAsBnR,MAAM,CAACuQ,OAAO,EACpC;gCACA6C,kBAAkB;gCAElB,MAAMrB,iBAAqC,AACzCH,UACAG,cAAc;gCAChB,IAAI,OAAOA,mBAAmB,UAAU;oCACtC5d,0BACEgH,UAAU4D,KAAK,EACfgT,gBACAV,mBACAJ,uBACAG;gCAEJ;gCACA;4BACF;4BAEA,OAAOnE,yBAAyBnP,KAAK8T;wBACvC;wBACAxD,WAAW,CAACpX;4BACVA,QAAQwL,OAAO,CAAC,CAACzJ,OAAOF;gCACtBuU,aAAavU,KAAKE;4BACpB;wBACF;wBACAsV,kBAAkB/S,WAAWgT,qBAAqB;wBAClDC,kBAAkB;4BAAC9B;yBAAgB;oBACrC,IAEJ;oBACE0E,sBAAsBxQ,KAAK;gBAC7B;gBAGFvM,yBACE+G,UAAU4D,KAAK,EACfsS,mBACAJ,uBACAG;gBAGF,MAAMrD,wBAAwBpb,0BAA0B;oBACtDmZ;oBACAL;oBACAuC,sBAAsBhB;oBACtBiB,UAAU3S,WAAW2S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBAEA,MAAMhR,aAAa,MAAM7K,eAAeod,kBAAkB2F,QAAQ;gBAClEjQ,SAASjI,UAAU,GAAGA;gBACtBiI,SAAS0Q,WAAW,GAAG,MAAMC,mBAC3B5Y,YACAqY,2BACAvN,cACAnK,YACA7C;gBAGF,IAAIsa,mBAAmBK,iBAAiB;oBACtC,IAAI7Q,aAAa,MAAM;wBACrB,oBAAoB;wBACpBK,SAASL,SAAS,GAAG,MAAMnP,6BACzBmP,WACA9J,qBACA+G;oBAEJ,OAAO;wBACL,oBAAoB;wBACpBoD,SAASL,SAAS,GAAG,MAAMpP,6BACzBqM;oBAEJ;oBACA0N,kBAAkBiB,OAAO;oBACzB,OAAO;wBACL1F,iBAAiBiE;wBACjB5D,WAAWkE;wBACXxC,QAAQ,MAAM7a,yBAAyB0jB,SAAS;4BAC9CtF;4BACArC;wBACF;wBACArD,eAAehU,qBACb4c,uBACAG;wBAEF,0CAA0C;wBAC1CnH,qBAAqB+I,0BAA0B7S,UAAU;wBACzDgK,iBAAiB6I,0BAA0B5S,MAAM;wBACjD0J,gBAAgBwI,gBAAgBU,0BAA0B3S,KAAK;wBAC/DqJ,eAAesJ,0BAA0B1S,IAAI;oBAC/C;gBACF,OAAO;oBACL,cAAc;oBACd,IAAInF,UAAU0P,YAAY,EAAE;wBAC1B,MAAM,qBAEL,CAFK,IAAIpX,sBACR,qHADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAIqa,aAAauF;oBACjB,IAAI9Q,aAAa,MAAM;wBACrB,+FAA+F;wBAC/F,qGAAqG;wBACrG,MAAMsL,SAASnJ,QAAQ,yBACpBmJ,MAAM;wBAET,qEAAqE;wBACrE,4EAA4E;wBAC5E,MAAM2F,gBAAgB,IAAIC;wBAE1B,MAAMC,eAAe,MAAM7F,qBACzB,KAACpK;4BACCC,mBAAmB8P;4BACnB7P,gBAAgB,KAAO;4BACvB7E,yBAAyBA;4BACzB8E,4BAA4BA;4BAC5BC,gCAAgCA;4BAChC/L,OAAOA;4BAET6b,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACtR,aAC1B;4BACEvC,QAAQnM,2BAA2B;4BACnCmK,SAASiP;4BACTnV;wBACF;wBAGF,wGAAwG;wBACxGgW,aAAave,aAAa8jB,SAASK;oBACrC;oBAEA,OAAO;wBACLjL,iBAAiBiE;wBACjB5D,WAAWkE;wBACXxC,QAAQ,MAAM5a,wBAAwBke,YAAY;4BAChDI,mBAAmB1a,gCACjB0Z,kBAAkB4G,eAAe,IACjChc,OACAkT;4BAEF+C;4BACArC;wBACF;wBACArD,eAAehU,qBACb4c,uBACAG;wBAEF,0CAA0C;wBAC1CnH,qBAAqB+I,0BAA0B7S,UAAU;wBACzDgK,iBAAiB6I,0BAA0B5S,MAAM;wBACjD0J,gBAAgBwI,gBAAgBU,0BAA0B3S,KAAK;wBAC/DqJ,eAAesJ,0BAA0B1S,IAAI;oBAC/C;gBACF;YACF,OAAO;gBACL;;;;;;;;;;;;;;;;SAgBC,GAED,MAAMyT,QAAQ5Y,UAAU6Y,gBAAgB;gBACxC,IAAI,CAACD,OAAO;oBACV,MAAM,qBAEL,CAFK,IAAIlR,MACR,kEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAMmN,mCAAmC,IAAItQ;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAMuQ,gCAAgC,IAAIvQ;gBAE1C,MAAME,cAAc,IAAI9J;gBACxB,MAAM0J,2BAA2BpJ;gBAEjC,MAAM8Z,8BAA+CrQ,iBAAiB;oBACpErG,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAckQ,8BAA8BjQ,MAAM;oBAClDC,YAAY+P;oBACZpQ;oBACAM,iBAAiB;oBACjBC,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,MAAM6Y,0BAA0B,IAAIzQ;gBACpC,MAAM0Q,8BAA+CvQ,iBAAiB;oBACpErG,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAcoQ,wBAAwBnQ,MAAM;oBAC5CC,YAAYkQ;oBACZvQ;oBACAM,iBAAiB;oBACjBC,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAM+Y,yBAAyB,MAAMxa,qBAAqBsI,GAAG,CAC3D+R,6BACAnP,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;gBAGrB,IAAIoW;gBACJ,IAAI;oBACFA,sBAAsBza,qBAAqBsI,GAAG,CAC5C+R,6BACAzK,aAAaxG,sBAAsB,EACnCoR,wBACAvR,wBAAwBI,aAAa,EACrC;wBACElB,SAAS,CAACF;4BACR,MAAMwF,SAAS1R,2BAA2BkM;4BAE1C,IAAIwF,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IACE0M,iCAAiChQ,MAAM,CAACuQ,OAAO,IAC/CN,8BAA8BjQ,MAAM,CAACuQ,OAAO,EAC5C;gCACA,mEAAmE;gCACnE,iEAAiE;gCACjE;4BACF,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;gCACA/a,0CACEoI,KACA3C,UAAU4D,KAAK;4BAEnB;wBACF;wBACAiB,QAAQiQ,8BAA8BjQ,MAAM;oBAC9C;gBAEJ,EAAE,OAAOlC,KAAc;oBACrB,IACEkS,iCAAiChQ,MAAM,CAACuQ,OAAO,IAC/CN,8BAA8BjQ,MAAM,CAACuQ,OAAO,EAC5C;oBACA,4EAA4E;oBAC9E,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnF/a,0CAA0CoI,KAAK3C,UAAU4D,KAAK;oBAChE;gBACF;gBAEA,IAAIuR,qBAAqB;oBACvB,MAAM,CAACI,cAAcC,aAAa,GAAGL,oBAAoB1C,GAAG;oBAC5D0C,sBAAsB;oBACtB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMM,mBAAmBF,cAAc5R;oBAEvC,MAAM+R,YAAYnM,QAAQ,yBACvBmM,SAAS;oBACZ,MAAMC,6BAA6Bjb,qBAAqBsI,GAAG,CACzDiS,6BACAS,yBACA,KAACpN;wBACCC,mBAAmBiN;wBACnBhN,gBAAgBA;wBAChB7E,yBAAyBA;wBACzB8E,4BAA4BA;wBAC5BC,gCAAgCA;wBAChC/L,OAAOA;wBAET;wBACEkI,QAAQmQ,wBAAwBnQ,MAAM;wBACtChC,SAAS,CAACF;4BACR,MAAMwF,SAAS1R,2BAA2BkM;4BAE1C,IAAIwF,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IAAI6M,wBAAwBnQ,MAAM,CAACuQ,OAAO,EAAE;4BAC1C,4EAA4E;4BAC9E,OAAO,IACLnS,QAAQC,GAAG,CAACmS,gBAAgB,IAC5BpS,QAAQC,GAAG,CAACoS,sBAAsB,EAClC;gCACA,8EAA8E;gCAC9E,mFAAmF;gCACnF/a,0CACEoI,KACA3C,UAAU4D,KAAK;4BAEnB;wBACF;wBACAwP,kBAAkB;4BAAC9B;yBAAgB;oBACrC;oBAEFqE,2BAA2BC,KAAK,CAAC,CAACjT;wBAChC,IAAIqS,wBAAwBnQ,MAAM,CAACuQ,OAAO,EAAE;wBAC1C,2DAA2D;wBAC7D,OAAO;4BACL,uEAAuE;4BACvE,yCAAyC;4BACzC,IAAInS,QAAQC,GAAG,CAACoS,sBAAsB,EAAE;gCACtC/a,0CAA0CoI,KAAK3C,UAAU4D,KAAK;4BAChE;wBACF;oBACF;gBACF;gBAEA,MAAMa,YAAYc,UAAU;gBAC5B,8DAA8D;gBAC9D,gEAAgE;gBAChEyP,wBAAwBxP,KAAK;gBAC7BsP,8BAA8BtP,KAAK;gBACnCqP,iCAAiCrP,KAAK;gBAEtC,sEAAsE;gBACtE,kFAAkF;gBAElF,IAAIoS,kBAAkB;gBACtB,MAAM/B,wBAAwB,IAAItR;gBAClC,MAAMuR,wBAAwBjd,2BAC5BsH,WAAWgN,sBAAsB;gBAGnC,MAAM4I,4BAA6CrR,iBAAiB;oBAClErG,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAciR,sBAAsBhR,MAAM;oBAC1CC,YAAY+Q;oBACZ,uFAAuF;oBACvFpR,aAAa;oBACbM,iBAAiB+Q;oBACjB9Q,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,IAAI8b,kBAAkB;gBACtB,MAAMjC,wBAAwB,IAAIzR;gBAClC,MAAM0R,wBAAwBpd,2BAC5BsH,WAAWgN,sBAAsB;gBAEnC,MAAM+I,oBAAoBpd;gBAE1B,MAAMqd,4BAA6CzR,iBAAiB;oBAClErG,MAAM;oBACNsG,OAAO;oBACPP;oBACAD;oBACAS,cAAcoR,sBAAsBnR,MAAM;oBAC1CC,YAAYkR;oBACZ,uFAAuF;oBACvFvR,aAAa;oBACbM,iBAAiBkR;oBACjBjR,YAAYlK;oBACZmK,QAAQnK;oBACRoK,OAAOpK;oBACPqK,MAAM;2BAAIhB,aAAagB,IAAI;qBAAC;oBAC5Bd;oBACAe,gBAAgBjJ;gBAClB;gBAEA,MAAMia,qBAAqB,MAAM1b,qBAAqBsI,GAAG,CACvD+S,2BACAnQ,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;gBAGrB,MAAMsX,8BAA+BW,6BACnC,MAAM3c,0BACJwb,sBAAsBhR,MAAM,EAC5B,IACEnK,qBAAqBsI,GAAG,CACtB+S,2BACAzL,aAAaxG,sBAAsB,EACnCsS,oBACAzS,wBAAwBI,aAAa,EACrC;wBACElB,SAAS,CAACF;4BACR,IAAIkT,sBAAsBhR,MAAM,CAACuQ,OAAO,EAAE;gCACxCwC,kBAAkB;gCAClB,IAAIhf,4BAA4B+J,MAAM;oCACpC,OAAOA,IAAIwF,MAAM;gCACnB;gCACA,OAAO1R,2BAA2BkM;4BACpC;4BAEA,OAAO+O,6BAA6B/O;wBACtC;wBACAkC,QAAQgR,sBAAsBhR,MAAM;oBACtC,IAEJ;oBACEgR,sBAAsBrQ,KAAK;gBAC7B;gBAGJ,IAAImN;gBACJ,MAAM4D,qBAAqBF,4BAA4BG,cAAc;gBACrE,IAAI;oBACF,MAAMd,YAAYnM,QAAQ,yBACvBmM,SAAS;oBACZ,MAAMzF,SAAS,MAAM3V,0BACnB,IACEI,qBAAqBsI,GAAG,CACtBmT,2BACAT,yBACA,KAACpN;4BACCC,mBAAmBgO;4BACnB/N,gBAAgBA;4BAChB7E,yBAAyBA;4BACzB8E,4BAA4BA;4BAC5BC,gCACEA;4BAEF/L,OAAOA;4BAET;4BACEkI,QAAQmR,sBAAsBnR,MAAM;4BACpChC,SAAS,CAACF,KAAc8T;gCACtB,IACE7d,4BAA4B+J,QAC5BqT,sBAAsBnR,MAAM,CAACuQ,OAAO,EACpC;oCACA6C,kBAAkB;oCAElB,MAAMrB,iBAAqC,AACzCH,UACAG,cAAc;oCAChB,IAAI,OAAOA,mBAAmB,UAAU;wCACtC5d,0BACEgH,UAAU4D,KAAK,EACfgT,gBACAV,mBACAJ,uBACAG;oCAEJ;oCACA;gCACF;gCAEA,OAAOnE,yBAAyBnP,KAAK8T;4BACvC;4BACArD,kBAAkB;gCAAC9B;6BAAgB;wBACrC,IAEJ;wBACE0E,sBAAsBxQ,KAAK;wBAC3B+Q,mBAAmBM,eAAe;oBACpC;oBAEFlE,aAAa1C,OAAOiI,OAAO;gBAC7B,EAAE,OAAOvV,KAAK;oBACZ,IACE/J,4BAA4B+J,QAC5BqT,sBAAsBnR,MAAM,CAACuQ,OAAO,EACpC;oBACA,4FAA4F;oBAC9F,OAAO;wBACL,oDAAoD;wBACpD,MAAMzS;oBACR;gBACF;gBAEA1J,yBACE+G,UAAU4D,KAAK,EACfsS,mBACAJ,uBACAG;gBAGF,IAAI2B,mBAAmBK,iBAAiB;oBACtC,MAAMa,gBAAgBlB,kBAClB7e,sBAAsB+c,yBACtB/c,sBAAsBkd;oBAC1B,IAAI6C,eAAe;wBACjB,MAAM,qBAEL,CAFK,IAAI/e,mBACR,CAAC,OAAO,EAAEiG,UAAU4D,KAAK,CAAC,oDAAoD,EAAEkV,cAAc,4EAA4E,CAAC,GADvK,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAI/e,mBACR,CAAC,OAAO,EAAEiG,UAAU4D,KAAK,CAAC,0JAA0J,CAAC,GADjL,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,MAAMpE,aAAa,MAAM7K,eACvB0hB,4BAA4BqB,QAAQ;gBAEtCjQ,SAASjI,UAAU,GAAGA;gBACtBiI,SAAS0Q,WAAW,GAAG,MAAMC,mBAC3B5Y,YACA2W,2BACA7L,cACAnK,YACA7C;gBAGF,MAAMsV,wBAAwBpb,0BAA0B;oBACtDmZ;oBACAL;oBACAuC,sBAAsBhB;oBACtBiB,UAAU3S,WAAW2S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBACA,MAAMgD,qBAAqBrT,WAAW2C,GAAG;gBACzC,OAAO;oBACLwK,iBAAiBiE;oBACjB5D,WAAWkE;oBACXxC,QAAQ,MAAM9a,mBAAmBoe,YAAa;wBAC5CI,mBAAmB1a,gCACjBge,4BAA4BqB,QAAQ,IACpC/a,OACAkT;wBAEF7N,oBAAoB;wBACpB4Q;wBACArC;wBACAiD;oBACF;oBACAtG,eAAehU,qBACb4c,uBACAG;oBAEF,0CAA0C;oBAC1CnH,qBAAqBiH,0BAA0B/Q,UAAU;oBACzDgK,iBAAiB+G,0BAA0B9Q,MAAM;oBACjD0J,gBAAgBwI,gBAAgBpB,0BAA0B7Q,KAAK;oBAC/DqJ,eAAewH,0BAA0B5Q,IAAI;gBAC/C;YACF;QACF,OAAO,IAAIhF,WAAWiD,YAAY,CAAC7G,iBAAiB,EAAE;YACpD,uEAAuE;YACvE,IAAIwI,kBAAkBlM,2BACpBsH,WAAWgN,sBAAsB;YAGnC,MAAM9I,2BAA2BpJ;YACjC,MAAM8d,4BAA6CrU,iBAAiB;gBAClErG,MAAM;gBACNsG,OAAO;gBACPP;gBACAD;gBACAY;gBACAC,YAAYlK;gBACZmK,QAAQnK;gBACRoK,OAAOpK;gBACPqK,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5Bd;YACF;YACA,MAAMtB,aAAa,MAAMrI,qBAAqBsI,GAAG,CAC/C+V,2BACAnT,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAMgT,oBAAqBiF,6BACzB,MAAM7c,2CACJO,qBAAqBsI,GAAG,CACtB+V,2BACAzO,aAAaxG,sBAAsB,EACnC,4CAA4C;YAC5Cf,YACAY,wBAAwBI,aAAa,EACrC;gBACElB,SAAS6O;YACX;YAIN,MAAMsH,oBAAoC;gBACxC3a,MAAM;gBACNsG,OAAO;gBACPP;gBACAD;gBACAY;gBACAC,YAAYlK;gBACZmK,QAAQnK;gBACRoK,OAAOpK;gBACPqK,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;gBAC5Bd;YACF;YACA,MAAMqR,YAAYnM,QAAQ,yBACvBmM,SAAS;YACZ,MAAM,EAAEwC,OAAO,EAAE9Q,SAAS,EAAE,GAAG,MAAM1M,qBAAqBsI,GAAG,CAC3DgW,mBACAtD,yBACA,KAACpN;gBACCC,mBAAmBwJ,kBAAkB4F,iBAAiB;gBACtDnP,gBAAgBA;gBAChB7E,yBAAyBA;gBACzB8E,4BAA4BA;gBAC5BC,gCAAgCA;gBAChC/L,OAAOA;gBAET;gBACEkG,SAASiP;gBACTmB,WAAW,CAACpX;oBACVA,QAAQwL,OAAO,CAAC,CAACzJ,OAAOF;wBACtBuU,aAAavU,KAAKE;oBACpB;gBACF;gBACAsV,kBAAkB/S,WAAWgT,qBAAqB;gBAClDC,kBAAkB;oBAAC9B;iBAAgB;YACrC;YAEF,MAAMsB,wBAAwBpb,0BAA0B;gBACtDmZ;gBACAL;gBACAuC,sBAAsBhB;gBACtBiB,UAAU3S,WAAW2S,QAAQ;gBAC7BtC,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMhR,aAAa,MAAM7K,eAAeod,kBAAkB2F,QAAQ;YAElE,IAAIX,+BAA+B/W,YAAY;gBAC7CyH,SAASjI,UAAU,GAAGA;gBACtBiI,SAAS0Q,WAAW,GAAG,MAAMC,mBAC3B5Y,YACAwZ,mBACA1O,cACAnK,YACA7C;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI7E,oBAAoBsM,gBAAgBkU,eAAe,GAAG;gBACxD,IAAI7R,aAAa,MAAM;oBACrB,qBAAqB;oBACrBK,SAASL,SAAS,GAAG,MAAMnP,6BACzBmP,WACA9J,qBACA+G;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBoD,SAASL,SAAS,GAAG,MAAMpP,6BACzBqM;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtD0N,kBAAkBiB,OAAO;gBACzB,OAAO;oBACL1F,iBAAiBiE;oBACjB5D,WAAWkE;oBACXxC,QAAQ,MAAM7a,yBAAyB0jB,SAAS;wBAC9CtF;wBACArC;oBACF;oBACArD,eAAenI,gBAAgBkU,eAAe;oBAC9C,0CAA0C;oBAC1CnK,qBAAqBiK,0BAA0B/T,UAAU;oBACzDgK,iBAAiB+J,0BAA0B9T,MAAM;oBACjD0J,gBAAgBwI,gBAAgB4B,0BAA0B7T,KAAK;oBAC/DqJ,eAAewK,0BAA0B5T,IAAI;gBAC/C;YACF,OAAO,IAAI7H,uBAAuBA,oBAAoBiQ,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/B9F,SAASL,SAAS,GAAG,MAAMpP,6BACzBqM;gBAGF,OAAO;oBACLiJ,iBAAiBiE;oBACjB5D,WAAWkE;oBACXxC,QAAQ,MAAM7a,yBAAyB0jB,SAAS;wBAC9CtF;wBACArC;oBACF;oBACArD,eAAenI,gBAAgBkU,eAAe;oBAC9C,0CAA0C;oBAC1CnK,qBAAqBiK,0BAA0B/T,UAAU;oBACzDgK,iBAAiB+J,0BAA0B9T,MAAM;oBACjD0J,gBAAgBwI,gBAAgB4B,0BAA0B7T,KAAK;oBAC/DqJ,eAAewK,0BAA0B5T,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAInF,UAAU0P,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAIpX,sBACR,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIqa,aAAauF;gBACjB,IAAI9Q,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMsL,SAASnJ,QAAQ,yBACpBmJ,MAAM;oBAET,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAM2F,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAM7F,qBACzB,KAACpK;wBACCC,mBAAmB8P;wBACnB7P,gBAAgB,KAAO;wBACvB7E,yBAAyBA;wBACzB8E,4BAA4BA;wBAC5BC,gCAAgCA;wBAChC/L,OAAOA;wBAET6b,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACtR,aAC1B;wBACEvC,QAAQnM,2BAA2B;wBACnCmK,SAASiP;wBACTnV;oBACF;oBAGF,wGAAwG;oBACxGgW,aAAave,aAAa8jB,SAASK;gBACrC;gBAEA,OAAO;oBACLjL,iBAAiBiE;oBACjB5D,WAAWkE;oBACXxC,QAAQ,MAAM5a,wBAAwBke,YAAY;wBAChDI,mBAAmB1a,gCACjB0Z,kBAAkB4G,eAAe,IACjChc,OACAkT;wBAEF+C;wBACArC;oBACF;oBACArD,eAAenI,gBAAgBkU,eAAe;oBAC9C,0CAA0C;oBAC1CnK,qBAAqBiK,0BAA0B/T,UAAU;oBACzDgK,iBAAiB+J,0BAA0B9T,MAAM;oBACjD0J,gBAAgBwI,gBAAgB4B,0BAA0B7T,KAAK;oBAC/DqJ,eAAewK,0BAA0B5T,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM+T,uBAAwCxU,iBAAiB;gBAC7DrG,MAAM;gBACNsG,OAAO;gBACPP;gBACAD;gBACAa,YAAYlK;gBACZmK,QAAQnK;gBACRoK,OAAOpK;gBACPqK,MAAM;uBAAIhB,aAAagB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMpC,aAAa,MAAMrI,qBAAqBsI,GAAG,CAC/CkW,sBACAtT,eACAlG,MACAH,KACA2B,IAAInC,UAAU,KAAK;YAErB,MAAMgT,oBAAqBiF,6BACzB,MAAM7c,2CACJO,qBAAqBsI,GAAG,CACtBkW,sBACA5O,aAAaxG,sBAAsB,EACnCf,YACAY,wBAAwBI,aAAa,EACrC;gBACElB,SAAS6O;YACX;YAIN,MAAM5N,yBAAyByF,QAAQ,yBACpCzF,sBAAsB;YAEzB,MAAM6O,aAAa,MAAMjY,qBAAqBsI,GAAG,CAC/CkW,sBACApV,sCACA,KAACwE;gBACCC,mBAAmBwJ,kBAAkB4F,iBAAiB;gBACtDnP,gBAAgBA;gBAChB7E,yBAAyBA;gBACzB8E,4BAA4BA;gBAC5BC,gCAAgCA;gBAChC/L,OAAOA;gBAET;gBACEkG,SAASiP;gBACTnV;gBACAyW,kBAAkB;oBAAC9B;iBAAgB;YACrC;YAGF,IAAIyF,+BAA+B/W,YAAY;gBAC7C,MAAMR,aAAa,MAAM7K,eAAeod,kBAAkB2F,QAAQ;gBAClEjQ,SAASjI,UAAU,GAAGA;gBACtBiI,SAAS0Q,WAAW,GAAG,MAAMC,mBAC3B5Y,YACA0Z,sBACA5O,cACAnK,YACA7C;YAEJ;YAEA,MAAMsV,wBAAwBpb,0BAA0B;gBACtDmZ;gBACAL;gBACAuC,sBAAsBhB;gBACtBiB,UAAU3S,WAAW2S,QAAQ;gBAC7BtC,iBAAiBA;YACnB;YACA,OAAO;gBACLlD,iBAAiBiE;gBACjB5D,WAAWkE;gBACXxC,QAAQ,MAAM9a,mBAAmBoe,YAAY;oBAC3CI,mBAAmB1a,gCACjB0Z,kBAAkB4G,eAAe,IACjChc,OACAkT;oBAEF7N,oBAAoB;oBACpB4Q;oBACArC;gBACF;gBACA,0CAA0C;gBAC1CzB,qBAAqBoK,qBAAqBlU,UAAU;gBACpDgK,iBAAiBkK,qBAAqBjU,MAAM;gBAC5C0J,gBAAgBwI,gBAAgB+B,qBAAqBhU,KAAK;gBAC1DqJ,eAAe2K,qBAAqB/T,IAAI;YAC1C;QACF;IACF,EAAE,OAAOxC,KAAK;QACZ,IACEpK,wBAAwBoK,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIsF,OAAO,KAAK,YACvBtF,IAAIsF,OAAO,CAAC1B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAM5D;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAIxK,qBAAqBwK,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM8Q,qBAAqBxc,oBAAoB0L;QAC/C,IAAI8Q,oBAAoB;YACtB,MAAMpL,QAAQ7P,4BAA4BmK;YAC1CxL,MACE,GAAGwL,IAAI+Q,MAAM,CAAC,mDAAmD,EAAErW,SAAS,kFAAkF,EAAEgL,OAAO;YAGzK,MAAM1F;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIqU,+BAA+B,MAAM;YACvC,MAAMrU;QACR;QAEA,IAAIsD;QAEJ,IAAIrQ,0BAA0B+M,MAAM;YAClCzB,IAAInC,UAAU,GAAGpJ,4BAA4BgN;YAC7CsD,YAAYvQ,mCAAmCwL,IAAInC,UAAU;QAC/D,OAAO,IAAIhJ,gBAAgB4M,MAAM;YAC/BsD,YAAY;YACZ/E,IAAInC,UAAU,GAAGjJ,+BAA+B6M;YAEhD,MAAMgR,cAAcpc,cAClB1B,wBAAwB8M,MACxBxC,WAAW2S,QAAQ;YAGrBlE,UAAU,YAAY+E;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9BvS,IAAInC,UAAU,GAAG;QACnB;QAEA,MAAM,CAACgV,qBAAqBC,qBAAqB,GAAG1c,mBAClD6I,WAAWyQ,aAAa,EACxB7J,aACA5G,WAAWiR,WAAW,EACtBjR,WAAWgR,4BAA4B,EACvCvZ,oBAAoB2H,KAAK,QACzB5C,OACA;QAGF,MAAMuc,uBAAwCxU,iBAAiB;YAC7DrG,MAAM;YACNsG,OAAO;YACPP;YACAD,cAAcA;YACda,YACE,QAAON,kCAAAA,eAAgBM,UAAU,MAAK,cAClCN,eAAeM,UAAU,GACzBlK;YACNmK,QACE,QAAOP,kCAAAA,eAAgBO,MAAM,MAAK,cAC9BP,eAAeO,MAAM,GACrBnK;YACNoK,OACE,QAAOR,kCAAAA,eAAgBQ,KAAK,MAAK,cAC7BR,eAAeQ,KAAK,GACpBpK;YACNqK,MAAM;mBAAKT,CAAAA,kCAAAA,eAAgBS,IAAI,KAAIhB,aAAagB,IAAI;aAAE;QACxD;QACA,MAAM8O,kBAAkB,MAAMvZ,qBAAqBsI,GAAG,CACpDkW,sBACA3R,oBACA7H,MACAH,KACAgS,0BAA0B1T,GAAG,CAAC,AAAC8E,IAAYwF,MAAM,IAAIhM,YAAYwG,KACjEsD;QAGF,MAAMiO,oBAAoBxZ,qBAAqBsI,GAAG,CAChDkW,sBACA5O,aAAaxG,sBAAsB,EACnCmQ,iBACAtQ,wBAAwBI,aAAa,EACrC;YACElB,SAAS6O;QACX;QAGF,IAAI;YACF,MAAMyC,aAAa,MAAM9f,0BAA0B;gBACjD+f,gBAAgB7K,QAAQ;gBACxB8K,uBACE,KAAC1K;oBACCpB,mBAAmB2L;oBACnBxL,gCAAgCA;oBAChCD,4BAA4BA;oBAC5BD,gBAAgBuL;oBAChBpQ,yBAAyBA;oBACzBhH,OAAOA;;gBAGX2X,eAAe;oBACb3X;oBACA,wCAAwC;oBACxCyW,kBAAkB;wBAACY;qBAAqB;oBACxCnE;gBACF;YACF;YAEA,IAAIkH,+BAA+B/W,YAAY;gBAC7C,MAAMR,aAAa,MAAM7K,eACvBqiB,2BAA2BU,QAAQ;gBAErCjQ,SAASjI,UAAU,GAAGA;gBACtBiI,SAAS0Q,WAAW,GAAG,MAAMC,mBAC3B5Y,YACA0Z,sBACA5O,cACAnK,YACA7C;YAEJ;YAEA,MAAMkW,qBAAqBrT,WAAW2C,GAAG;YAEzC,oEAAoE;YACpE,gEAAgE;YAChE,MAAMqW,eACJnC,sCAAsChd,8BAClCgd,2BAA2BU,QAAQ,KACnCV,2BAA2B2B,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9BrL,iBAAiBiE;gBACjB5D,WAAWkE;gBACXxC,QAAQ,MAAM9a,mBAAmB4f,YAAY;oBAC3CpB,mBAAmB1a,gCACjB8gB,cACAxc,OACAkT;oBAEF7N,oBAAoB;oBACpB4Q,uBAAuBpb,0BAA0B;wBAC/CmZ;wBACAL;wBACAuC,sBAAsB,EAAE;wBACxBC,UAAU3S,WAAW2S,QAAQ;wBAC7BtC,iBAAiBA;oBACnB;oBACAD;oBACAiD;gBACF;gBACAtG,eAAe;gBACf4B,qBACEpK,mBAAmB,OAAOA,eAAeM,UAAU,GAAGlK;gBACxDkU,iBACEtK,mBAAmB,OAAOA,eAAeO,MAAM,GAAGnK;gBACpD6T,gBAAgBwI,gBACdzS,mBAAmB,OAAOA,eAAeQ,KAAK,GAAGpK;gBAEnDyT,eAAe7J,mBAAmB,OAAOA,eAAeS,IAAI,GAAG;YACjE;QACF,EAAE,OAAOoP,UAAe;YACtB,IACEtR,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBvN,0BAA0B2e,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BjL,QAAQ;gBACViL;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAM6E,gBAAuC,IAAIhY;AACjD,MAAMiY,iBAA+C,EAAE;AAEvD,SAASnO,kBAAkBoO,IAAsB;IAC/CF,cAAcG,GAAG,CAACD;IAClBA,KAAKpL,OAAO,CAAC;QACX,IAAIkL,cAAcvb,GAAG,CAACyb,OAAO;YAC3BF,cAAcI,MAAM,CAACF;YACrB,IAAIF,cAAc7L,IAAI,KAAK,GAAG;gBAC5B,uEAAuE;gBACvE,IAAK,IAAIrP,IAAI,GAAGA,IAAImb,eAAezL,MAAM,EAAE1P,IAAK;oBAC9Cmb,cAAc,CAACnb,EAAE;gBACnB;gBACAmb,eAAezL,MAAM,GAAG;YAC1B;QACF;IACF;AACF;AAEA,OAAO,eAAe6H,mBACpB0D,YAAwC,EACxCxV,uBAA8D;IAE9D,MAAM,EAAE8V,wBAAwB,EAAE,GAChC,6DAA6D;IAC7DlQ,QAAQ;IAEV,IAAI;QACFkQ,yBAAyBN,cAAc;YACrCO,wBAAwB;gBACtBC,eAAehW,wBAAwBgW,aAAa;gBACpDC,WAAWjW,wBAAwBkW,gBAAgB;gBACnDzN,iBAAiB;YACnB;QACF;IACF,EAAE,OAAM;IACN,8DAA8D;IAC9D,gEAAgE;IAChE,oCAAoC;IACtC;IAEA,0EAA0E;IAC1E,2EAA2E;IAC3ElB,kBAAkBzQ;IAClB,OAAO,IAAIia,QAAQ,CAACoF;QAClBT,eAAe1C,IAAI,CAACmD;IACtB;AACF;AAEA,MAAMpT,uBAAuB,OAC3BhH,MACAH;IAEA,MAAM,EACJwa,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGhf,gBAAgB0E;IAEpB,IAAI+G;IACJ,IAAIuT,mBAAmB;QACrB,MAAM,GAAGC,OAAO,GAAG,MAAMlf,gCAAgC;YACvDwE;YACA2a,UAAUF,iBAAiB,CAAC,EAAE;YAC9BG,cAAcH,iBAAiB,CAAC,EAAE;YAClC7Y,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAqF,oBAAoBwT;IACtB;IAEA,OAAOxT;AACT;AAEA,eAAe2R,mBACbgC,kBAA0B,EAC1B1V,cAA8B,EAC9B4F,YAA2B,EAC3BnK,UAAsB,EACtB7C,mBAA+C;IAE/C,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMqG,0BAA0BxD,WAAWwD,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnCxD,WAAWiD,YAAY,CAACiX,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgBrX,QAAQC,GAAG,CAACmI,YAAY,KAAK;IACnD,MAAMqO,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWU,gBACP3W,wBAAwB4W,oBAAoB,GAC5C5W,wBAAwB6W,gBAAgB;QAC5CpO,iBAAiB;IACnB;IAEA,8EAA8E;IAC9E,0EAA0E;IAC1E,2EAA2E;IAC3E,sBAAsB;IACtB,EAAE;IACF,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,2EAA2E;IAC3E,6EAA6E;IAC7E,uEAAuE;IACvE,2EAA2E;IAC3E,6EAA6E;IAC7E,kBAAkB;IAClB,MAAMqO,0BACJta,WAAWiD,YAAY,CAAC7G,iBAAiB,KAAK,QAAQ,iBAAiB;IACvE,CAAC4D,WAAWiD,YAAY,CAACC,SAAS,CAAC,wBAAwB;;IAE7D,MAAMqX,YAAYhW,eAAeQ,KAAK;IACtC,OAAO,MAAMoF,aAAa8N,kBAAkB,CAC1CqC,yBACAL,oBACAM,WACA/W,wBAAwBI,aAAa,EACrC2V,wBACApc;AAEJ"}